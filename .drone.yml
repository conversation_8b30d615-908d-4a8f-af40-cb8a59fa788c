kind: pipeline
type: docker
name: build

workspace:
  base: /drone
  path: /drone/src

steps:
  - name: build image
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker build --rm -t mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} . --build-arg GIT_TAG=${DRONE_TAG##v}

volumes:
  - name: dockersock
    host:
      path: /var/run/docker.sock

trigger:
  event:
    - tag
