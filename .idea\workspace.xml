<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="a3beb348-d9aa-4db4-a6f4-3961f0268e53" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.env.build" beforeDir="false" afterPath="$PROJECT_DIR$/.env.build" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.env.production" beforeDir="false" afterPath="$PROJECT_DIR$/.env.production" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.env.sandbox" beforeDir="false" afterPath="$PROJECT_DIR$/.env.sandbox" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/example/nuxt.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/example/nuxt.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/components/internal/order/state/incart/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/lib/components/internal/order/state/incart/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2dXzIbaVgWD0nQTpYUoeXZg53r0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "feat/payment-wallet",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a3beb348-d9aa-4db4-a6f4-3961f0268e53" name="Changes" comment="" />
      <created>1710169133462</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1710169133462</updated>
      <workItem from="1710169137386" duration="302000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/middleware.js" />
      </list>
    </option>
  </component>
</project>