# Nuxt Module Checkout

A module that can handle sophisticated, multi-attributes service booking

## Features

- [ ] Supported use cases including for user role - [mobile-wellcare-vn](https://github.com/wellcare/mobile-wellcare-vn), as well as agent role - [dudu-wellcare-vn](https://github.com/dudu-wellcare-vn)
- [ ] Able to use
  - [x] multiple step mode,
  - [ ] single step mode or
  - [ ] chat mode
- [ ] Component [w-product-step](./docs/w-product-step.md)
- [ ] Component [w-stepper](./docs/w-stepper.md)
- [ ] Component []

# Entities / Key Concepts

- product: là sản phẩm, hoặc dịch vụ được đặt hàng.
- order-item: yêu cầu mua một sản phẩm dịch vụ (product), theo một mức giá (price) số lượng nhất định (quantity)
- order: tập hợp các order-item, kèm theo các thông khác như ngườ<PERSON> mua, thu<PERSON>, đ<PERSON><PERSON> chỉ, voucher <PERSON><PERSON> đãi, h<PERSON>nh thức thanh toán và các yêu cầu khác của người dùng.
- workflow: product của Wellcare cần thu thập các thông tin khi đặt mua, các thông tin này được thu thập từ một workflow gồm các step nhỏ để người dùng dễ nhập thông tin hơn. Khi người dùng bắt đầu vào checkout, module sẽ load workflow tương ứng của sản phẩm để render các step cho người nhập.
- Với các sản phẩm thanh toán bằng chooseCard (nằm trong incart/index.vue),khi nhấn nút payment thì sẽ gửi sự kiện onClick = startPaymentProcess khiến cho quá trình thanh toán xảy ra. Lúc này đơn hàng sẽ chuyển trạng thái (state) từ incart sang place cho nên trong component order-page, biến state cũng sẽ thay đổi theo chuyển từ incart --> place và dẫn đến component place/index.vue --> trong component này có thanh điều hướng router đến payment/order/orderId/success

```mermaid
erDiagram
    OrderItem ||..||  Product: has
    Product ||..||  Workflow: has
    Order ||--|{ OrderItem: includes
```

## How to use in a app

### Prerequisities

This module require the following module, make sure they are installed in your app

- @nuxt/composition-api
- cookie-universal-nuxt

### Install

install it

```bash
$ yarn add @wellcare/nuxt-module-checkout --save
```

add it in your nuxt-config buildModule sections

```javascript
  buildModules: [
    ['@wellcare/nuxt-module-checkout', {prefix: 'w', level: 1}]
  ],
```

create environment file, copy values from `.env.sandbox` or `.env.production`

### How to add new checkout product

- [docs](./docs/add-new.md)

## Contribution Guides

### Folder structure

- [/docs](./docs): neccessary documentations for app maintanance, preferable mermaid diagrams.
- [/example](./example): an example app that should test different components and functions.
- [/lib](./lib): contain all codes that will be built to dependency package.
- [/lib/global/]: expose components with prefix, example `w-product-step`
- [/lib/global/stepper]: component to handle checkout steps navigation.
- [/lib/static/workflow](./lib/static/workflow): definition json for different workflow

### Developing new checkout flow

Please follow [new-flow-guide](./docs/new-flow.md) to delcare new flow and its related forms/inputs.

### Running app in local

run example from your local environment

```bash
#install
yarn
# run
yarn dev
```

## License

[UNLICENSED](./license.md)

Copyright (c) mHealth Technologies ([@wellcare](https://wellcare.vn))

## Links:

- Guide to wrap vuetify components: https://dev.to/kouts/create-wrapper-components-for-vuetify-components-2aah
- Tips for lazy load to improve performance: https://github.com/nuxt/nuxt.js/issues/7698
