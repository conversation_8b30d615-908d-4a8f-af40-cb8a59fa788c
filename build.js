const fs = require('fs')
const path = require('path')

function copy(src, dest, condition = { extension: undefined }) {
  const stat = fs.statSync(src)
  if (stat.isDirectory()) {
    const files = fs.readdirSync(src)
    files.map((file) => {
      const srcFilePath = path.join(src, file)
      const destFilePath = path.join(dest, file)
      copy(srcFilePath, destFilePath, condition)
    })
  } else if (
    !condition.extension ||
    (condition.extension && src.includes(condition.extension))
  ) {
    const dirPath = path.dirname(dest)
    if (fs.existsSync(dirPath)) {
      fs.copyFileSync(src, dest)
    } else {
      fs.mkdirSync(dirPath, { recursive: true })
      fs.copyFileSync(src, dest)
    }
  }
}

copy(path.resolve(__dirname, 'dist/lib'), path.resolve(__dirname, 'dist/'))

fs.rmSync(path.resolve(__dirname, 'dist/lib'), { recursive: true, force: true })
// fs.unlinkSync(path.resolve(__dirname, 'dist/middleware.js'))

copy(
  path.resolve(__dirname, 'lib/pages'),
  path.resolve(__dirname, 'dist/pages'),
  { extension: '.vue' }
)

copy(
  path.resolve(__dirname, 'lib/layouts'),
  path.resolve(__dirname, 'dist/layouts'),
  { extension: '.vue' }
)

copy(
  path.resolve(__dirname, 'lib/components'),
  path.resolve(__dirname, 'dist/components'),
  { extension: '.vue' }
)
copy(
  path.resolve(__dirname, 'lib/static'),
  path.resolve(__dirname, 'dist/static')
)

// write package.json
const packageJson = require('./package.json')
delete packageJson.devDependencies
delete packageJson.scripts
packageJson.version = process.env.GIT_TAG || packageJson.version
fs.writeFileSync(
  path.resolve(__dirname, 'dist/package.json'),
  JSON.stringify(packageJson)
)
