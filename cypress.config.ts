import fs from 'fs'
import { defineConfig } from 'cypress'
require('dotenv').config()
const glob = require('glob')
const autoRecord = require('cypress-autorecord/plugin')

export default defineConfig({
  env: {
    wellcareUsername: process.env.WELLCARE_USERNAME,
    wellcarePassword: process.env.WELLCARE_PASSWORD
  },

  e2e: {
    baseUrl: 'http://localhost:8080',
    setupNodeEvents(on, config) {
      // implement node event listeners here
      require('cypress-fail-fast/plugin')(on, config)
      ;(config as any).autorecord = {
        interceptPattern: 'https://api.mhealthvn.com/*'
      }
      autoRecord(on, config, fs)
      on('task', {
        glob({ path, option }) {
          return glob.sync(path, option)
        }
      })
      return config
    },
    specPattern: ['tests/e2e/*.cy.{js,jsx,ts,tsx}'],
    supportFile: 'tests/cypress/support/e2e.{js,jsx,ts,tsx}',
    experimentalSessionAndOrigin: true
  },

  videosFolder: 'tests/cypress/videos',
  supportFolder: 'tests/cypress/support',
  fixturesFolder: 'tests/cypress/fixtures',
  downloadsFolder: 'tests/cypress/downloads',
  screenshotsFolder: 'tests/cypress/screenshots',

  component: {
    devServer: {
      framework: 'nuxt',
      bundler: 'webpack'
    }
  }
})
