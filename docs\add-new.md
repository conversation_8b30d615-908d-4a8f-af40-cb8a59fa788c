## Add new checkout-product

### In folder repositories, create data workflow
- Flow interface: [here](../lib/repositories/workflow/flow.interface.ts)
### Create component for product
- Consider each step in product is a form, conference docs for form here [link](./form.md)
### After create component, add to products-group
- [link](../lib/components/internal/shared/products-group.vue)

## Good luck and happy coding !!!
```
    __
___( o)>
\ <_. )
 `---'   from duccanhole with love
```