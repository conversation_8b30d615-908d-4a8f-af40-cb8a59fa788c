# Form Component Development Guideline

A form must and use [vuelidate](https://vuelidate-next.netlify.app/) to validate data.

Moving to next step can be done by either way:

#### Option 1. on `w-stepper` component:

- w-stepper component will emit event `change`, with this, `w-product-step` should submit and validate the form component by calling child function submit() and v$.$validate()
- If not validated, stepper changes shall be reverted and currentStep will not be changed.

#### Option 2. On the form component:

- form component will emit event `submit` on successful input

## props

The product-step component will pass the following props to a form:
| field | type | description |
| ---------- | ------ | ----------------------------------- |
| order | object | |
| order-item | object | |
| provider | object | |
| default | object | default inputs for different fields |

## events

| event          | description                                                      |
| -------------- | ---------------------------------------------------------------- |
| validate       | validate data before submit                                      |
| submit         | update data order-item                                           |
| input          | when input changed                                               |
| goToPayment    | determine product will go to payment page or not                 |
| set order item | update data for payment page (such as title, subtitle, url, ...) |

## functions

| name           | signature                                                                    | description                                                                                                                                                        |
| -------------- | ---------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| reset          | (): void                                                                     | Resets the state of all registered inputs (inside the form) to {} for arrays and null for all other values. Resets errors bag when using the lazy-validation prop. |
| v$.$reset      |
| (): void       | Resets validation of all registered inputs without modifying their state     |
| v$.$validate() |
| (): boolean    | Validates all registered inputs. Returns true if successful and false if not |
