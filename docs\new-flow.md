# Guide

In order to develop a new checkout flow

### Step 1: declare the flow

Put it in the file [`@lib/repositories/worklfow/flow.ts`](../lib/repositories/workflow/flow.ts)
Each workflow should have a distinticve declaration file

### Step 2: building forms

If the workflow reuse existing forms, or using dynamic form with existing inputs, you can pass this step.

#### Forms

To build new customized form, put the component in [`@lib/components/internal/forms`] and follow the [guide here](./form.md)

#### Inputs

Each form should emits the following events:
| event | description |
| ------------------- | ----------------------------------------------------------------- |
| emit-data | when any of the form input has its value updated successfully |
| emit-order-quantity | when updating a form input results in order-item quantity changed |

To build new customized input (apart from [@wellcare/vue-component](https://github.com/wellcare/vue-component) and [vuetify component](https://vuetify.com)), put the component in [`@lib/components/internal/input`]. Each input should emits the following events:
| event | description |
| ----- | ----------- |
| | |

### Step 3: import forms to products group

Link [here](../lib/components/internal/shared/products-group.vue)
