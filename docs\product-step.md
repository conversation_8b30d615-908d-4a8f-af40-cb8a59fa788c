# ProductStep Component 
This is a global component to allow user customize service option and make order payment.

## Props
| field   | type   | required | description                          |
| ------- | ------ | -------- | ------------------------------------ |
| product | string | yes      | slug of the product                  |
| step    | string | no       | name of the step for a checkout flow |

## Events

## Reuse or create new order
Before customizing a service, the logic to use an order and order item is as follow:
```mermaid
graph TD
    A[search incart order] --> B{IncartOrder?}
    B --> |yes| C[store commit order]
    C --> D[search order item]
    B -->|no| E[create order item]
    D --> F{same product?}    
    F --> |yes| G[store commit orderitem]
    F --> |no| H[replace order item]    
```
## How to add new product to step
- Add flow: [docs](./new-flow.md)
- Add step: [docs](./product-step.md)
