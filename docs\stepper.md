# Stepper Component
Stepper is a global component, and used in ProductStep Component with `w-stepper` tag, hence in your project it can be replaced as long as it receive the props to configure steps, and emit events as follow:

## props
| field | description |
| ----- | ----------- |
| steps |             |

## events
| event | description                     |
| ----- | ------------------------------- |
| next  | when  user hits next            |
| back  | when user hits back             |
| jump  | when user go to a specific step |
