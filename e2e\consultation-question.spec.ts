import { test } from '@playwright/test'
import { chromium } from 'playwright'
test('consultation-question', async () => {
  const browser = await chromium.launch({ headless: false })
  const context = await browser.newContext({
    storageState: './auth.json'
  })
  const page = await context.newPage()
  const ctxt = page.context()
  ctxt.storageState()
  await page.goto('http://192.168.86.57:8080/')
  try {
    await page.getByRole('button', { name: 'BS. Nguyễn Trí Đoàn' }).click()
    // Step 1: Choose type of communication
    await page.getByText('Voice call').click()
    await page.getByRole('button', { name: 'Next' }).click()
    // Step 2: Choose language
    await page.locator('.d-flex').first().click()
    await page.getByRole('button', { name: 'Next' }).click()
    // Step 3: Whom is this consultation for?
    await page.locator('.col-sm-6.col-12').first().click()
    await page.getByRole('button', { name: 'Next' }).click()
    // Step 4: Medical history
    await page.locator('.v-text-field__slot').first().click()
    await page.locator('input[name="reason"]').fill('Remember me')
    await page
      .locator('textarea[name="chiefComplaint"]')
      .fill('What do you do?')
    await page.locator('input[name="question-0"]').fill('Hello')
    await page.getByRole('button', { name: 'Next' }).click()
    await page.getByText('Vital Signs').click()
    await page
      .locator('form')
      .filter({ hasText: 'Height cm Weight kg' })
      .getByRole('button')
      .nth(1)
      .press('Tab')
    await page
      .locator('form')
      .filter({ hasText: 'Height cm Weight kg' })
      .getByRole('button')
      .nth(2)
      .press('Tab')
    await page.getByRole('button', { name: 'Next' }).click()
    await page.getByText('Vaccination').click()
    await page.locator('.v-radio', { hasText: 'None or Unknown' }).click()
    await page.getByRole('button', { name: 'Next' }).click()
    // Step 5: Choose an appointment
    await page.getByRole('button', { name: 'Next' }).click()
    await page.locator('.duration-time').first().click()
    await page
      .locator('.row.fill-height.no-gutters.align-center.justify-center')
      .first()
      .click()
    await page.locator('button.rounded-lg.my-2.mx-2').first().click()
    await page.getByText('I do consent').click()
    await page.waitForSelector('button', { timeout: 5000 })
    await page.getByRole('button', { name: 'Next' }).click()

    // Wait before closing the browser
    await page.waitForTimeout(6000)
    // Close the browser
    await browser.close()
  } catch (error) {
    console.error('Oops, something went wrong:', error)
  }
})
