## How to automate forms with <PERSON><PERSON> and <PERSON><PERSON>

https://dev.to/apify/how-to-automate-forms-with-javascript-and-playwright-4077

// Expect a title "to contain" a substring.
`await expect(page).toHaveTitle(/Playwright/)`

// Expect an attribute "to be strictly equal" to the value.
`await expect(page.locator('text=Get Started').first()).toHaveAttribute('href', '/docs/intro')`

// Expect an element "to be visible".
`await expect(page.locator('text=Learn more').first()).toBeVisible()`

// Expect some text to be visible on the page.
`await expect(page.locator('text=Introduction').first()).toBeVisible()`

## Preserve authenticated state

Run codegen with --save-storage to save cookies and localStorage at the end of the session. This is useful to separately record an authentication step and reuse it later when recording more tests.

`npx playwright codegen ${your-website} --save-storage=auth.json`
