/* purgecss start ignore  */
.fade {
  &-enter-active,
  &-leave-active {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  }

  &-enter,
  &-leave-active {
    opacity: 0;
  }
}

.layout,
.page {
  &-enter-active,
  &-leave-active {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  }

  &-enter,
  &-leave-active {
    opacity: 0;
  }
}

/* purgecss end ignore */
.text-20 {
  font-size: 20px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-23 {
  font-size: 23px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-24 {
  font-size: 24px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-22 {
  font-size: 22px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-18 {
  font-size: 18px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-16 {
  font-size: 16px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-14 {
  font-size: 14px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-36 {
  font-size: 36px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-34 {
  font-size: 34px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-25 {
  font-size: 25px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-26 {
  font-size: 26px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.premiere-button {
  background: #fbc02d !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.conceirge-button {
  background: #1e88e5 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

#check-out-layout {
  background: #f5f5f5 !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.product-step .headline {
  font-size: larger !important;
}

@media only screen and (max-width: 425px) {
  .text-20 {
    font-size: 19px !important;
  }

  .text-23 {
    font-size: 22px !important;
  }

  .text-24 {
    font-size: 23px !important;
  }

  .text-22 {
    font-size: 21px !important;
  }

  .text-18 {
    font-size: 17px !important;
  }

  .text-16 {
    font-size: 15px !important;
  }

  .text-14 {
    font-size: 13px !important;
  }

  .text-36 {
    font-size: 35px !important;
  }

  .text-34 {
    font-size: 33px !important;
  }

  .text-25 {
    font-size: 24px !important;
  }

  .text-26 {
    font-size: 26px !important;
  }
}

* {
  font-family: 'Quicksand', sans-serif !important;
}

.w-toast {
  padding: 20px !important;
  justify-content: start !important;
  font-family: Quicksand, sans-serif;
  font-weight: 500 !important;
}

.w-warning-toast {
  background-color: #ffc107 !important;
}

.w-info-toast {
  background-color: #17a2b8 !important;
}

.w-error-toast {
  background-color: #f44336;
}

.w-succes-toast {
  background-color: #4caf50;
}

.w-full {
  width: 100% !important;
}
