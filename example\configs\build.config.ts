import ESLintPlugin from 'eslint-webpack-plugin'

export const buildConfig = {
  extend(config: any, ctx: any) {
    if (ctx.isDev && ctx.isClient && config.module) {
      config?.plugins?.push(
        new ESLintPlugin({
          extensions: ['vue', 'ts']
        })
      )
    }
    config.node = {
      fs: 'empty'
      // perf_hooks: 'empty'
    }
    config?.module?.rules.push({
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto'
    })
  },
  postcss: {},
  transpile: [/typed-vuex/, 'vee-validate/dist/rules']
}
