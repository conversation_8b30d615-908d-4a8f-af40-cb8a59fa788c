export const checkoutConfig = (env) => ({
  prefix: 'w',
  level: 1,
  vat: {
    show: false,
    useOrganizationId: '1234'
  },
  fee: {
    hidden: true
  },
  beneficiary: {
    meOnly: true
  },
  payment: {
    showFee: true,
    consultationLink: env.PAYMENT_CONSULTATION_LINK,
    gatewayId: env.PAYMENT_GATEWAY_ID,
    acceptWalletTypes: ['cash', 'membership'],
    blockedProductsForPayment: env.BLOCKED_PRODUCTS_FOR_PAYMENT_BY_SKU,
    promotion: env.PAYMENT_PROMOTION_SLUG,
    wallet: {
      cash: {
        title: 'wellcare-prepaid',
        label: 'wellcare card',
        className: 'service-card',
        titleColor: {
          color: 'rgb(130, 255, 243)',
          textShadow: '0px 4px 4px #2a7d75'
        },
        colorAccount: {
          color: '#194F48',
          textShadow: '0px 4px 4px rgba(132, 182, 161, 0.26)'
        }
        // color: ['#01463f', '#009688'],
        // icon: '$wallet-outline',
        // wellcareLogo: true,
      },
      membership: {
        title: 'premiere',
        label: 'membership card',
        className: 'member-card',
        titleColor: {
          color: '#ffe1d7',
          textShadow: '0px 4px 4px #903a1d'
        },
        colorAccount: {
          color: '#8C2502',
          textShadow: '0px 4px 4px rgba(132, 182, 161, 0.26)'
        }
        // color: ['#243b55', '#141e30'],
        // image: `/icons/diamond.png`,
        // wellcareLogo: true,
      },
      point: {
        title: 'point',
        color: ['#01463f', '#009688'],
        icon: '$wallet-outline',
        wellcareLogo: false
      },
      premiere: {
        title: 'premiere',
        color: ['#eacda3', '#d6ae7b'],
        image: `/icons/crown.png`,
        wellcareLogo: true
      },
      conceirge: {
        title: 'conceirge',
        color: ['#243b55', '#141e30'],
        image: `/icons/diamond.png`,
        wellcareLogo: true
      }
    },
    membership: [
      {
        type: 'premiere',
        title: 'premiere',
        description: 'for premiere care',
        pricePerMonth: 80000,
        icon: '/icons/crown.png',
        background: 'yellow lighten-5',
        mainColor: 'yellow darken-2',
        buttonClass: 'premiere-button',
        benefits: [
          { text: 'short question for doctor.' },
          { text: '960.000 VND year.' },
          {
            text: 'watch all video QA from doctor without limitation.'
          },
          {
            text: 'unlock all health courses and resources'
          }
        ]
      },
      {
        type: 'conceirge',
        title: 'conceirge',
        description: 'for conceirge care',
        pricePerMonth: 250000,
        icon: '/icons/diamond.png',
        background: 'blue lighten-5',
        mainColor: 'blue',
        buttonClass: 'conceirge-button',
        benefits: [
          { text: 'all premiere care service.' },
          { text: '3.000.000 VND year.' },
          {
            text: 'register your private doctor and assistant up to 2 member.'
          },
          { text: 'can change private doctor (2 times)' }
        ]
      }
    ],
    sub: {
      match: [/manulife/]
    },
    addOnServices: true,
    voucher: {
      allow: true,
      onlyVoucher: false,
      autoApply: false,
      cancel: true,
      policy: null,
      code: env.PAYMENT_VOUCHER_CODE
    }
  },
  topup: {
    sources: env.PAYMENT_SOURCES,
    zalopay: {
      baseUrl: env.ZALOPAY_URL,
      appId: env.ZALOPAY_APP_ID
    },
    momo: {},
    bank: {
      accounts: [
        {
          number: '*********',
          beneficiary: 'CTCP CONG NGHE MHEALTH -',
          bankName: '- Asia Commercial Bank'
        }
      ]
    }
  },
  forWellcare: true,
  UIcustomize: {
    canUploadFile: false,
    chooseMedium: {
      showFee: true,
      showIconSelected: true,
      showOptionOffline: true
    },
    chooseProviderTimeslot: {
      showFee: true,
      showAlert: true
    },
    question: {
      placeholder:
        'unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
      // 'unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
    },
    prepareMedicalRecord: {
      canUploadFile: true,
      showLabel: true
    },
    stepper: {
      showArrowIcon: false
    }
  },
  bpm: {
    defaultKey: 'checkout',
    disabled: false
  },
  deeplink: {
    enabled: false,
    scheme: 'wellcare://'
  }
})
