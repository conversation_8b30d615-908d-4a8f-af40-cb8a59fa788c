export const pwaOption = {
  // https://pwa.nuxtjs.org/meta
  meta: {
    lang: 'vi',
    nativeUI: true
  },
  // https://pwa.nuxtjs.org/manifest
  manifest: {
    theme_color: '#4299E1',
    start_url: './'
    //   useWebmanifestExtension: false
  },

  // https://pwa.nuxtjs.org/workbox
  workbox: {
    offline: true,
    offlineStrategy: 'networkFirst',
    offlineAnalytics: true,
    enabled: true,
    runtimeCaching: [
      {
        // Should be a regex string. Compiles into new RegExp('https://my-cdn.com/.*')
        urlPattern: 'https://.*/api/v4.0/provider/.*/schedule/search',
        // Defaults to `NetworkFirst` if omitted
        handler: 'CacheFirst',
        strategyOptions: {
          cacheName: 'provider-timeslot' // required for Expiration plugin
        },
        strategyPlugins: [
          {
            use: 'Expiration',
            config: {
              maxEntries: 10,
              maxAgeSeconds: 60 * 5
            }
          }
        ]
      },
      {
        urlPattern: 'https://ajax.cloudflare.com/.*',
        handler: 'staleWhileRevalidate'
      },
      {
        urlPattern: 'https://fonts.gstatic.com/.*',
        handler: 'staleWhileRevalidate'
      },
      {
        urlPattern: 'https://www.google.com/.*',
        handler: 'staleWhileRevalidate'
      },
      {
        urlPattern: 'https://www.gstatic.com/.*',
        handler: 'staleWhileRevalidate'
      }
    ]
  }
}
