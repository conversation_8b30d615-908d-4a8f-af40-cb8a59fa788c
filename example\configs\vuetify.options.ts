import { IconsOptions } from 'vuetify/types/services/icons'
import {
  mdiWeatherSunny,
  mdiWeatherNight,
  mdiCash,
  mdiRecord,
  mdiCloseCircle,
  mdiTag,
  mdiChevronRight,
  mdiCheck,
  mdiClose,
  mdiFlare,
  mdiContentCopy,
  mdiCheckCircle,
  mdiTrashCan,
  mdiAccountPlus,
  mdiAccount,
  mdiCalendar,
  mdiGenderMale,
  mdiGenderFemale,
  mdiCircleOutline,
  mdiWalletOutline,
  mdiMagnify,
  mdiChevronLeft,
  mdiCreditCardOutline,
  mdiPhone,
  mdiVideo,
  mdiCancel,
  mdiAlertCircle,
  mdiCameraEnhance,
  mdiVideoPlusOutline,
  mdiCircle,
  mdiCircleSmall,
  mdiStar,
  mdiPlay,
  mdiCloseCircleOutline,
  mdiCameraOutline,
  mdiFilePdfBox,
  mdiWaveform,
  mdiDotsVertical,
  mdiCloudUploadOutline,
  mdiDownload,
  mdiAlert,
  mdiCalendarClock,
  mdiCardBulletedOutline,
  mdiChevronDoubleDown,
  mdiChevronDoubleUp,
  mdiMessageText,
  mdiMapMarker,
  mdiPencil,
  mdiImage,
  mdiChartLine,
  mdiClockOutline,
  mdiScaleBathroom,
  mdiAlertCircleOutline,
  mdiPencilOutline,
  mdiInformationOutline,
  mdiCrown,
  mdiCheckCircleOutline,
  mdiLightningBolt,
  mdiRadioboxMarked,
  mdiRadioboxBlank,
  mdiCheckboxMarked,
  mdiTrashCanOutline,
  mdiChevronDown,
  mdiChevronUp,
  mdiHomeCity
} from '@mdi/js'

export default {
  icons: {
    iconfont: 'mdiSvg', // default
    values: {
      chevronDown: mdiChevronDown,
      chevronUp: mdiChevronUp,
      pencil: mdiPencil,
      alertCircleOutline: mdiAlertCircleOutline,
      scaleBathroom: mdiScaleBathroom,
      chartLine: mdiChartLine,
      chervonRight: mdiChevronRight,
      clockOutline: mdiClockOutline,
      alert: mdiAlert,
      account: mdiAccount,
      accountPlus: mdiAccountPlus,
      calendar: mdiCalendar,
      weatherSunny: mdiWeatherSunny,
      weatherNight: mdiWeatherNight,
      cash: mdiCash,
      record: mdiRecord,
      'close-circle': mdiCloseCircle,
      tag: mdiTag,
      'chevron-right': mdiChevronRight,
      check: mdiCheck,
      close: mdiClose,
      flare: mdiFlare,
      'content-copy': mdiContentCopy,
      checkCircle: mdiCheckCircle,
      trash: mdiTrashCan,
      trashOutline: mdiTrashCanOutline,
      male: mdiGenderMale,
      female: mdiGenderFemale,
      circleOutline: mdiCircleOutline,
      'wallet-outline': mdiWalletOutline,
      magnify: mdiMagnify,
      chervonLeft: mdiChevronLeft,
      chevronLeft: mdiChevronLeft,
      'chervon-left': mdiChevronLeft,
      chevronRight: mdiChevronRight,
      'check-circle': mdiCheckCircle,
      'credit-card-outline': mdiCreditCardOutline,
      phone: mdiPhone,
      video: mdiVideo,
      cancel: mdiCancel,
      'alert-circle': mdiAlertCircle,
      cameraEnhance: mdiCameraEnhance,
      videoPlus: mdiVideoPlusOutline,
      circle: mdiCircle,
      'circle-small': mdiCircleSmall,
      star: mdiStar,
      play: mdiPlay,
      'close-circle-outline': mdiCloseCircleOutline,
      cameraOutline: mdiCameraOutline,
      filePdfBox: mdiFilePdfBox,
      waveform: mdiWaveform,
      dotsVertical: mdiDotsVertical,
      cloudUpload: mdiCloudUploadOutline,
      download: mdiDownload,
      calendarClock: mdiCalendarClock,
      cardBulletedOutline: mdiCardBulletedOutline,
      doubleDown: mdiChevronDoubleDown,
      doubleUp: mdiChevronDoubleUp,
      messageText: mdiMessageText,
      mapMarker: mdiMapMarker,
      edit: mdiPencil,
      image: mdiImage,
      pencilOutline: mdiPencilOutline,
      informationOutline: mdiInformationOutline,
      crown: mdiCrown,
      checkCircleOutline: mdiCheckCircleOutline,
      lightningBolt: mdiLightningBolt,
      radioboxMarked: mdiRadioboxMarked,
      radioboxBlank: mdiRadioboxBlank,
      checkboxMarked: mdiCheckboxMarked,
      homeCity: mdiHomeCity
    }
  } as IconsOptions,
  customVariables: ['~/assets/variables.scss'],
  theme: {
    dark: false,
    default: false,
    disable: false,
    options: {
      customProperties: true
    },
    themes: {
      light: {
        primary: '#009688',
        secondary: '#ff5722',
        accent: '#ff5722',
        error: '#f44336',
        warning: '#ffc107',
        info: '#2196f3',
        success: '#4caf50'
      },
      dark: {
        primary: '#009688',
        secondary: '#ff5722',
        accent: '#ff5722',
        error: '#f44336',
        warning: '#ffc107',
        info: '#2196f3',
        success: '#4caf50'
      }
    }
  }
}
