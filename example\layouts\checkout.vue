<template>
  <v-app id="check-out-layout">
    <v-main>
      <v-container class="pa-0" fluid>
        <Nuxt keep-alive />
      </v-container>
    </v-main>
  </v-app>
</template>
<script lang="ts">
import { ref, useContext, useRoute, useRouter } from '@nuxtjs/composition-api'
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  setup() {
    const drawer = ref(false)
    const { $vuetify } = useContext()
    const route = useRoute()
    const { options } = useRouter()
    const routes = options.routes
    $vuetify.theme.dark = false
    return { drawer, routes, route }
  }
})
</script>
