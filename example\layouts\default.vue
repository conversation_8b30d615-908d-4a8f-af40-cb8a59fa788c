<template>
  <v-app>
    <v-app-bar color="#14b8a6" app dark flat>
      <v-app-bar-nav-icon @click="drawer = !drawer" />
      <v-toolbar-title>{{ route.name }}</v-toolbar-title>
    </v-app-bar>
    <v-navigation-drawer v-model="drawer" absolute temporary>
      <v-list nav dense>
        <v-list-item-group active-class="deep-purple--text text--accent-4">
          <v-list-item
            v-for="route in routes"
            :key="route.path"
            :to="route.path"
          >
            <v-list-item-title>{{ route.name }}</v-list-item-title>
          </v-list-item>
        </v-list-item-group>
      </v-list>
      <template #append>
        <div class="pa-2">
          <v-switch
            v-model="$vuetify.theme.dark"
            :prepend-icon="
              $vuetify.theme.dark ? '$weatherNight' : '$weatherSunny'
            "
            label="color mode"
          />
        </div>
      </template>
    </v-navigation-drawer>
    <v-main>
      <v-container fluid class="pa-0">
        <Nuxt keep-alive />
      </v-container>
    </v-main>
  </v-app>
</template>
<script lang="ts">
import { ref, useContext, useRoute, useRouter } from '@nuxtjs/composition-api'
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
  setup() {
    const drawer = ref(false)
    const { $vuetify } = useContext()
    const route = useRoute()
    const { options } = useRouter()
    const routes = options.routes
    $vuetify.theme.dark = false
    return { drawer, routes, route }
  }
})
</script>
