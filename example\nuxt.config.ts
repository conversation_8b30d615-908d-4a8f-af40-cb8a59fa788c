import { resolve } from 'path'
import { NuxtOptions } from '@nuxt/types'
import DotEnv from 'dotenv'
import { axiosConfig } from './configs/axios.config'
import { googleFontsConfig } from './configs/google-fonts.config'
import {
  buildConfig,
  checkoutConfig,
  pwaOption,
  webpackOptimzationOption,
  vuetifyConfig,
  i18nConfig,
  toastConfig,
  authenConfig
} from './configs/index'
import { dayjsConfig } from './configs/dayjs.config'
import { gtmConfig } from './configs/gtm.config'

DotEnv.config({ path: './.env' })

export default {
  target: 'server',
  ssr: false,
  components: true,
  loading: '~/components/loading.vue',
  publicRuntimeConfig: {
    accountBaseUrl: process.env.ACCOUNT_BASE_URL,
    endpoint: process.env.API_ENDPOINT,
    'nuxt-module-data-layer': {
      /* The code snippet is setting the values for the `baseURL`, `accountBaseURL`, and `xTenantId`
      properties in the `publicRuntimeConfig` object. These values are retrieved from environment
      variables (`process.env.API_ENDPOINT`, `process.env.ACCOUNT_BASE_URL`, and
      `process.env.X_TENANT_ID`). */
      baseURL: process.env.API_ENDPOINT,
      accountBaseURL: process.env.ACCOUNT_BASE_URL,
      xTenantId: process.env.X_TENANT_ID
    },
    'nuxt-module-media': {
      uploadEndpoint:
        process.env.MEDIA_UPLOAD_ENDPOINT || 'https://upload.mhealthvn.com'
    },
    'nuxt-module-account': authenConfig(process.env),
    checkout: checkoutConfig(process.env),
    fileEndpoint: process.env.FILE_ENDPOINT
  },
  server: {
    host: process.env.APP_HOST || '0.0.0.0',
    port: process.env.APP_PORT || '8080'
  },
  rootDir: resolve(__dirname, '..'),
  buildDir: resolve(__dirname, '../.nuxt'),
  srcDir: resolve(__dirname),
  dir: {
    static: '../lib/static'
  },
  plugins: [{ src: '~/plugins/vue-dompurify-html', mode: 'client' }],
  css: ['~/assets/styles/app'],
  modules: [
    ['@wellcare/nuxt-module-data-layer', { prefix: 'w', level: 1 }],
    '@nuxtjs/axios',
    'nuxt-socket-io',
    'cookie-universal-nuxt',
    '@nuxtjs/dayjs',
    '@nuxtjs/i18n',
    '@nuxtjs/toast',
    ['@nuxtjs/pwa', pwaOption],
    ['../lib', checkoutConfig(process.env)],
    [
      '@nuxtjs/gtm',
      {
        ...gtmConfig,
        enabled: true,
        debug: true
      }
    ]
  ],
  buildModules: [
    '@nuxt/typescript-build',
    '@vueuse/nuxt',
    '@nuxtjs/composition-api/module',
    '@nuxtjs/eslint-module',
    '@nuxtjs/stylelint-module',
    '@nuxtjs/google-fonts',
    '@nuxtjs/vuetify',
    // 'nuxt-typed-vuex',
    ['@wellcare/nuxt-module-phr', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-chart', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-media', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-account', { prefix: 'w', level: 1 }],
    ['@wellcare/vue-component', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-elastic', { prefix: 'w', level: 1 }],
    ['@wellcare/nuxt-module-content', { prefix: 'w', level: 1 }],
    ['nuxt-webpack-optimisations', webpackOptimzationOption]
  ],
  io: {
    sockets: [
      // Required
      {
        // At least one entry is required
        name: 'Observation',
        url: process.env.SOCKET_ENDPOINT || 'https://socketio.mhealthvn.com',
        default: true
      }
    ]
  },
  router: {
    middleware: ['authenRedirect', 'checkMembership']
  },
  build: buildConfig,
  axios: axiosConfig,
  vuetify: vuetifyConfig,
  i18n: i18nConfig,
  // https://typescript.nuxtjs.org/guide/lint.html
  typescript: {
    typeCheck: {
      eslint: {
        files: './**/*.{ts,vue}'
      }
    }
  },
  googleFonts: googleFontsConfig,
  dayjs: dayjsConfig,
  // https://github.com/nuxt-community/eslint-module
  eslint: {
    fix: true
  },

  // https://github.com/nuxt-community/stylelint-module
  stylelint: {
    fix: true
  },
  generate: {
    interval: 3000
  },
  toast: toastConfig
} as unknown as Partial<NuxtOptions>
//                      xxxxx
//                   xXXXXXXXXXx
//                  XXXXXXXXXXXXX           FROM - duccanhole
//                 xXXXXXXXX  XXXx
//                 XXXXXXXXX 0XXXX\\\\\\
//                xXXXXXXXXXxxXXXX\\\\\\\
//                XXXXXXXXXXXXXXXX////// \
//                XXXXXXXXXXXXXXXXX
//                XXXXX|\XXX/|XXXXX
//                XXXXX| \-/ |XXXXX
//               xXXXXX| [ ] |XXXXXx
//             xXXXX   | /-\ |   XXXXx
//          xXXXXX     |/   \|     XXXXXx
//        xXXXXXX                   XXXXXXx
//       xXXXXXXX                   XXXXXXXx
//      xXXXXXXXX                   XXXXXXXXx
//     xXXXXXXXXX                   XXXXXXXXXx
//    xXXXXXXXXXX                   XXXXXXXXXXx
//   xXXXXXXXXXXX                   XXXXXXXXXXXx
//  xXXXXXXXX XXX                   XXX XXXXXXXXx
//  XXXXXXXX  XXX                   XXX  XXXXXXXX
// xXXXXXXX   XXX                   XXX   XXXXXXXx
// XXXXXX     XXX                   XXX     XXXXXX
// XXXX       XXX                   XXX       XXXX
//  XX        XXX                   XXX        XX
//            XXX                   XXX
//            XXX                   XXX
//            XXX                   XXX
//            XXX                   XXX
//            XXXx                 xXXX
//            XXXXXXXXXXXXXXXXXXXXXXXXX
//            XXXXXXX           XXXXXXX
//        ____XXXXXX             XXXXXX____
//       /________/               \________\
