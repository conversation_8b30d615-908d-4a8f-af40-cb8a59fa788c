<template>
  <v-container class="w-container">
    <h1 v-dompurify-html="'Current incart orders'"></h1>
    <v-btn @click="test">Test</v-btn>
    <v-text-field v-model="userId" label="userId"> userId </v-text-field>
    <v-btn @click="executeSearchOrder()"> search </v-btn>
    <v-text-field v-model="orderId" label="orderId"> orderId </v-text-field>
    <v-btn color="error" @click="signOut">Log out</v-btn>
    <v-btn @click="execute()"> remove </v-btn>
    <v-btn :disabled="!orderId" :to="`/order/${orderId}`"> pay </v-btn>
    <h1>Test Full Product flow</h1>
    <div class="d-flex flex-wrap" style="gap: 10px">
      <v-btn
        color="#4ade80"
        class="mt-5"
        @click="$router.push('/checkout/dat-cau-hoi')"
      >
        Đặt câu hỏi
      </v-btn>
      <v-btn
        color="#cbd5e1"
        class="mt-5"
        @click="$router.push('/checkout/hoi-kien-thuc')"
      >
        Hỏi kiến thức
      </v-btn>
      <v-btn
        color="primary"
        class="mt-5"
        @click="$router.push('/checkout/hoa-nguyen-hoa')"
      >
        BS. Hoa Nguyen Hoa
      </v-btn>

      <v-btn
        color="#a78bfa"
        class="mt-5"
        @click="$router.push('/checkout/nguyen-tri-doan')"
      >
        BS. Nguyễn Trí Đoàn
      </v-btn>

      <v-btn
        color="#f86553"
        class="mt-5"
        @click="
          $router.push(
            '/checkout/nguyen-thai-anh-khoa-wellcare-test-membership-personal-doctor'
          )
        "
      >
        BS. Nguyen-thai-anh-khoa membership-personal-doctor
      </v-btn>

      <v-btn
        color="#86efac"
        class="mt-5"
        @click="
          $router.push('/checkout/nguyen-tri-doan-membership-personal-doctor')
        "
      >
        BS. Nguyen Tri Doan membership-personal-doctor
      </v-btn>

      <v-btn
        color="#d1bbf3"
        class="mt-5"
        @click="$router.push('/checkout/membership')"
      >
        Mua member hong
      </v-btn>
      <v-btn
        color="#ef8c6a"
        class="mt-5"
        @click="$router.push('/checkout/pregnancy-diary')"
      >
        Nhật ký thai kỳ
      </v-btn>
      <v-btn
        color="#41b35d"
        class="mt-5"
        @click="$router.push('/checkout/personal-doctor')"
      >
        Bác sĩ riêng
      </v-btn>
      <v-btn
        color="#0866ff"
        class="mt-5"
        style="color: white !important"
        @click="$router.push('/checkout/addon-membership')"
      >
        Addon Membership
      </v-btn>
      <v-btn
        color="#f59e0b"
        class="mt-5"
        style="color: white !important"
        @click="$router.push('/checkout/hanh-trinh-cung-con-khon-lon')"
      >
        Hành trình nuôi con khôn lớn
      </v-btn>
    </div>
    <h1>Test single steps</h1>
    <v-text-field v-model="step" />
    <v-btn color="primary" @click="$router.push(`/step/${step}`)"> go </v-btn>
  </v-container>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  useRouter,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import { removeOrder } from '../../lib/repositories/order/remove-order'
import { searchOrder } from '../../lib/repositories/order/search-order'

export default defineComponent({
  setup() {
    const router = useRouter()
    const step = ref('address')
    const { state, dispatch }: any = useStore()
    const { app } = useContext()
    const userId = ref(state.authen.user?._id)
    const option = ref({
      skip: 0,
      limit: 1,
      count: true,
      filter: {
        deliverTo: userId.value,
        state: 'incart'
      }
    })
    watch(userId, () => (option.value.filter.deliverTo = userId.value))
    const { execute: executeSearchOrder, response } = searchOrder(option)
    const orderId = ref('')
    watch(response, () => {
      if (response.value.results.length > 0)
        orderId.value = response.value.results[0]._id
    })
    const { execute } = removeOrder(orderId)
    const signOut = () => {
      dispatch('authen/signOut', { ctx: app })
    }

    const test = () => {
      router.push({
        path: '/checkout/gap-mat-truc-tiep-hoa-nguyen-hoa',
        query: {
          reason: 'Tôi đã cảm thấy buồn chán',
          expectations: [
            'Lắng nghe',
            'Chỉ cho tôi các kỹ năng mới',
            'Tôi không biết'
          ]
        }
      })
    }
    return {
      step,
      userId,
      orderId,
      execute,
      executeSearchOrder,
      test,
      signOut
    }
  }
})
</script>
<style scoped>
.w-container {
  height: 100vh;
  display: grid;
  place-items: center;
}
.w-container >>> button {
  box-shadow: none !important;
  letter-spacing: normal;
}
</style>
