<template>
  <w-order-page class="order-page" />
</template>
<script lang="ts">
import {
  defineComponent,
  onMounted,
  useStore,
  computed
} from '@nuxtjs/composition-api'
export default defineComponent({
  layout: 'checkout',
  setup() {
    const { commit, state } = useStore()
    // eslint-disable-next-line dot-notation
    const wallets = computed(() => state['checkout']['wallets'] || [])

    onMounted(() => {
      commit('checkout/updateField', {
        path: 'wallets',
        value: [...wallets.value]
      })
    })
  }
})
</script>
<style>
@import url('https://fonts.googleapis.com/css2?family=Quicksand&display=swap');
body * {
  font-family: 'Quicksand', sans-serif !important;
}
</style>
