<template>
  <v-container class="w-container">
    <h1>testing a step</h1>
    <p>{{ param }}</p>
    <component :is="component" />
  </v-container>
</template>
<script lang="ts">
import { defineComponent, ref, useContext } from '@nuxtjs/composition-api'
import Address from '../../../../lib/components/internal/forms/address/index.vue'
import Question from '../../../../lib/components/internal/forms/question.vue'

export default defineComponent({
  components: { Address, Question },
  setup() {
    const { route } = useContext()
    const steps = ['Address', 'Question']
    const component = ref(route.value.params.step)
    return { component, steps }
  }
})
</script>
<style scoped>
.w-container {
  height: 100vh;
  display: grid;
  place-items: center;
}
</style>
