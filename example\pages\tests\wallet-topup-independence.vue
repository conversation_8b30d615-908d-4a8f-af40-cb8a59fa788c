<template>
  <w-choose-topup
    :title="$t('top up via')"
    :outlined="false"
    flat
    :width="'100%'"
    :return-url="'account'"
  />
</template>
<script lang="ts">
import { useContext, useStore } from '@nuxtjs/composition-api'
import { defineComponent, onMounted, ref } from '@vue/composition-api'

export default defineComponent({
  setup() {
    const { $axios } = useContext()
    const { state }: any = useStore()
    const wallets = ref([])
    onMounted(() => {
      $axios({
        url: `/wallet-journal/account/user/${state.authen.user._id}`,
        method: 'get'
      }).then((res) => (wallets.value = res.data.results))
    })
    return {}
  }
})
</script>
