<template>
  <v-container>
    <v-autocomplete v-model="key" label="choose a flow" :items="workflows" />
    <p>{{ workflow }}</p>
  </v-container>
</template>
<script lang="ts">
import { computed, defineComponent, ref } from '@nuxtjs/composition-api'
import { useWorkflow } from '../../../lib/repositories/workflow/use-workflow'

export default defineComponent({
  setup() {
    const key = ref('consultation-question')
    const workflows = ['consultation-question', 'consultation-indepth']
    const option = computed(() => {
      return { key: key.value }
    })
    const { workflow } = useWorkflow(option)
    return { workflow, workflows, key }
  }
})
</script>
