/* purgecss start ignore  */
.fade {
  &-enter-active,
  &-leave-active {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  }

  &-enter,
  &-leave-active {
    opacity: 0;
  }
}

.layout,
.page {
  &-enter-active,
  &-leave-active {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  }

  &-enter,
  &-leave-active {
    opacity: 0;
  }
}

/* purgecss end ignore */
.text-20 {
  font-size: 20px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-23 {
  font-size: 23px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-24 {
  font-size: 24px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-22 {
  font-size: 22px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-18 {
  font-size: 18px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-16 {
  font-size: 16px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-14 {
  font-size: 14px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-36 {
  font-size: 36px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-34 {
  font-size: 34px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.text-25 {
  font-size: 25px !important;
  font-family: 'Quicksand', sans-serif !important;
}

.member-card.premiere {
  background: #eacda3 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #d6ae7b, #eacda3) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #d6ae7b, #eacda3) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.member-card.conceirge {
  background: #141e30 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #141e30, #243b55) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #141e30, #243b55) !important;
}

.member-card.customer {
  background: #1f4037 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #99f2c8, #1f4037) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #99f2c8, #1f4037) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.member-card.wellcare-prepaid {
  background: #009688 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #009688, #01463f) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #009688, #01463f) !important;
}

.membership-table {
  background: #f5e2d0 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #ffffff, #f5e2d0) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #ffffff, #f5e2d0) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.premiere-button {
  background: #fbc02d !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #ffee58, #fbc02d) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.conceirge-button {
  background: #1e88e5 !important;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #90caf9, #1e88e5) !important;
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.overflow-hidden {
  overflow: hidden !important;
}

.product-step .headline {
  font-size: larger !important;
}

.w-full {
  width: 100% !important;
}
@media only screen and (max-width: 425px) {
  .member-card {
    transform: scale(0.9) !important;
  }
}
