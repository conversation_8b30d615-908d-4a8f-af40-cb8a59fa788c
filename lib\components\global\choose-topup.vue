<template>
  <v-card
    v-if="availableTopupMethods.length > 0"
    class="mx-auto"
    elevation="0"
    style="box-shadow: 0px 2px 6px -2px rgba(79, 79, 79, 0.2); border: none"
    v-bind="{ rounded: 'lg', width: '97%', ...$attrs }"
  >
    <v-card-title class="font-weight-bold text-20">{{
      $t(title)
    }}</v-card-title>
    <v-list>
      <v-list-item-group color="primary">
        <template v-for="method in availableTopupMethods">
          <v-list-item
            :key="method.key"
            color="primary"
            @click="selectsMethod(method)"
          >
            <v-img
              max-height="40"
              max-width="40"
              :src="'https://khamtuxa.vn/' + method.image"
            ></v-img>
            <v-list-item-content class="px-4">
              <v-list-item-title class="font-weight-bold text-18">{{
                $t(method.title)
              }}</v-list-item-title>
              <v-list-item-subtitle class="text-14 mt-1">{{
                $t(method.subtitle)
              }}</v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-icon
              ><v-icon medium>$chevron-right</v-icon></v-list-item-icon
            >
          </v-list-item>
        </template>
      </v-list-item-group>
    </v-list>
    <v-dialog
      v-model="currentMethod.state"
      :fullscreen="$vuetify.breakpoint.xsOnly"
      max-width="600"
      transition="dialog-bottom-transition"
    >
      <v-card tile elevation="0">
        <v-toolbar :height="55 + safeArea.top" color="primary" class="toolbar">
          <div
            class="white--text d-flex align-center justify-space-between"
            style="width: 100%"
          >
            <span class="text-18">{{ $t(currentMethod.method.title) }}</span>
            <v-btn color="white" icon @click="removeCurrentMethod"
              ><v-icon>$close</v-icon></v-btn
            >
          </div>
        </v-toolbar>
        <component
          :is="currentMethod.method.component"
          :user="user"
          :countdown="countdown"
          :method="currentMethod.method"
          :order="order"
          :return-url="returnUrl"
          :current-wallet="currentWallet"
          class="pa-5"
          :class="{
            'pb-16': $vuetify.breakpoint.xsOnly,
            'pop-up': !$vuetify.breakpoint.xsOnly
          }"
        ></component>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useStore
} from '@nuxtjs/composition-api'
import useCountdown from '../../composables/use-countdown'

interface ISafeArea {
  top: number
  right: number
  bottom: number
  left: number
}

interface ITopupMethod {
  key: string
  title: string
  subtitle: string
  image: string
  button?: string
  component: any
}

const DEFAULT_TOPUP_SOURCES = 'momo,zalopay,bank-card,bank-transfer,cash'
const TOPUP_METHODS = [
  {
    key: 'bank-transfer',
    image: 'checkout/payment/methods/bank-transfer.svg',
    title: 'bank transfer',
    subtitle: 'transfer from a bank',
    button: 'confirmed payment',
    component: () => import('../internal/topup/bank-transfer.vue')
  },
  {
    key: 'bank-card',
    image: 'checkout/payment/methods/credit-card.svg',
    title: 'bank card',
    subtitle: 'credit card or debit card',
    button: 'continue',
    component: () => import('../internal/topup/bank-card.vue')
  },
  {
    key: 'momo',
    image: 'checkout/payment/methods/momo.svg',
    title: 'momo e-wallet',
    subtitle: 'pay directly on momo app',
    component: () => import('../internal/topup/momo.vue')
  },
  {
    key: 'zalopay',
    image: 'checkout/payment/methods/zalo-pay-2.png',
    title: 'zalopay e-wallet',
    subtitle: 'through zalopay e-wallet',
    component: () => import('../internal/topup/zalopay.vue')
  },
  {
    key: 'cash',
    image: 'checkout/payment/methods/cash.svg',
    title: 'cash',
    subtitle: 'at convenience stores or supermarkets',
    component: () => import('../internal/topup/cash.vue')
  }
]

export default defineComponent({
  props: {
    title: {
      type: String,
      default: 'Payment methods'
    },
    order: {
      type: Object,
      default: () => {}
    },
    currentWallet: {
      type: Object,
      default: () => {}
    },
    returnUrl: {
      type: String,
      default: '/'
    }
  },
  setup() {
    const { state }: any = useStore()
    const user = computed(() => state.authen.user)
    const { $config } = useContext()
    const { countdown, startCountdown } = useCountdown()
    startCountdown(60 * 8)
    const safeArea = computed<ISafeArea>(
      () => state?.app?.safeArea || { top: 0 }
    )

    // Ensure topupSources is a string
    let topupSourcesString = DEFAULT_TOPUP_SOURCES
    if (typeof $config?.checkout?.topup?.sources === 'string') {
      topupSourcesString = $config.checkout.topup.sources
    }

    const topupSources: string[] = topupSourcesString.split(',')

    const availableTopupMethods: ITopupMethod[] = TOPUP_METHODS.filter(
      (method) => topupSources.includes(method.key)
    )
    const defaultMethod: ITopupMethod = availableTopupMethods[0]
    const currentMethod = ref<{
      state: boolean
      method: ITopupMethod
    }>({
      state: false,
      method: defaultMethod
    })
    const selectsMethod = (method: ITopupMethod) => {
      currentMethod.value.state = true
      currentMethod.value.method = method
    }
    const removeCurrentMethod = () => {
      currentMethod.value.state = false
    }
    return {
      availableTopupMethods,
      currentMethod,
      selectsMethod,
      removeCurrentMethod,
      user,
      topupSources,
      safeArea,
      countdown,
      startCountdown
    }
  }
})
</script>

<style scoped>
.overflow-hidden {
  overflow: hidden !important;
}

.pop-up {
  max-height: 600px !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

@media only screen and (min-width: 769px) {
  .pop-up::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  .pop-up::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
  }

  .pop-up::-webkit-scrollbar-thumb {
    background-color: rgba(138, 135, 135, 0.8);
    border-radius: 10px;
  }

  .pop-up::-webkit-scrollbar-thumb:hover {
    background-color: rgba(138, 135, 135);
  }
}

.toolbar >>> .v-toolbar__content {
  height: 100% !important;
  align-items: flex-end !important;
}
</style>
