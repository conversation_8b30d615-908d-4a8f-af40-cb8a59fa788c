<template>
  <client-only>
    <v-card rounded="lg" class="pa-4">
      <v-card-title class="text-break text-justify">
        <h5 class="font-weight-medium text-center">
          {{ $t('pt') }}. {{ patient.name }}
          <span class="text-lowercase font-weight-light">
            {{ $t('is having a pending consultation with') }}</span
          >
          {{ provider.title || $t('dr') }}. {{ provider.name }}
        </h5>
      </v-card-title>
      <v-card-actions class="justify-center pa-0">
        <v-btn
          x-large
          text
          block
          tile
          class="font-weight-normal justify-center rounded-lg"
          style="
            background-color: rgb(248, 248, 248);
            color: var(--v-primary-base);
            letter-spacing: normal;
          "
          @click="connectToSupporter"
          >{{ $t('call support') }}</v-btn
        >
      </v-card-actions>
      <v-card-actions class="justify-center pa-0">
        <v-btn
          x-large
          block
          tile
          text
          class="mt-3 font-weight-normal justify-center rounded-lg"
          style="
            background-color: var(--v-primary-base);
            letter-spacing: normal;
            color: white;
          "
          @click.native.passive="goToLink"
          >{{ $t('view medical record') }}</v-btn
        >
      </v-card-actions>
      <!-- <v-card-actions class="justify-center pa-0">
        <v-btn
          x-large
          block
          text
          tile
          class="justify-center"
          @click="$emit('on-close')"
        >
          {{ $t('continue') }}
        </v-btn>
      </v-card-actions> -->
      <v-btn icon size="36" class="btn-close" @click="$emit('on-close')">
        <v-icon>$close</v-icon>
      </v-btn>
    </v-card>
  </client-only>
</template>

<script lang="ts">
import { defineComponent, useContext, useRouter } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    patient: {
      type: Object,
      required: true
    },
    provider: {
      type: Object,
      required: true
    },
    consultation: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const { $vuetify, $config, $toast, i18n } = useContext()
    const router = useRouter()
    const connectToSupporter = () => {
      // if ($vuetify.breakpoint.mdAndDown) {
      //   return window.open('https://zalo.me/84366905905')
      // } else {
      //   return window.location.replace(
      //     'https://chat.zalo.me/?phone=84366905905'
      //   )
      // }
      try {
        if (window && process.client) {
          window.open('tel:+84366905905')
        } else {
          $toast.info(i18n.t('call us +84366905905'))
        }
      } catch (error) {
        $toast.info(i18n.t('call us +84366905905'))
      }
    }
    const goToLink = (e: Event) => {
      e.preventDefault()
      const forWellcare = $config.checkout?.forWellcare
      if (forWellcare) {
        let link
        if ($vuetify.breakpoint.mobile) {
          // try go to deep link to open app
          link = 'wellcare://consultations/' + props.consultation._id
          // if cant open deep link, open link phr in web
          const timeout = setTimeout(() => {
            link = `${
              $config.patientBaseUrl || 'https://mobile.wellcare.vn'
            }/consultations/${props.consultation._id}`
            window.location.href = link
          }, 1500)
          // if deeplink was opened, stop open link phr web
          window.onblur = () => {
            clearTimeout(timeout)
          }
        } else
          link = `${
            $config.patientBaseUrl || 'https://mobile.wellcare.vn'
          }/consultations/${props.consultation._id}`
        window.location.href = link
      } else
        router.push({
          path: '/consultations/' + props.consultation._id
        })
    }
    return {
      connectToSupporter,
      goToLink
    }
  }
})
</script>
<style scoped>
.bottom-line {
  border-bottom: solid 1px #c3c3c3;
  border-top: solid 1px #c3c3c3;
}
.btn-close {
  position: absolute;
  top: 5px;
  right: 5px;
  border-top: none !important;
}
</style>
