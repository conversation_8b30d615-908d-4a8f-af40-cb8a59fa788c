<template>
  <v-card
    class="py-10 mx-auto"
    elevation="0"
    color="transparent"
    :width="$vuetify.breakpoint.xsOnly ? '90%' : '500'"
    style="height: 100vh"
  >
    <div
      class="my-6 d-flex align-center justify-center flex-column"
      style="height: 100%"
    >
      <!-- <v-icon size="70" color="primary">$check-circle</v-icon> -->
      <img
        src="https://storage.googleapis.com/cms-gallery/677e57b245f9f378aeb887ff/stars.png"
        height="320"
      />
      <h3 class="font-weight-bold text-20">
        {{ $t('Congratulations') }}
      </h3>
      <div class="mt-3 mb-10 text-center">
        {{
          $t('your subscription is activated, enjoy your exclusive benefits')
        }}
      </div>
      <!-- <instruction
        :consultation-id="consultationId"
        :is-short-question="isShortQuestion"
        :is-membership-product="isMembershipProduct"
      /></div -->
      <div style="width: 100%" class="mt-auto">
        <template v-if="isNative">
          <v-btn
            v-if="isPregnancyProduct"
            class="font-weight-bold my-3 text-uppercase"
            rounded="lg"
            large
            depressed
            color="primary"
            block
            @click="$emit('action:pregnancy-diary')"
            >{{ $t('open') }}</v-btn
          >
          <v-btn
            v-else
            class="font-weight-bold my-3 text-uppercase"
            rounded="lg"
            large
            depressed
            color="primary"
            block
            @click="$emit('action:membership')"
            >Membership</v-btn
          ></template
        >
        <template v-else><download /></template>
      </div>
    </div>
  </v-card>
</template>
<script lang="ts">
import { Capacitor } from '@capacitor/core'
import {
  defineComponent,
  useRoute,
  onMounted,
  onUnmounted,
  computed
} from '@nuxtjs/composition-api'
import confetti from 'canvas-confetti'
import download from '../internal/order/instruction/download.vue'

// function sleep(ms) {
//   return new Promise((resolve) => setTimeout(resolve, ms))
// }

export default defineComponent({
  components: { download },
  setup() {
    const timeoutList: Array<NodeJS.Timeout> = []
    const isNative = Capacitor.isNativePlatform()
    const route = useRoute()
    const isPregnancyProduct = computed(
      () => route.value.query?.pregnancyProduct === 'true'
    )
    const fire = () => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: {
          y: 0.5
        }
      })
    }
    onMounted(() => {
      for (let i = 1; i <= 3; i++) {
        timeoutList.push(setTimeout(fire, 1800 * i))
      }
    })
    onUnmounted(() => {
      for (const timeout of timeoutList) {
        clearTimeout(timeout)
      }
      confetti.reset()
    })
    return {
      isNative,
      isPregnancyProduct
    }
  }
})
</script>
