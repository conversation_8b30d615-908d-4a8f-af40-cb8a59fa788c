<template>
  <div
    class="d-flex align-center justify-center flex-column transparent"
    style="
      width: 100vw;
      max-width: 100vw;
      overflow: hidden;
      height: 100vh;
      position: fixed;
    "
  >
    <div class="font-weight-bold text-20 py-4 mb-8">
      {{ $t('Order is being processed') }}
    </div>
    <div class="text-center" style="min-height: 20px; width: 80%">
      <v-progress-circular
        :size="50"
        color="primary"
        indeterminate
      ></v-progress-circular>
    </div>
  </div>
</template>
<!-- eslint-disable dot-notation -->
<script lang="ts">
import {
  computed,
  defineComponent,
  useRoute,
  useRouter,
  ref,
  onMounted,
  onUnmounted
} from '@nuxtjs/composition-api'
import { usePaymentOrder } from '../../repositories'
import getProductInfo from '../../composables/get-product-info'
export default defineComponent({
  setup() {
    const timeInterval = ref(null)
    const fetchingInterval = ref(null)
    const processing = ref(0)
    const route: any = useRoute()
    const router = useRouter()
    const { isMembershipProduct, isPregnancyProduct } = getProductInfo()
    const { executeGetOrder } = usePaymentOrder(
      computed(() => ({ _id: route.value.params.id })),
      computed(() => ({ polling: computed(() => 1000) })),
      computed(() => null)
    )

    const cleanup = () => {
      clearInterval(timeInterval.value)
      clearInterval(fetchingInterval.value)
    }

    const routeToOrder = (orderId: string) => {
      cleanup()
      processing.value = 100
      router.push(`/payment/order/${orderId}`)
    }

    const routeToOrderSuccess = (orderId: string) => {
      cleanup()
      processing.value = 100
      router.push({
        path: `/payment/order/${orderId}/success`,
        query: {
          membershipProduct: String(isMembershipProduct.value),
          pregnancyProduct: String(isPregnancyProduct.value)
        }
      })
    }

    const checkOrder = async () => {
      try {
        const res = await executeGetOrder()
        const { state, _id } = res.results
        if (['placed', 'fullfilled', 'partialfilled'].includes(state))
          return routeToOrderSuccess(_id)
        if (processing.value >= 100) routeToOrder(_id)
      } catch (error) {
        if (processing.value >= 100) router.push('/')
      }
    }

    onMounted(() => {
      timeInterval.value = setInterval(() => {
        processing.value += 1
      }, 100)
      fetchingInterval.value = setInterval(() => {
        if (process.client) checkOrder()
      }, 1000)
    })

    onUnmounted(() => cleanup())
    return { processing }
  }
})
</script>
