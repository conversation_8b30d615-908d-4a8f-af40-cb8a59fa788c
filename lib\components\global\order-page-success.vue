<template>
  <div>
    <div v-if="isShowBanner" class="d-flex justify-center mt-5">
      <w-banners-container device="android" condition="checkout" />
    </div>
    <v-card
      :width="$vuetify.breakpoint.xsOnly ? '90%' : '500'"
      class="mt-10 py-10 mx-auto"
      elevation="0"
      color="transparent"
    >
      <v-overlay v-if="loading">
        <div class="text-center">
          <v-progress-circular
            :size="70"
            color="primary"
            indeterminate
          ></v-progress-circular>
        </div>
      </v-overlay>
      <template v-else>
        <div class="d-flex align-center justify-center flex-column">
          <v-icon size="70" color="primary">$check-circle</v-icon>
          <div class="text-20 font-weight-bold mt-3 mb-10">
            {{ $t('order successful') }}
          </div>
          <instruction
            :consultation-id="consultationId"
            :is-short-question="isShortQuestion"
            :is-membership-product="isMembershipProduct"
          />
        </div>
      </template>
    </v-card>
  </div>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  useRoute,
  useRouter,
  ref
} from '@nuxtjs/composition-api'
import { usePaymentOrder } from '../../repositories'
import Instruction from '../internal/order/instruction/index.vue'
export default defineComponent({
  components: { Instruction },
  props: {
    isShowBanner: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const loading = ref(true)
    const route = useRoute()
    const router = useRouter()
    const { onGetOrderSuccess, orderItems /*, onGetOrderItemSuccess */ } =
      usePaymentOrder(
        computed(() => ({ _id: route.value.params.id })),
        computed(() => ({ polling: computed(() => 5000) })),
        computed(() => 'placed')
      )
    const consultationId = computed(
      () => orderItems.value[0]?.meta?.consultationId
    )
    const isShortQuestion = computed(
      () => !orderItems.value[0]?.meta?.consultTime
    )
    onGetOrderSuccess((data) => {
      if (
        data.results.state !== 'placed' &&
        data.results.state !== 'fullfilled' &&
        data.results.state !== 'partialfilled'
      )
        router.push(`/payment/order/${data.results._id}`)
      else {
        loading.value = false
      }
    })
    /*
    onGetOrderItemSuccess((data) => {
      if (data?.results[0]?.meta?.medium === 'video') {
        instructionModal.value = true
      }
    })
    */
    return { loading, consultationId, isShortQuestion }
  }
})
</script>
