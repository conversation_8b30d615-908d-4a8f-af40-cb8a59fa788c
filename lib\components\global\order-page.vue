<template>
  <div class="mx-auto pt-5">
    <v-skeleton-loader
      v-if="isLoading"
      class="order-page mx-auto"
      width="97%"
      type="card-avatar, article, actions, table-heading, list-item-two-line, image"
    />

    <component
      :is="orderState"
      v-else
      class="order-page"
      :order="orderDetails"
      :order-items="orderItems"
    />
  </div>
</template>

<script lang="ts">
import {
  ref,
  watch,
  useRoute,
  useRouter,
  computed,
  onMounted,
  useContext,
  onUnmounted,
  defineComponent
} from '@nuxtjs/composition-api'
import { useOrderStatusEvent } from '../../composables'
import {
  updateOrder,
  usePaymentOrder,
  getUserWallets
} from '../../repositories'
import Incart from '../internal/order/state/incart/index.vue'
import Placed from '../internal/order/state/placed/index.vue'
import Fullfilled from '../internal/order/state/fullfilled/index.vue'
import Partialfilled from '../internal/order/state/partialfilled/index.vue'
import Other from '../internal/order/state/other.vue'
import Abandoned from '../internal/order/state/abandoned.vue'

export default defineComponent({
  components: { Incart, Placed, Other, Fullfilled, Partialfilled, Abandoned },
  setup: () => {
    const route = useRoute()
    const router = useRouter()
    const orderUpdateOptions = ref<any>({})
    const { $config, $gtm, store }: any = useContext()
    const orderId = computed(() => route.value.params.id ?? null)
    const userId = computed(() => store.state.authen?.user?._id ?? null)
    const { getUserWalletsLoading } = getUserWallets(
      computed(() => ({ user: userId.value }))
    )

    const { execute: executeOrderUpdate } = updateOrder()

    const {
      orderItems,
      paymentLoading,
      executeGetOrder,
      getOrderLoading,
      order: orderDetails,
      onSearchPromotionSuccess
    } = usePaymentOrder(
      computed(() => ({ _id: orderId.value })),
      computed(() => ({ polling: computed(() => 0) })),
      computed(() => 'pending')
    )
    const { orderStateEvent, executeEvtSource } = useOrderStatusEvent(
      computed(() => orderDetails.value?._id)
    )

    const paymentConfig = computed(() => $config.checkout?.payment ?? {})
    const isPaymentCompleted = computed(
      () => orderDetails.value?.payment?.state === 'done'
    )

    const orderState = computed(
      () => orderStateEvent.value || orderDetails.value?.state || ''
    )
    const isLoading = computed(
      () => getUserWalletsLoading.value || paymentLoading.value
    )

    const handlePromotionSuccess = (response: any) => {
      if (!response?.results || orderDetails.value?.state === 'placed') return

      const promotions = response.results.reduce((acc: string[], item: any) => {
        if (item?._id) acc.push(item._id)
        return acc
      }, [])

      if (paymentConfig.value?.voucher?.autoApply) {
        applyAutoPromotion(promotions, response.results[0])
        return
      }

      updatePromotionInOrder(promotions)
    }

    const applyAutoPromotion = (
      promotions: string[],
      currentPromotion: any
    ) => {
      if (!currentPromotion || isPromotionLimitReached(currentPromotion)) return

      orderUpdateOptions.value = {
        _id: orderDetails.value?._id,
        promotions
      }
      if (orderUpdateOptions.value._id) {
        executeOrderUpdate(orderUpdateOptions.value)
      }
    }

    const isPromotionLimitReached = (promotion: any) =>
      (promotion?.redemption?.count ?? 0) >=
        (promotion?.policy?.benefit?.repeat?.frequencyMax ?? Infinity) ||
      (promotion?.redemption?.sum ?? 0) >=
        (promotion?.policy?.benefit?.maxAmount ?? Infinity)

    const updatePromotionInOrder = (promotions: string[]) => {
      if (orderDetails.value?._id) {
        executeOrderUpdate({
          _id: orderDetails.value._id,
          promotions
        })
      }
    }

    let gtmPushTimeoutId: number | null = null

    const initClientSideLogic = () => {
      getOrderLoading.value = true
      setTimeout(executeGetOrder, 2000)
      gtmPushTimeoutId = window.setTimeout(pushGtmEvent, 0)
      orderUpdateOptions.value.source = window.location.host
    }

    const pushGtmEvent = () => {
      if (!orderDetails.value || !orderItems.value) return

      const gtmOption = {
        event: 'add-to-cart',
        component: 'order-page',
        userId: userId.value,
        ecommerce: {
          currency: 'VND',
          value: orderDetails.value.total ?? 0,
          coupon: orderDetails.value.voucher ?? '',
          items: orderItems.value.map((item: any) => ({
            item_name: item.product?.name ?? '',
            item_id: item.product?._id ?? '',
            item_brand: item.source ?? '',
            item_category: item.product?.sku ?? '',
            quantity: item.quantity ?? 0,
            price: item.price ?? 0,
            item_variant: item.meta?.medium ?? ''
          }))
        }
      }
      $gtm?.push({ ecommerce: null })
      $gtm?.push(gtmOption)
    }

    onSearchPromotionSuccess(handlePromotionSuccess)

    onMounted(() => {
      if (process.client) {
        initClientSideLogic()
      }
      watch(isPaymentCompleted, () => {
        if (isPaymentCompleted.value)
          router.push({
            path: `/payment/order/${orderId.value}/processing`
          })
      })

      executeEvtSource()
    })

    onUnmounted(() => {
      if (gtmPushTimeoutId !== null) {
        clearTimeout(gtmPushTimeoutId)
      }
    })

    return {
      isLoading,
      orderItems,
      orderState,
      orderDetails,
      orderStateEvent,
      orderUpdateOptions,
      isPaymentCompleted
    }
  }
})
</script>

<style scoped>
@media only screen and (max-width: 1260px) {
  .order-page {
    max-width: 960px !important;
  }
}

@media only screen and (min-width: 1261px) {
  .order-page {
    max-width: 1210px !important;
  }
}
</style>
