<template>
  <v-skeleton-loader
    v-if="loading || loadingProductInfo"
    class="order-page mx-auto"
    width="97%"
    type="card-avatar, article, actions, table-heading, list-item-two-line, image"
  />
  <div v-else>
    <w-order-membership-success v-if="hasMembershipProduct" />
    <w-page-order-success-view
      v-else
      :is-short-question="isShortQuestion"
      :consultation-id="consultationId"
      :is-show-banner="isShowBanner"
    />
  </div>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  useRoute,
  useRouter,
  ref,
  watch
} from '@nuxtjs/composition-api'
import { usePaymentOrder } from '../../../../repositories'
import getProductInfo from '../../../../composables/get-product-info'
export default defineComponent({
  props: {
    isShowBanner: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const loading = ref(true)
    const route = useRoute()
    const router = useRouter()
    const { isMembershipProduct, loading: loadingProductInfo } =
      getProductInfo()
    const { onGetOrderSuccess, orderItems, executeGetOrder } = usePaymentOrder(
      computed(() => ({ _id: route.value.params.id })),
      computed(() => ({ polling: computed(() => 4000) })),
      computed(() => ['placed', 'completed'])
    )
    const consultationId = computed(
      () => orderItems.value[0]?.meta?.consultationId
    )
    const isShortQuestion = computed(
      () => !orderItems.value[0]?.meta?.consultTime
    )
    const hasMembershipProduct = computed(
      () => route.value.query?.membershipProduct === 'true'
    )
    onGetOrderSuccess((data) => {
      if (
        data.results.state !== 'placed' &&
        data.results.state !== 'fullfilled' &&
        data.results.state !== 'partialfilled'
      )
        router.push(`/payment/order/${data.results._id}`)
      else {
        loading.value = false
      }
    })
    if (process.client) {
      executeGetOrder()
    }

    watch(isMembershipProduct, (val) => {
      loading.value = true
      router.replace({
        path: route.value.path,
        query: {
          membershipProduct: val
        }
      })
      loading.value = false
    })
    return {
      loading,
      consultationId,
      isShortQuestion,
      loadingProductInfo,
      hasMembershipProduct
    }
  }
})
</script>
