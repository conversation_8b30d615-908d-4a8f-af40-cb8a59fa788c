<template>
  <div>
    <div v-if="isShowBanner" class="d-flex justify-center mt-5">
      <w-banners-container device="android" condition="checkout" />
    </div>
    <v-card
      :width="$vuetify.breakpoint.xsOnly ? '90%' : '500'"
      class="mt-10 py-10 mx-auto"
      elevation="0"
      color="transparent"
    >
      <v-overlay v-if="loading">
        <div class="text-center">
          <v-progress-circular
            :size="70"
            color="primary"
            indeterminate
          ></v-progress-circular>
        </div>
      </v-overlay>
      <div v-else class="d-flex align-center justify-center flex-column">
        <v-icon size="70" color="primary">$check-circle</v-icon>
        <div class="text-20 font-weight-bold mt-3 mb-10">
          {{ $t('order successful') }}
        </div>
        <instruction
          :consultation-id="consultationId"
          :is-short-question="isShortQuestion"
        />
      </div>
    </v-card>
  </div>
</template>
<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'
import Instruction from '../../../internal/order/instruction/index.vue'
export default defineComponent({
  components: { Instruction },
  props: {
    consultationId: {
      type: String,
      default: ''
    },
    isShortQuestion: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    isShowBanner: {
      type: Boolean,
      default: false
    }
  },
  setup() {}
})
</script>
