<template>
  <v-card outlined width="97%" class="my-4 mx-auto pa-2">
    <v-card-text class="d-flex align-center justify-space-between">
      <span>{{ $t('current point') }}</span
      ><strong
        ><span>{{ currentPoint }} {{ $t('point') }}</span></strong
      >
    </v-card-text>
    <v-card-text class="d-flex align-center justify-space-between">
      <span>{{ $t('payment point') }}</span
      ><strong
        ><span
          >{{ getPaymentPoint(paymentFee) }} {{ $t('point') }}</span
        ></strong
      >
    </v-card-text>
    <v-divider></v-divider>
    <v-card-text class="d-flex align-center justify-space-between">
      <span>{{ $t('after payment') }}</span
      ><strong
        ><span
          >{{ currentPoint - getPaymentPoint(paymentFee) }}
          {{ $t('point') }}</span
        ></strong
      >
    </v-card-text>
  </v-card>
</template>
<script lang="ts">
import { defineComponent, useContext, computed } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    currentPoint: {
      type: Number,
      default: 0,
      required: true
    },
    paymentFee: {
      type: Number,
      default: 0,
      required: true
    }
  },
  setup() {
    const { $config } = useContext()
    const convertRate = computed(
      () => $config.checkout?.payment?.pointConversion?.rate
    )
    const getPaymentPoint = (fee: number) => fee / convertRate.value
    return { convertRate, getPaymentPoint }
  }
})
</script>
