<template>
  <client-only>
    <v-container class="mb-16 product-step">
      <v-row dense justify="center">
        <v-col cols="12" lg="8" xs="6">
          <keep-alive exclude="ChooseProvider,Skeleton">
            <products-group
              v-if="orderItem"
              ref="form"
              :order="order"
              :order-item="orderItem"
              :order-items="orderItems"
              :current-step="currentStep"
              :loading="loadingProductOrder"
              @submit="onSubmit"
              @set-order-item="setOrderItem"
              @set-step-meta="setStepMeta"
            />
            <v-skeleton-loader
              v-else
              type="image"
              height="100%"
            ></v-skeleton-loader>
          </keep-alive>
        </v-col>
      </v-row>
      <w-stepper
        ref="stepper"
        :steps="steps"
        :curr-step="currentStep"
        :value="currentStep.step"
        :loading-next="loadingProductOrder"
        @change="onStepperChange"
        @completed="onComplete"
      ></w-stepper>
      <v-dialog v-model="showDialog" width="800" persistent>
        <v-container
          class="d-flex justify-center pa-0"
          style="box-shadow: none !important"
        >
          <v-card class="mx-auto" width="100%" outlined>
            <!-- Top status bar -->
            <v-card-subtitle class="primary d-flex align-center py-4">
              <v-icon color="white" class="mr-2">$alert-circle</v-icon>
              <span class="font-weight-medium white--text">Thông báo</span>
            </v-card-subtitle>

            <v-card-text class="text-center">
              <!-- Calendar icon -->
              <div class="d-flex justify-center my-4">
                <div class="position-relative">
                  <v-avatar size="64" class="primary">
                    <v-icon size="32" color="white">$calendarClock</v-icon>
                  </v-avatar>
                </div>
              </div>

              <!-- Content -->
              <h3 class="text-h6 font-weight-bold">
                {{ $t('No Available Slot') }}
              </h3>
              <p class="grey--text text--darken-1">
                {{
                  $t(
                    'Please try again later or go to the Services section to continue exploring. Thank you for your interest'
                  )
                }}
              </p>
            </v-card-text>
          </v-card>
        </v-container>
      </v-dialog>
    </v-container>
  </client-only>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  computed,
  useRouter,
  watch,
  onMounted,
  useStore,
  useRoute
} from '@nuxtjs/composition-api'
import { watchOnce } from '@vueuse/core'
import {
  useWorkflow,
  IStep,
  useProductOrder,
  createBpm
} from '../../repositories'
import ProductsGroup from '../internal/shared/products-group.vue'

// BE CAREFULL, THIS IS CORE OF STEP
export default defineComponent({
  // fetchOnServer: false,
  components: {
    ProductsGroup
  },

  setup() {
    const { app, $dayjs, $toast, i18n, $gtm } = useContext()
    const { commit } = useStore()
    // const { state }: any = useStore()
    const showDialog = ref<boolean>(false)
    const router = useRouter()
    const route = useRoute()
    const form = ref()
    const stepper = ref()

    // On Mounted
    onMounted(() => {
      const mainProductName = route.value?.params?.product
      commit('checkout/SET_MAIN_PRODUCT_NAME', mainProductName)
    })

    // STEP 1: loading skeleton
    // 1.1 body
    const currentStep = ref<IStep>({
      component: 'Skeleton',
      step: 1
    }) // 1.2 stepper
    const steps = ref<any>([{ step: 1, name: 'customize' }])
    // SET STEP META
    const setStepMeta = (data: any) => {
      const { step, meta } = data
      const modifiedAbility = steps.value[step - 1].meta?.modifiedAbility
      if (!modifiedAbility) return console.warn("This step meta can't modify")
      const prevState = steps.value[step - 1].meta
      steps.value[step - 1].meta = { ...prevState, ...meta }
    }
    // STEP 2
    // 2.1: load product workflow
    const productOption = computed(() => ({
      product: { slug: route.value.params.product }
    }))
    const paramStep = computed(() => route.value.params.step)

    // const getSpoken = computed(() => state?.checkout?.provider?.spoken || 0)

    const {
      order,
      orderItem,
      product,
      orderItems,
      setOrderItem,
      setOrderItemMeta,
      onProductSuccess,
      onProductError,
      onProductOrderSuccess,
      onProductOrderError,
      // execute,
      authenUser,
      updateOrder,
      loading: loadingProductOrder
    } = useProductOrder(productOption, { useFetch: true })

    // execute()
    const workflowOption = computed(() => ({
      key: product.value?.checkoutFlow?.name
    }))
    const { workflow } = useWorkflow(workflowOption)

    onProductSuccess((res) => {
      // Exception (product without workflow)
      if (!workflow.value) {
        return
      }
      // 2.2 push to the right step (move to step 1 if mismatched)
      if (
        paramStep.value &&
        !workflow.value.steps.find((i) => i.slug === paramStep.value)
      ) {
        router.replace({
          params: { ...route.value.params, step: undefined }
        })
      }

      const getSpokenProvider =
        res.results?.provider?.spoken?.some((item) => item !== 'vi') ?? false

      // STEP 3: load step
      // 3.1 load stepper
      steps.value = workflow.value?.steps
        .filter((step) => {
          return !(step.slug === 'choose-language' && !getSpokenProvider)
        })
        .map((step, index) => ({
          ...step,
          step: step.step || index + 1,
          name: step.content?.title || step.slug
        }))
        .sort((a, b) => a.step - b.step)
        .map((step, index) => ({ ...step, step: index + 1 }))

      // 3.2 show step component
      currentStep.value =
        steps.value.find((i: IStep) => i.slug === paramStep.value) ||
        steps.value[0]
    })

    onProductError(() => {
      if (!showDialog.value) showDialog.value = true
    })

    const bpmOption = ref({
      templateKey: 'checkout',
      uid: `checkout-${order.value?._id}`,
      user: authenUser.value?._id,
      customer: authenUser.value?._id,
      data: {
        order: order.value,
        provider: orderItems.value[0]?.product?.provider,
        user: authenUser.value,
        url: process.client ? window.location.href : undefined
      }
    })

    const {
      timeout,
      timeout2,
      execute: executeCreateBpm
      // onSucess: onSucessCreateBpm
    } = createBpm(bpmOption, {
      timeout: 1000 * 10
    })

    const onStepperChange = (data: any) => {
      clearTimeout(timeout.value)
      clearTimeout(timeout2.value)
      executeCreateBpm()
      const gtmOption = {
        event: `checkout-${currentStep.value.component}`,
        component: currentStep.value.component,
        userId: authenUser.value._id,
        ecommerce: {
          step: currentStep.value.step,
          currency: 'VND',
          value: order.value.total,
          coupon: order.value.voucher,
          items: orderItems.value.map((i) => ({
            item_name: i.product?.name,
            item_id: i.product?._id,
            item_brand: i.source,
            item_category: i.product?.sku,
            quantity: i.quantity,
            price: i.price,
            item_variant: i.meta?.medium
          }))
        }
      }
      $gtm?.push({ ecommerce: null })
      $gtm?.push(gtmOption)
      if (data.submit) {
        if (!order.value._id)
          return $toast.error(
            i18n.t("can't find your order please try again").toString()
          ) // DO NOT GO NEXT STEP IF THERE IS NO ORDER
        form.value
          .validate()
          .then((check: boolean) => {
            if (check) {
              form.value.submit()
              bpmOption.value.data.order = {
                ...order.value,
                orderItems: orderItems.value
              }
              currentStep.value = steps.value[data.step - 1]
            } else stepper.value.revertChange()
          })
          .catch((error) => app.$toast.error(error))
      } else {
        currentStep.value = steps.value[data.step - 1]
      }
    }

    // step 4: set query to meta data
    onProductOrderSuccess(() => {
      bpmOption.value.data.order = order.value
      bpmOption.value.data.provider = orderItems.value[0]?.product?.provider
      bpmOption.value.data.order = {
        ...order.value,
        orderItems: orderItems.value
      }
      bpmOption.value.uid = `checkout-${order.value._id}`
      executeCreateBpm()
      setOrderItem({
        title: product.value.name,
        description: product.value.content?.brief,
        image: product.value.content?.featuredImage
      })
      const orderTags = [
        `${
          orderItems.value.map((item) => item.product?.name).join(' - ') ||
          product.value?.provider?.name
        } - ${
          product.value?.content?.title ||
          orderItems.value.map((item) => item.product?.tags).join(' - ')
        }`,
        authenUser.value.phone,
        authenUser.value.name,
        product.value.provider?.name,
        product.value.content?.title
      ]

      const description = `${authenUser.value.name} (${
        authenUser.value.gender
      }) ${authenUser.value.phone} | Cập nhật lúc ${$dayjs(
        order.value.updatedAt
      ).format('HH:mm YYYY-MM-DD')}`
      updateOrder({ ...order.value, update: { tags: orderTags, description } })

      const defaultOrderItemMeta = {
        user: authenUser.value._id,
        ...(product.value.provider && { provider: product.value.provider }),
        fee: product.value.price
      }

      setOrderItemMeta({ ...route.value.query, ...defaultOrderItemMeta })
    })
    onProductOrderError(() => {
      if (!showDialog.value) showDialog.value = true
      // if (!showDialog.value && !workflow.value) showDialog.value = true
    })
    const onSubmit = setOrderItemMeta

    let completeStep = false
    let startLoading = false
    // wating api update order before complete
    const onComplete = () => {
      form.value.validate().then((check: boolean) => {
        if (check) {
          form.value.submit()
          // router.push('/order/' + order.value._id)
          // if (form.value.goToPayment()) {
          //   router.push('/order/' + order.value._id)
          // }
          completeStep = true
        } else stepper.value.revertChange()
      })
    }
    // if loading done, go to payment
    watch(loadingProductOrder, () => {
      if (loadingProductOrder.value) startLoading = true
      else if (!loadingProductOrder.value && startLoading && completeStep) {
        completeStep = false
        if (form.value?.goToPayment()) {
          if (order.value._id) router.push('/payment/order/' + order.value._id)
          else
            $toast.error(
              i18n.t("can't find your order please try again").toString()
            )
        }
      }
    })

    watchOnce(order, () => {
      if (!workflow.value) {
        steps.value = []
        router.push('/payment/order/' + order.value._id)
      }
    })

    return {
      steps,
      form,
      currentStep,
      stepper,
      onStepperChange,
      onSubmit,
      setOrderItem,
      workflow,
      order,
      orderItem,
      orderItems,
      onComplete,
      loadingProductOrder,
      setStepMeta,
      showDialog
    }
  }
})
</script>
