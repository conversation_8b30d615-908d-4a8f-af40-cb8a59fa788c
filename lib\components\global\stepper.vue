<template>
  <v-footer
    app
    fixed
    inset
    padless
    color="w-footer lighten-1 mt-4 font-weight-bold"
    :style="{ paddingBottom: $vuetify.breakpoint.mobile ? '20px' : '0' }"
  >
    <!-- <v-btn color="primary" @click="onNext"></v-btn>
    <v-stepper :value="selected" @change="onChange">
      <v-stepper-header>
        <div v-for="(step, i) in steps" :key="i">
          <v-stepper-step
            :step="step.step"
            :editable="step.step <= selected + 1"
            :complete="selected > step.step"
          >
            {{ step.name }}
          </v-stepper-step>
          <v-divider></v-divider>
        </div>
      </v-stepper-header>
    </v-stepper>
    <v-btn color="primary" @click="onNext"></v-btn> -->
    <v-container
      v-if="showStepper()"
      class="d-flex"
      :class="isMobile ? 'flex-column' : 'flex-row'"
    >
      <v-btn
        v-if="!isMobile && currStep.step !== 1"
        :disabled="currStep.step === 1"
        color="primary"
        outlined
        class="text-uppercase"
        rounded
        @click="onPrev"
      >
        <v-icon v-show="showArrowIcon"> $chervonLeft </v-icon>
        {{ $t('back') }}
      </v-btn>
      <div class="mx-auto d-flex flex-row justify-space-around">
        <!-- v-if="getStepsLength > 1" -->
        <w-progress-stepper
          v-if="getStepsLength >= 2"
          :steps="getStepsLength"
          :current-step="getCurrentStep"
          :divider-width="isMobile ? 50 : 70"
          :step-size="isMobile ? 18 : 20"
        />
        <!--
        <template v-for="(item, index) of steps">
          <v-chip
            :key="index"
            :small="isMobile"
            :color="currStep.step === index + 1 ? 'primary' : ''"
            class="mx-2"
          >
            {{ index + 1 }}
          </v-chip>
        </template>
        -->
      </div>
      <v-btn
        v-if="!isMobile"
        :loading="loadingNext"
        color="primary"
        :disabled="loadingNext || isDisabled"
        :style="{
          ...(isDisabled && {
            backgroundColor: 'rgba(0,0,0,0.12) !important',
            color: 'rgba(0,0,0,0.26) !important'
          })
        }"
        depressed
        rounded
        class="text-uppercase"
        @click="onNext"
      >
        {{ $t('next') }}
        <v-icon v-show="showArrowIcon"> $chevronRight</v-icon>
      </v-btn>
      <v-row v-if="isMobile" class="mt-2">
        <v-col v-if="currStep.step !== 1">
          <v-btn
            block
            :disabled="currStep.step === 1"
            color="primary"
            outlined
            rounded
            class="text-uppercase"
            @click="onPrev"
          >
            <v-icon v-show="showArrowIcon"> $chervonLeft </v-icon>
            {{ $t('back') }}
          </v-btn>
        </v-col>
        <v-col>
          <v-btn
            block
            :loading="loadingNext"
            color="primary"
            :disabled="loadingNext"
            :style="{
              ...(isDisabled && {
                backgroundColor: 'rgba(0,0,0,0.12) !important',
                color: 'rgba(0,0,0,0.26) !important'
              })
            }"
            depressed
            rounded
            class="text-uppercase"
            @click="onNext"
          >
            {{ $t('next') }}
            <v-icon v-show="showArrowIcon">$chevronRight</v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-footer>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useContext,
  useStore
} from '@nuxtjs/composition-api'
import type { ComputedRef, PropType } from '@nuxtjs/composition-api'

import { useEventBus } from '@wellcare/nuxt-module-data-layer/compositions'

interface IStep {
  step: number
  name: string
  complete?: boolean
  editable?: boolean
  meta?: any
}

export default defineComponent({
  props: {
    steps: {
      type: Array as PropType<IStep[]>,
      required: true
    },
    currStep: {
      type: Object,
      required: true
    },
    value: {
      type: Number,
      default: 1
    },
    loadingNext: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { registerEvent } = useEventBus()
    const { state }: any = useStore()

    const selected = ref(props.value)
    const { $vuetify, $config } = useContext()
    const showArrowIcon: ComputedRef<boolean> = computed(
      () => $config?.checkout?.UIcustomize?.stepper?.showArrowIcon ?? true
    )
    let prev = props.value
    const onChange = (step: number) => {
      prev = selected.value
      selected.value = step
      emit('change', selected.value)
    }
    const onNext = () => {
      prev = selected.value
      if (props.steps[prev]?.meta?.skip) selected.value += 2
      else selected.value++
      if (selected.value > props.steps.length) {
        emit('completed', {
          step: selected.value,
          submit: true
        })
      } else {
        emit('change', {
          step: selected.value,
          submit: true
        })
      }
    }
    const onPrev = ({ step }: { step?: number }) => {
      if (step) {
        selected.value = step
      } else {
        prev = selected.value
        let temp = prev
        if (props.steps[(temp -= 2)]?.meta?.skip) selected.value -= 2
        else selected.value--
        if (selected.value < 1) selected.value = 1
      }
      emit('change', {
        step: selected.value,
        submit: false
      })
    }
    const revertChange = () => {
      selected.value = prev
    }
    const isMobile = computed(() => $vuetify.breakpoint.smAndDown)
    const showStepper = () => {
      if (props.currStep.showStepper === undefined) return true
      return props.currStep.showStepper
    }
    const getStepsLength = computed(
      () => props.steps.filter((step) => !step.meta?.skip).length
    )
    const getCurrentStep = computed(() => {
      const prevStep = props.steps[props.currStep.step - 2]
      if (prevStep?.meta?.skip) return props.currStep.step - 1
      return props.currStep.step
    })
    const isDisabled = computed(
      () => state?.checkout?.buttonNextState?.isDisabled
    )

    onMounted(() => {
      registerEvent('preStep', onPrev)
    })

    return {
      selected,
      onNext,
      onPrev,
      onChange,
      revertChange,
      isMobile,
      showStepper,
      showArrowIcon,
      getStepsLength,
      getCurrentStep,
      isDisabled
    }
  }
})
</script>
<style scoped>
.v-btn {
  height: 44px !important;
}
</style>
