<template>
  <div class="d-flex align-center justify-center">
    <v-card
      :class="`${memberService.className} card-active-${activate} ma-2`"
      style="
        position: relative;
        font-size: 20px;
        font-weight: 700;
        background-size: cover;
        border-radius: 16px;
      "
      width="320"
      height="176"
      @click="$emit('chooseCard')"
    >
      <div
        style="position: absolute; opacity: 0.8; top: 20px; left: 20px"
        :style="memberService.titleColor"
      >
        {{ $t(`${memberService.label}`) }}
      </div>
      <div style="position: absolute; opacity: 0.8; top: 10px; right: 15px">
        <v-icon color="white" size="25">{{
          activate ? '$checkCircle' : '$circleOutline'
        }}</v-icon>
      </div>
      <div v-if="memberService.type === 'membership'">
        <div
          class="service-card__price flex flex-column"
          style="position: absolute; bottom: 65px; left: 20px; font-size: 14px"
        >
          <div v-if="asynchronousWalletInfo.isExist">
            {{ asynchronousWalletInfo.usable }}/{{
              asynchronousWalletInfo.balance
            }}
            {{ $t('time(s) of usage remaining') }}
          </div>
          <!-- <div v-if="personalWalletInfo.isExist">
            {{ personalWalletInfo.usable }}/{{ personalWalletInfo.balance }}
            {{ $t('minute(s) of usage remaining') }}
          </div> -->
        </div>
      </div>
      <div
        v-else
        class="service-card__price"
        style="position: absolute; bottom: 56px; left: 20px; font-weight: bold"
      >
        <span>{{ $n(getCashUsable) }} VND</span>

        <div
          style="
            width: 100%;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            margin-top: 4;
          "
          class="mt-1 mx-auto d-flex align-center justify-center"
        >
          <span class="mr-1" style="font-weight: 600">{{
            $t('Usable balance is valid in 6 months')
          }}</span>
          <v-tooltip top max-width="320" color="#f5f5f5" close-delay="4000">
            <template #activator="{ on, attrs }">
              <v-icon color="white" size="18" v-bind="attrs" v-on="on"
                >$informationOutline
              </v-icon>
            </template>
            <span class="black--text">
              {{
                $t(
                  'The remaining balance will be kept in your account and can be used within 6 months'
                )
              }}
            </span>
          </v-tooltip>
        </div>
      </div>

      <div
        style="
          position: absolute;
          bottom: 20px;
          left: 20px;
          right: 20px;
          font-size: 20px;
          color: #f1f1f1;
          font-weight: 500;
          text-transform: uppercase;
        "
        class="d-flex justify-space-between align-end"
      >
        <span
          :class="[checkLengthUsername]"
          :style="{
            maxWidth: memberService.type === 'membership' ? '180px' : '230px',
            lineHeight: '18px'
          }"
          >{{ user.name }}</span
        >
        <div
          v-if="memberService.type === 'membership'"
          class="d-flex flex-column justify-center align-end"
          style="font-size: 14px"
        >
          <span>
            {{ $t('Valid to') }}
          </span>
          <span style="line-height: 18px">
            {{ $dayjs(memberService.valid.end).format('DD/MM/YYYY') }}
          </span>
        </div>
      </div>
    </v-card>
  </div>
</template>
<script lang="ts">
import {
  ref,
  computed,
  defineComponent,
  useStore,
  reactive
} from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    memberService: {
      type: Object,
      default: () => {}
    },
    activate: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const walletData = [{}, {}]
    const { state }: any = useStore()
    const type = computed(() => props.memberService.type)
    const model = ref()
    const onCardChange = (value: any) => {
      emit('onCardChange', value)
    }
    const getCashUsable = computed(() => props.memberService?.usable)

    const user = computed(() => state?.authen?.user || '')
    const checkLengthUsername = computed(() => {
      return user.value.name.split(' ').length >= 4 ? 'text-16' : 'text-20'
    })

    const asynchronousWalletInfo = reactive({
      isExist: false,
      usable: 0,
      balance: 10
    })

    const personalWalletInfo = reactive({
      isExist: false,
      usable: 0,
      balance: 150
    })

    if (props.memberService?.type === 'membership') {
      const memberWallet = props.memberService
      memberWallet.child.forEach((el) => {
        switch (el.type) {
          case 'benefit-ask-doctor':
            asynchronousWalletInfo.isExist = true
            asynchronousWalletInfo.usable = el.usable
            asynchronousWalletInfo.balance = el.balance
            break
          case 'benefit-personal-doctor':
            personalWalletInfo.isExist = true
            personalWalletInfo.usable = el.usable
            personalWalletInfo.balance = el.balance
            break
          default:
            break
        }
      })
    }
    return {
      type,
      user,
      model,
      walletData,
      onCardChange,
      getCashUsable,
      personalWalletInfo,
      checkLengthUsername,
      asynchronousWalletInfo
    }
  }
})
</script>
<style scoped>
.service-card {
  background-image: url('../../static/wellcare-service-card.png');
}
.member-card {
  background-image: url('../../static/wellcare-member-card.png');
}
.service-card__price {
  background: linear-gradient(
    314deg,
    #fff 17.07%,
    rgba(255, 255, 255, 0.54) 115.4%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.member-card__price {
  background: linear-gradient(
    314deg,
    #fff 17.07%,
    rgba(255, 255, 255, 0.54) 115.4%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-active-true {
  transform: scale(1);
}

.card-active-false {
  transform: scale(0.95);
}
</style>
