<script lang="ts">
import {
  useStore,
  defineComponent,
  onMounted,
  watch
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'
import { IProvider } from '@wellcare/nuxt-module-data-layer'
import { useProviderSlotCheck } from '../../../composables'

export default defineComponent({
  props: {
    provider: {
      type: Object as PropType<IProvider>,
      default: () => ({})
    }
  },
  setup(props) {
    const { commit } = useStore()
    const { alertTimeslot, provider, checkProviderSlots } =
      useProviderSlotCheck(props.provider.slug)

    watch(provider, () => {
      commit('checkout/updateField', {
        path: 'provider',
        value: provider.value
      })
    })

    watch(
      () => props.provider,
      (val) => {
        console.log(val)
      },
      {
        immediate: true
      }
    )

    onMounted(() => {
      checkProviderSlots()
    })

    return {
      alertTimeslot,
      checkProviderSlots
    }
  }
})
</script>

<template>
  <v-dialog v-model="alertTimeslot" max-width="350">
    <v-card rounded="lg">
      <v-card-title class="justify-center h6 d-flex flex-column align-center">
        <div class="warning-icon mt-3">
          <v-icon color="primary" x-large> $messageText </v-icon>
        </div>
      </v-card-title>
      <v-card-text>
        <h3
          v-dompurify-html="
            $t('today schedule of doctor is not fixed', {
              doctor: provider ? provider.name : ''
            })
          "
          class="text--primary text-center text-break mt-2"
        ></h3>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
