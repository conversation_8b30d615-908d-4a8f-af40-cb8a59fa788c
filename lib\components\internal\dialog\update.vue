<template>
  <div>
    <v-bottom-sheet
      v-if="isMobile"
      :value="value"
      :fullscreen="true"
      :persistent="persistent"
    >
      <v-card
        :tile="fullScreen"
        :class="{
          'bt-sheet': !fullScreen
        }"
      >
        <div
          class="white--text d-flex align-center justify-space-around"
          :style="{
            height: 50 + safeArea.top + 'px',
            paddingTop: safeArea.top + 'px',
            backgroundColor: 'var(--v-primary-base)'
          }"
          dense
        >
          <v-btn
            v-show="isShowBtnClose"
            class="ml-n1"
            small
            icon
            dark
            absolute
            left
            @click="onCancel"
          >
            <v-icon>$close</v-icon>
          </v-btn>
          <p class="text-center mb-0 w-title white--text text-md-title">
            {{ $t(title) }}
          </p>
          <slot name="btn:update:secondary">
            <v-btn
              v-show="showSaveBtn"
              absolute
              style="background-color: #006f6466"
              right
              depressed
              :loading="loading"
              class="none-uppercase white--text px-5 mr-n1 rounded-lg"
              :disabled="!canUpdate || loading"
              @click="onUpdated"
            >
              <slot name="btn:update:secondary">
                {{ $t('save') }}
              </slot>
            </v-btn>
          </slot>
        </div>
        <v-card class="overflow-y-auto" height="90vh" flat tile>
          <slot></slot>
          <v-card-actions class="px-4 pb-4">
            <slot name="btn:cancel"> </slot>
            <slot name="btn:update"> </slot>
          </v-card-actions>
          <slot name="loading">
            <w-loading
              v-model="loadingDialog"
              :size-loading="2"
              absolute
            ></w-loading>
          </slot>
        </v-card>
      </v-card>
    </v-bottom-sheet>
    <v-dialog
      v-else
      :value="value"
      :persistent="persistent"
      transition="dialog-bottom-transition"
      v-bind="{ ...defaultAttrs, ...$attrs }"
    >
      <v-card rounded="lg" height="85vh">
        <v-toolbar
          flat
          color="primary"
          class="w-toolbar white--text"
          :style="{
            paddingTop: safeArea.top + 'px'
          }"
        >
          <v-btn
            v-show="isShowBtnClose"
            icon
            dark
            absolute
            left
            @click="onCancel"
          >
            <v-icon size="24">$close</v-icon>
          </v-btn>
          <p class="text-center mb-0 w-title title">
            {{ $t(title) }}
          </p>
          <v-btn icon dark absolute right @click="onCancel">
            <v-icon size="24">$information-outline</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card class="overflow-y-auto" elevation="0" tile>
          <slot></slot>
          <v-card-actions class="px-4 pb-4">
            <v-spacer></v-spacer>
            <slot name="btn:cancel">
              <v-btn
                v-if="showCancelBtn"
                text
                class="none-uppercase primary--text px-5"
                @click="onCancel"
              >
                {{ $t('cancel') }}
              </v-btn>
            </slot>
            <slot name="btn:update">
              <v-btn
                v-show="showSaveBtn"
                color="primary"
                depressed
                :loading="loading"
                :block="!showCancelBtn"
                :disabled="!canUpdate || loading"
                class="none-uppercase px-5"
                @click="onUpdated"
              >
                {{ $t('update') }}
              </v-btn>
            </slot>
          </v-card-actions>
          <slot name="loading">
            <w-loading
              v-model="loadingDialog"
              :size-loading="2"
              absolute
            ></w-loading>
          </slot>
        </v-card>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useStore,
  watch
} from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    fullScreen: {
      type: Boolean,
      default: false
    },
    canUpdate: {
      type: Boolean,
      default: false
    },
    showCancelBtn: {
      type: Boolean,
      default: false
    },
    showSaveBtn: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    persistent: {
      type: Boolean,
      default: false
    },
    isShowBtnClose: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const { state } = useStore()
    const { $vuetify } = useContext()
    const loadingDialog = ref<boolean>(props?.loading)
    const isMobile = computed(() => $vuetify.breakpoint.width < 600)
    const defaultAttrs = computed(() => ({ ...props }))

    watch(
      () => props.loading,
      () => {
        loadingDialog.value = props.loading
      }
    )

    const safeArea = computed(() => state?.app?.safeArea || { top: 0 })

    const onUpdated = (data: any) => {
      emit('updated', data)
    }

    const onCancel = () => {
      emit('cancel', false)
    }

    return {
      onUpdated,
      onCancel,
      loadingDialog,
      isMobile,
      defaultAttrs,
      safeArea
    }
  }
})
</script>
<style scoped>
div.bt-sheet {
  border-radius: 8px 8px 0 0 !important;
}

div >>> div.v-dialog.v-bottom-sheet {
  box-shadow: none !important;
  width: 100%;
}

.w-toolbar {
  position: relative;
  height: 56px !important;
}
.w-toolbar >>> .v-toolbar__content {
  height: 56px !important;
}
.w-toolbar >>> .w-title {
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(100% - 2 * 48px);
  transform: translate(-50%, -50%);
  font-weight: bold;
}

@media screen and (max-width: 600px) {
  .w-toolbar >>> .w-title {
    font-size: 0.95rem;
  }
}
</style>
