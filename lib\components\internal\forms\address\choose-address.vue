<template>
  <v-container class="text--secondary">
    <h3>
      {{
        $t('saved address') +
        ' (' +
        (response && response.total ? response.total : 0) +
        ')'
      }}
    </h3>
    <p v-if="listAddress.length === 0" class="grey--text mt-2">
      {{ $t('no data') }}
    </p>
    <div v-else>
      <v-overlay absolute :value="loading">
        <v-progress-circular indeterminate />
      </v-overlay>
      <v-radio-group
        ref="listAddressEl"
        v-model="selectedAddress"
        style="max-height: 350px; overflow-y: auto"
      >
        <v-radio
          v-for="(item, index) in listAddress"
          :key="index"
          :value="index"
          :label="item.line1 + ', ' + item.province"
        >
          <template #label>
            <div class="d-flex" style="width: 100%">
              <div class="text-break text-wrap flex-grow-1">
                {{ item.line1 }}, {{ item.province }}
              </div>
              <v-menu>
                <template #activator="{ on, attrs }">
                  <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon>$dotsVertical</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="handlePickerAddress('edit', index)">
                    <v-list-item-title>{{ $t('edit') }}</v-list-item-title>
                  </v-list-item>
                  <v-list-item @click="handlePickerAddress('remove')">
                    <v-list-item-title class="red--text">{{
                      $t('remove')
                    }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-radio>
      </v-radio-group>
      <!-- <v-btn
        v-if="listAddress.length < response.total"
        outlined
        @click="loadMore"
      >
        {{ $t('more') }}
      </v-btn> -->
    </div>
    <div>
      <v-btn
        color="primary"
        class="mt-3 mx-auto"
        width="300"
        @click="handlePickerAddress('add')"
      >
        {{ $t('add new address') }}
      </v-btn>
    </div>
    <v-dialog v-model="dialog" width="450" :fullscreen="isMobile">
      <address-picker
        v-model="dialog"
        :type="typePicker"
        :data="dataPicker"
        @saveAddress="saveAddress"
        @editAddress="editAddress"
        @removeAddress="removeAddress"
        @cancelAddress="dialog = false"
      />
    </v-dialog>
  </v-container>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  useContext,
  computed,
  useStore
} from '@nuxtjs/composition-api'
import { useScroll } from '@vueuse/core'
import { Store } from 'vuex'
import {
  getUserAddress,
  createUserAddress,
  updateAddress,
  removeAddress as removeAddressReq,
  ISearchOption
} from '../../../../repositories'
import addressPicker from '../../input/address-picker.vue'
// import { ISearchOption } from "lib/repositories/compositions/search-option.interface";

export default defineComponent({
  components: {
    addressPicker
  },
  setup(_, { emit }) {
    const { $vuetify } = useContext()

    const listAddressEl = ref<any>(null)
    const { arrivedState } = useScroll(listAddressEl)
    const isMobile = computed(() => {
      return $vuetify.breakpoint.smAndDown
    })

    const userId = computed(() => store.state?.checkout.order?.deliverTo)

    const dialog = ref<boolean>(false)

    const typePicker = ref<string>('')
    const dataPicker = ref<any>({})

    const store: Store<any> = useStore()

    const options = ref<ISearchOption>({
      limit: 10
    })
    const {
      results: listAddress,
      response,
      execute: getListAddress,
      loading
    } = getUserAddress(options)
    const selectedAddress = ref<number>(0)

    const dataPickerAction = ref<any>({})

    const { execute: addAddress, onSucess: createSucess } =
      createUserAddress(dataPickerAction)
    const saveAddress = (data: any) => {
      dataPickerAction.value = data
      dataPickerAction.value.user = userId.value
      addAddress()
      dialog.value = false
    }
    createSucess(() => {
      refreshListAddress()
    })
    const { execute: updateAddressAction, onSucess: updateSucess } =
      updateAddress(dataPickerAction)
    const editAddress = (data: any) => {
      dataPickerAction.value = data
      updateAddressAction()
      dialog.value = false
    }
    updateSucess(() => {
      refreshListAddress()
    })
    const removeAddressRep = ref<any>({})
    const { execute: removeAddressAction, onSucess: removeSuccess } =
      removeAddressReq(removeAddressRep)
    const removeAddress = (data: any) => {
      removeAddressRep.value.id = data?._id
      removeAddressAction()
      dialog.value = false
    }
    removeSuccess(() => {
      refreshListAddress()
    })

    const handlePickerAddress = (type: string, index: number = -1) => {
      typePicker.value = type
      switch (type) {
        case 'add':
          dataPicker.value = {
            line1: '',
            province: ''
          }
          dialog.value = true
          break
        case 'edit':
          if (index !== -1) {
            dataPicker.value = listAddress.value[index]
            selectedAddress.value = index
          } else {
            dataPicker.value = listAddress.value[selectedAddress.value]
          }
          dialog.value = true
          break
        case 'remove':
          removeAddress(listAddress.value[selectedAddress.value])
          dialog.value = false
          break
        default:
      }
    }
    const refreshListAddress = () => {
      getListAddress()
      selectedAddress.value = 0
    }
    const loadMore = () => {
      options.value.limit = (options.value?.limit ?? 5) + 5
      getListAddress()
    }
    watch(selectedAddress, () => {
      emit('update-address', listAddress.value[selectedAddress.value])
    })
    watch(listAddress, () => {
      if (listAddress.value.length > 0) {
        emit('update-address', listAddress.value[0])
      } else {
        emit('update-address', null)
      }
    })
    watch(arrivedState, (val) => {
      if (
        val.bottom &&
        !loading.value &&
        listAddress.value.length < response.value.total
      ) {
        loadMore()
      }
    })
    getListAddress()
    return {
      listAddress,
      response,
      selectedAddress,
      isMobile,
      handlePickerAddress,
      dialog,
      saveAddress,
      editAddress,
      removeAddress,
      dataPicker,
      typePicker,
      loading,
      loadMore,
      listAddressEl
    }
  }
})
</script>
