<template>
  <v-container class="text--secondary">
    <h3>{{ $t('reciever') }}</h3>
    <v-radio-group v-model="selectReceiver">
      <v-radio value="default" :label="defaultReceiverLabel" />
      <v-radio value="other" :label="$t('other')" />
    </v-radio-group>
    <v-expand-transition>
      <v-form
        v-show="selectReceiver === 'other'"
        ref="formChooseOther"
        v-model="isValidForm"
      >
        <v-text-field
          v-model="otherUser.name"
          outlined
          dense
          :label="$t('name')"
          :rules="[(v) => !!v || $t('required field')]"
        />
        <v-text-field
          v-model="otherUser.phone"
          outlined
          dense
          :label="$t('phone number')"
          :rules="[
            (v) => !!v || $t('required field'),
            (v) => vaildatePhone(v) || $t('invalid phone')
          ]"
        />
      </v-form>
    </v-expand-transition>
  </v-container>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  useStore,
  watch
} from '@nuxtjs/composition-api'
// import { useOrder } from '../../../../repositories'
import { Store } from 'vuex'

export default defineComponent({
  setup(_, { emit }) {
    const store: Store<any> = useStore()
    // const orderStore = useOrder()
    const defaultUser = computed<{ name: string; phone: string }>(
      (): {
        name: string
        phone: string
      } => {
        return {
          name: store.state?.authen?.user?.name,
          phone: store.state?.authen?.user?.phone
        }
      }
    )
    const otherUser = reactive({
      name: '',
      phone: ''
    })

    const defaultReceiverLabel = computed<string>(() =>
      [defaultUser.value.name, defaultUser.value.phone].join(', ')
    )
    const selectReceiver = ref<string>('default')

    const isValidForm = ref<boolean>(false)
    const formChooseOther = ref<any>(null)

    const vaildatePhone = (phoneValue: string) => {
      if (phoneValue === '') return false
      const phoneRegex = /^(0|\+84)\d{9}$/
      return phoneRegex.test(phoneValue)
    }

    const validateOtherReiver = () => {
      if (selectReceiver.value === 'other') {
        formChooseOther.value?.validate()
        return isValidForm.value
      }
      return true
    }

    const updateOtherReceiver = () => {
      // formChooseOther.value?.validate()
      if (!vaildatePhone(otherUser.phone) || !otherUser.name) {
        emit('update-receiver', {
          name: null,
          phone: null
        })
      } else {
        emit('update-receiver', {
          name: otherUser.name,
          phone: otherUser.phone
        })
      }
    }

    watch(
      () => selectReceiver.value,
      (val) => {
        if (val === 'default') {
          emit('update-receiver', {
            name: defaultUser.value.name,
            phone: defaultUser.value.phone
          })
          // isSavedOther.value = false;
        } else {
          updateOtherReceiver()
        }
      }
    )

    watch(otherUser, () => {
      updateOtherReceiver()
    })

    onMounted(() => {
      emit('update-receiver', {
        name: defaultUser.value.name,
        phone: defaultUser.value.phone
      })
    })
    return {
      selectReceiver,
      vaildatePhone,
      defaultReceiverLabel,
      otherUser,
      isValidForm,
      formChooseOther,
      updateOtherReceiver,
      validateOtherReiver
    }
  }
})
</script>
