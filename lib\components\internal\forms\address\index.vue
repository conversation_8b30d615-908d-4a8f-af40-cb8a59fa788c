<template>
  <v-container fluid class="fill-height pa-0">
    <h3 class="headline text--secondary title-dark font-weight-bold">
      {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
    </h3>
    <choose-receiver
      ref="chooseRecieverComponent"
      @update-receiver="updateReceiver"
    />
    <choose-address @update-address="updateAddress" />
    <v-container v-show="error" class="red--text shake">{{
      $t(error)
    }}</v-container>
  </v-container>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  useRoute
} from '@nuxtjs/composition-api'
import chooseReceiver from './choose-receiver.vue'
import chooseAddress from './choose-address.vue'

export default defineComponent({
  // name: "home-delivery",
  components: {
    chooseReceiver,
    chooseAddress
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  emits: ['on-receiver-medicine'],
  setup(_props, { emit }) {
    const route = useRoute()

    const chooseRecieverComponent = ref<any>(null)

    const error = ref<string>('')

    const state = reactive({
      name: '',
      phone: '',
      address: {
        province: '',
        line1: ''
      } as { province: string; line1: string } | null,
      deliveryTypes: 'homeDelivery',
      orgName: 'longChau',
      consultation: ''
    })
    const updateAddress = (data: any) => {
      if (data === null) {
        state.address = null
        return
      }
      state.address = {
        province: data?.province,
        line1: data?.line1
      }
    }

    const updateReceiver = (data: any) => {
      state.name = data.name
      state.phone = data.phone
    }
    const touch = () => {
      error.value = ''
      const consultation = route.value.query?.consultation
      if (!consultation) {
        error.value = 'missing consultation info'
        setTimeout(() => {
          error.value = ''
        }, 2500)
        return false
      }
      if (state.address) {
        const address = state.address
        if (!address?.province || !address?.line1) {
          error.value = 'please fill in the address'
          setTimeout(() => {
            error.value = ''
          }, 2500)
          return false
        }
      } else {
        error.value = 'please fill in the address'
        setTimeout(() => {
          error.value = ''
        }, 2500)
        return false
      }
      const validateReciever =
        chooseRecieverComponent.value.validateOtherReiver()
      if (!state.phone || !state.name || !validateReciever) {
        return false
      }
      return true
    }
    const submit = () => {
      state.consultation = route.value.query?.consultation as string
      emit('submit', state)
    }
    return {
      updateAddress,
      updateReceiver,
      submit,
      state,
      touch,
      chooseRecieverComponent,
      error
    }
  }
})
</script>
<style scoped>
.shake {
  animation: shake 1s;
}
@keyframes shake {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(5px);
  }
  50% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(5px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
