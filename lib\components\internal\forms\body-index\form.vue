<template>
  <v-card elevation="0" color="transparent">
    <v-form :key="key" ref="formRef" v-model="valid" @submit="submit">
      <v-row>
        <v-col cols="12">
          <w-number-input
            suffix="kg"
            :label="$t('weight')"
            :rules="[...rules.require]"
            :range="[0, 250]"
            :default-val="Number(weight)"
            :increment="0.5"
            @change="(v) => onChangeNumber({ value: v, type: 'weight' })"
          />
        </v-col>
        <v-col cols="12">
          <w-number-input
            suffix="cm"
            :label="$t('height')"
            :range="[0, 250]"
            :default-val="Number(height)"
            :increment="0.5"
            @change="(v) => onChangeNumber({ value: v, type: 'height' })"
          />
        </v-col>

        <v-col cols="12">
          <w-number-input
            suffix="cm"
            :label="$t('headCircumference')"
            :range="[0, 60]"
            :default-val="Number(headCircumference)"
            :increment="0.5"
            @change="
              (v) => onChangeNumber({ value: v, type: 'headCircumference' })
            "
          />
        </v-col>
      </v-row>
    </v-form>

    <w-loading v-model="loading" absolute :size-loading="2" />
  </v-card>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  onMounted,
  reactive,
  ref,
  toRefs,
  useContext,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import { useObservation } from '../../../../repositories'
import { useRerender } from '../../../../composables'
import { calcConvertUnitValue, CONVERT } from '../../../../utils'

export interface IBodyIndex {
  height: string
  weight: string
  headCircumference: string
}

export default defineComponent({
  name: 'BodyIndex',
  props: {
    label: {
      type: String,
      default: ''
    },
    orderItem: {
      type: Object,
      required: true
    },
    observedAt: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { $vuetify, i18n, $dayjs } = useContext()
    $dayjs.extend(relativeTime)
    $dayjs.extend(utc)

    // const inputs = [
    //   {
    //     key: 'weight',
    //     label: 'weight',
    //     suffix: 'kg',
    //     rules:[],
    //     defaultValue: 0
    //   }
    // ]

    const { commit } = useStore()
    const { resObs, executeSearch, executeImportCreate, loading } =
      useObservation()
    const { key, refresh } = useRerender()

    const formRef = ref()
    const valid = ref<boolean>(false)
    const name = ref<string>(props.label)
    const year = ref<number>(1)
    const notChanged = ref<boolean>(true)

    const index = reactive<IBodyIndex>({
      height: '',
      weight: '',
      headCircumference: ''
    })

    const babyId = computed<string>(
      () =>
        props.orderItem?.meta?.patientInfo?._id ||
        props.orderItem?.meta?.baby_info?._id ||
        ''
    )

    const dense = computed(() => $vuetify.breakpoint.mobile)
    const observedAt = computed<string>(() => props.observedAt)
    const rules = computed(() => ({
      require: (v: number) => !!v || i18n.t('required')
    }))

    const submit = async () => {
      formRef.value.validate()

      if (!valid.value) {
        return false
      } else if (notChanged.value) {
        return true
      } else if (index.weight) {
        try {
          const data = Object.keys(index)
            .filter((item) => index[item]) // Filter out undefined items
            .map((item) => ({
              key: item,
              name: CONVERT[item].name,
              observedAt: props.observedAt,
              unit: CONVERT[item].unit,
              user: babyId.value,
              value: index[item]
            }))
          const res = await executeImportCreate(data)

          if (res.code === 200) {
            notChanged.value = true
            return valid.value
          } else {
            return false
          }
        } catch (error) {
          console.error(error)
          return false
        }
      } else {
        return false
      }
    }

    const onChangeNumber = (val: { value: string; type: string }) => {
      index[val.type] = val.value
    }

    // Formatting function to get 'DD/MM/YYYY' string
    const formatDate = (date) =>
      $dayjs(date).utc().startOf('day').format('DD/MM/YYYY')

    watch(index, () => {
      notChanged.value = false
    })

    onBeforeMount(async () => {
      try {
        // // init reset state
        index.weight = ''
        index.height = ''
        index.headCircumference = ''

        await executeSearch({
          filter: JSON.stringify({
            user: babyId.value,
            name: [
              'Person Height',
              'Person Weight',
              'Person Head Circumference'
            ]
          }),
          limit: 1,
          sort: props.type === 'newborn' ? 'observedAt' : '-observedAt'
        })
        const code = resObs.value?.code
        const results: any[] = resObs.value?.results

        if (code === 200 && results?.length) {
          for (const i of results) {
            const itemDate = formatDate(i?.observedAt)
            const compareDate = formatDate(observedAt.value)
            if (itemDate === compareDate) {
              index[i?.key] = calcConvertUnitValue({
                unit: i?.unit,
                value: i?.value
              })
            }
          }

          refresh()

          setTimeout(() => {
            notChanged.value = true
          }, 100)
        } else {
          notChanged.value = false
        }
      } catch (error) {
        console.error(error)
      }
    })

    onMounted(() => {
      if (props.type === 'newborn') {
        commit('checkout/SET_SUMMARIZE_NEWBORN', formatDate(props?.observedAt))
      } else {
        commit('checkout/SET_SUMMARIZE_PRESENT', formatDate(props?.observedAt))
      }
    })
    return {
      key,
      dense,
      valid,
      name,
      formRef,
      rules,
      submit,
      onChangeNumber,
      year,
      loading,
      refresh,
      ...toRefs(index)
    }
  }
})
</script>
