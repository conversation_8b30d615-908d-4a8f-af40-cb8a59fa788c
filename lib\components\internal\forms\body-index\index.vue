<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  onMounted,
  provide,
  ref,
  useContext,
  useStore
} from '@nuxtjs/composition-api'
import { useRerender } from '../../../../composables'
import { IStepperItems, IUser } from '../../../../models'

import StepperValidated from '../../shared/stepper-validated.vue'
import AvatarChangePatient from '../../shared/avatar-change-patient.vue'

export default defineComponent({
  name: 'MedicalHistory',
  components: { StepperValidated, AvatarChangePatient },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, { emit }) {
    const { $dayjs } = useContext()
    const { commit } = useStore()
    const { key, rerenderSrc } = useRerender()

    const stepperValidatedRef = ref()

    const babyInfo = computed<IUser>(
      () =>
        props.orderItem?.meta?.patientInfo || props.orderItem?.meta?.baby_info
    )

    const items = computed<IStepperItems[]>(() => [
      {
        key: 'newborn',
        label: 'newborn',
        component: 'step-body-index',
        required: true,
        isFirstOpen: true,
        props: {
          observedAt: babyInfo.value?.dob,
          require: ['height', 'weight', 'headCircumference']
        }
      },
      {
        key: 'present',
        label: 'present',
        component: 'step-body-index',
        required: true,
        props: {
          observedAt: $dayjs().toISOString(),
          require: ['height', 'weight', 'headCircumference']
        }
      }
    ])

    rerenderSrc({
      source: () => babyInfo.value?._id,
      onChanged: (newVal, oldVal) => {
        if (newVal !== oldVal) {
          // console.log('change')
        }
      }
    })

    const next = () => {
      return stepperValidatedRef.value?.next()
    }

    const back = () => {
      stepperValidatedRef.value?.back()
    }

    const touch = () => {
      return next()
    }

    const submit = () => {
      emit('submit', {})
    }

    onBeforeMount(() => {
      provide('babyInfo', babyInfo)
    })

    onMounted(() => {
      commit('checkout/SET_ENABLE_BTN_NEXT')
    })

    return {
      key,
      next,
      back,
      touch,
      items,
      submit,
      babyInfo,
      stepperValidatedRef
    }
  }
})
</script>

<template>
  <v-row no-gutters>
    <v-col cols="12" class="mb-n2">
      <div class="d-flex justify-space-between align-center">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
        </h3>
        <AvatarChangePatient :key="key" :patient="babyInfo" />
      </div>
    </v-col>

    <v-col cols="12">
      <StepperValidated
        ref="stepperValidatedRef"
        :key="key"
        :items="items"
        :order="order"
        :order-item="orderItem"
      />
    </v-col>
  </v-row>
</template>
