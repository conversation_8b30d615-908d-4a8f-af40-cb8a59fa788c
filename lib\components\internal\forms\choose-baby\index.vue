<template>
  <v-row :key="'choose-patient-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
      <h4 class="body-1 text--secondary">
        {{ $t('program applies to children under 6 years old') }}
      </h4>
    </v-col>
    <patient-list
      ref="patientListRef"
      :show-dialog="showDialog"
      @patient="selectPatient"
      @existed-consultation="isExisted = $event"
      @on-close="showDialog = false"
    />
    <h5 class="body-1 mb-2 mt-1 text--secondary">
      {{ $t(currentStep.content.description) }}
    </h5>
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  useContext,
  useStore,
  ref
} from '@nuxtjs/composition-api'
import type { PropType, Ref } from '@nuxtjs/composition-api'

import { IStep } from '../../../../repositories/workflow/flow.interface'
import PatientList from './patient-list.vue'
export default defineComponent({
  components: {
    PatientList
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(_, { emit }) {
    // DECLARE VARIABLE
    const { state: storeState } = useStore()

    const state = reactive({
      baby_date: '',
      baby_id: '',
      baby_info: {},
      user: (storeState as any).authen.user._id
    })
    const { $toast, i18n } = useContext()
    const isExisted: Ref<boolean> = ref(false)
    const showDialog: Ref<boolean> = ref(false)
    const patientListRef = ref()
    // LOGIC

    const submit = () => {
      // console.log(state, ' line 55')
      if (!state.baby_id || JSON.stringify(state.baby_info) === '{}') {
        $toast?.global?.appError({
          message: i18n.t('please choose patient')
        })
        return
      }

      emit('submit', state)
    }
    const selectPatient = (data) => {
      state.baby_id = data.patient
      state.baby_info = data.patientInfo
      state.baby_date = data.patientInfo.dob
    }
    const touch = () => {
      // Allow to book additional consultations even if have already had another consultation
      // if (isExisted.value) {
      //   showDialog.value = true
      //   return false
      // }
      if (!state.baby_id) {
        $toast?.global?.appError({
          message: i18n.t('please choose patient')
        })
        return false
      }

      if (!(state.baby_info as any)?.avatar?.url) {
        patientListRef.value?.requireAvatar()

        return false
      }
      return true
    }

    return {
      patientListRef,
      state,
      submit,
      selectPatient,
      touch,
      isExisted,
      showDialog
    }
  }
})
</script>

<style scoped></style>
