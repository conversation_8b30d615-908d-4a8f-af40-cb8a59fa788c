<template>
  <w-form-user
    ref="formUser"
    :relate-id="relateId"
    :show-relationship="isShowRelationship"
    :default-data="defaultData"
    require-avatar
    :w-attrs-date-field="{
      max: $dayjs().toISOString(),
      min: $dayjs().subtract(5, 'year').toISOString()
    }"
    :btn-submit="{ text: 'save' }"
    :relationships="relationships"
    @on:submit-form="onSubmitForm"
    @on:submit-success="onSubmitFormSuccess"
  />
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { useUser } from '@wellcare/nuxt-module-account/repositories'
import { IUser } from '../../../../../models/user'

export default defineComponent({
  props: {
    textNote: {
      type: String,
      default: ''
    },
    persistent: {
      type: <PERSON>olean,
      default: false
    },
    isShowBtnClose: {
      type: Boolean,
      default: true
    },
    typeActionForm: {
      type: String as PropType<'create' | 'update' | 'delete'>,
      default: 'create'
    },
    defaultData: {
      type: Object as PropType<IUser>,
      default: () => ({} as IUser)
    }
  },
  setup({ typeActionForm, defaultData }, { emit }) {
    // DECLARE
    const { i18n } = useContext()
    const { user } = useUser()

    const formUser = ref<any>(null)
    const relationships = ref<any[]>([
      { text: i18n.t('child'), value: 'child' }
    ])

    const relateId = computed(() => user.value._id)
    const loading = computed<boolean>(() => formUser.value?.loadingForm)

    const onSubmitForm = () => {
      formUser.value.submitFormDataFn(typeActionForm)
    }

    const onSubmitFormSuccess = () => {
      emit('user-added', 'add')
    }

    const isShowRelationship = computed<boolean>(
      () => !(defaultData.userId === relateId.value)
    )

    return {
      relateId,
      formUser,
      loading,
      onSubmitForm,
      onSubmitFormSuccess,
      isShowRelationship,
      relationships
    }
  }
})
</script>

<style scoped>
label:focus-within {
  color: var(--v-primary-base);
}

label span:first-child {
  padding-left: 8px;
}

.w-label__required::after {
  content: '*';
  color: red;
}

.w-label__required:first-letter {
  text-transform: uppercase;
}

.v-btn.white--text.primary.v-btn--active >>> .v-icon {
  color: white;
}
</style>
