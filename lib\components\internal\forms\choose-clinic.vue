<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col v-for="(clinic, index) of clinics" :key="index" cols="12" sm="6">
      <v-card
        class="pb-3 rounded-lg d-flex flex-column w-card pa-4"
        :class="selectedIndex === index ? 'active-card' : ''"
        hover
        @click="chooseClinic(clinic, index)"
      >
        <div class="flex-row d-flex align-start flex-grow-1">
          <div
            class="pr-2 d-flex flex-column justify-space-between flex-shrink-1 flex-grow-1"
          >
            <v-card-title
              class="flex-row pb-1 pa-0 mt-n1 flex-nowrap align-content-center"
            >
              <v-icon color="primary">
                {{
                  selectedIndex === index ? '$checkCircle' : '$circleOutline'
                }}
              </v-icon>
              <span
                class="ml-2 title font-weight-medium"
                style="word-break: break-word"
              >
                {{ $t(clinic.name) }}
              </span>
              <v-spacer />
            </v-card-title>
            <v-card-text class="pa-0 text--secondary">
              {{ clinic.location.street1 }}
            </v-card-text>
          </div>
          <div class="flex-shrink-0 w-icon-square-area">
            <v-icon>$mapMarker</v-icon>
          </div>
        </div>
      </v-card>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  useContext,
  watch,
  watchEffect
} from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useProductOrder, useProviderTimeslots } from '../../../repositories'

export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    // declare variable
    const { orderItem } = useProductOrder()
    const { provider, executeSearchProvider } = useProviderTimeslots(
      computed(() => ({
        slug: props.orderItem.product.provider.slug
      }))
    )
    const clinics: ComputedRef<any[]> = computed(() => provider.value.location)
    const selectedIndex: Ref<number> = ref(-1)
    const stateData = reactive({
      location: null
    })
    const { $toast, i18n } = useContext()
    // logic
    const chooseClinic = (clinic, index) => {
      selectedIndex.value = index
      stateData.location = clinic._id
    }
    const getSelected = () => {
      if (orderItem.value.meta?.location) {
        selectedIndex.value = clinics.value.findIndex(
          (c) => c._id === orderItem.value.meta?.location
        )
      }
    }
    const touch = () => {
      if (!stateData.location || selectedIndex.value === -1) {
        $toast.error(i18n.t('please choose clinic'))
        return false
      }
      return true
    }
    const submit = () => {
      emit('submit', stateData)
    }
    watch(
      () => clinics,
      () => {
        getSelected()
      }
    )
    // first load
    executeSearchProvider()
    watchEffect(() => {
      getSelected()
    })
    return {
      clinics,
      selectedIndex,
      chooseClinic,
      touch,
      submit
    }
  }
})
</script>
<style scoped>
.active-card {
  border: 2px solid var(--v-primary-base);
  /* background-color: var(--v-primary-lighten5); */
  background-color: #00968710;
}
</style>
