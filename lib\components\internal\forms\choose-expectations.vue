<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useRoute,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IOrderItem, IStep } from '../../../repositories'
import { mentalHealth } from '../../../utils'

export default defineComponent({
  props: {
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem<any>>,
      default: () => ({} as IOrderItem<any>),
      required: true
    }
  },
  setup({ orderItem }, { emit }) {
    const { i18n, $toast } = useContext()
    const { commit } = useStore()
    const route = useRoute()

    const expectationsCheckbox = ref<string[]>([])
    const expectationsRadio = ref<{ [key: string]: string }>({})

    const meta = computed(() => orderItem.meta)
    const target = computed<string>(
      () => (route.value.query?.target as string) || ''
    )
    const expectationsMeta = computed(
      () => mentalHealth[target.value]?.expectations || []
    )
    const locale = computed(() => i18n.locale)

    const isValid = (): boolean => {
      return expectationsMeta.value.every((item, index) => {
        if (item.type === 'checkbox') {
          return expectationsCheckbox.value.length > 0
        }
        if (item.type === 'radio') {
          return Boolean(expectationsRadio.value[`radio${index}`])
        }
        return true
      })
    }

    const touch = (): boolean => {
      if (isValid()) {
        return true
      } else {
        $toast.global.appWarning({
          message: i18n.t('please choose an expectation before proceeding.')
        })
        return false
      }
    }

    const submit = () => {
      emit('submit', {
        expectations: [
          ...expectationsCheckbox.value.map((key) => ({ key })),
          ...Object.values(expectationsRadio.value).map((key) => ({ key }))
        ]
      })
    }

    const setDisableButtonNextStep = (val: boolean) => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', val)
    }

    watch(expectationsCheckbox, (val) => {
      setDisableButtonNextStep(!val.length)
    })

    watch(
      expectationsRadio,
      (val) => {
        setDisableButtonNextStep(!val)
      },
      {
        deep: true
      }
    )

    // Sync data
    watch(
      () => meta.value?.expectations,
      (expectations) => {
        if (expectations?.length) {
          expectationsMeta.value.forEach((item, index) => {
            if (item.type === 'checkbox') {
              const selectedCheckboxes = expectations
                .filter((exp) =>
                  item.data.some((data) => data[locale.value] === exp.key)
                )
                .map((exp) => exp.key)
              expectationsCheckbox.value = selectedCheckboxes

              // setDisableButtonNextStep(!expectationsCheckbox.value.length)
            }
            if (item.type === 'radio') {
              const selectedRadio = expectations
                .filter((exp) =>
                  item.data.some((data) => data[locale.value] === exp.key)
                )
                .map((exp) => exp.key)

              if (selectedRadio?.length) {
                expectationsRadio.value[`radio${index}`] =
                  expectations[index]?.key
              }
            }
            // setDisableButtonNextStep(!expectationsRadio.value.length)
          })
        }
      },
      { immediate: true }
    )

    // onMounted(() => {
    //   commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
    // })

    return {
      expectationsCheckbox,
      expectationsRadio,
      expectationsMeta,
      locale,
      touch,
      submit
    }
  }
})
</script>

<template>
  <v-row no-gutters>
    <v-col cols="12">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12">
      <v-row v-for="(item, index) in expectationsMeta" :key="index" no-gutters>
        <v-col cols="12">
          <h3 class="headline text--secondary title-dark font-weight-bold">
            {{ item[locale] }}
          </h3>
        </v-col>
        <v-col cols="12">
          <template v-if="item.type === 'radio'">
            <v-radio-group v-model="expectationsRadio[`radio${index}`]">
              <v-row>
                <v-col
                  v-for="(option, optionIndex) in item.data"
                  :key="optionIndex"
                  cols="12"
                >
                  <v-radio
                    :label="option[locale]"
                    :value="option[locale]"
                  ></v-radio>
                </v-col>
              </v-row>
            </v-radio-group>
          </template>
          <template v-else-if="item.type === 'checkbox'">
            <v-row no-gutters>
              <v-col
                v-for="(option, optionIndex) in item.data"
                :key="optionIndex"
                cols="12"
              >
                <v-checkbox
                  v-model="expectationsCheckbox"
                  :label="option[locale]"
                  :value="option[locale]"
                ></v-checkbox>
              </v-col>
            </v-row>
          </template>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<style scoped>
#skeleton >>> .v-skeleton-loader__list-item-avatar {
  padding: 0px !important;
}
</style>
