<template>
  <div>
    <p>{{ $t(title) }}</p>
    <provider-info :provider="providerChosen" />
    <provider-list
      :title="exploreTitle"
      :providers="providers"
      @on-choose-expert="onChooseExpert"
    />
    <provider-list
      :title="preferTitle"
      :providers="providers"
      @on-choose-expert="onChooseExpert"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'

import { useProductOrder } from '../../../repositories'

import ProviderList from './choose-provider/provider-list.vue'
import ProviderInfo from './choose-provider/provider-info.vue'

export default defineComponent({
  components: {
    ProviderInfo,
    ProviderList
  },
  props: {},
  setup(_, { emit }) {
    const { authenUser } = useProductOrder()

    const title = ref('Lựa chọn dưới đây dựa trên nội dung hội thoại')
    const exploreTitle = ref('Explore other matches?')
    const preferTitle = ref('Prefer your last known therapists?')

    const question = ref('question')
    const contentId = ref('61c9b2d449e9d830ef6fe01b')

    const providers = ref([
      {
        avatar: {
          url: 'https://storage.googleapis.com/cms-gallery-sandbox/62064989989520627096273e/gold-crown-laurel-wreath-winner-frame-isolated-vector-first-place-champion-sign-golden-ribbon-royal-luxury-vip-vintage-107979130.jpg',
          name: 'gold-crown-laurel-wreath-winner-frame-isolated-vector-first-place-champion-sign-golden-ribbon-royal-luxury-vip-vintage-107979130.jpg',
          _id: '62064989989520627096273e'
        },
        specialty: {
          en: {
            title: ['Pediatrics', 'Family Physicians', 'Internal Medicine']
          },
          fr: {
            title: ['Pédiatrie', 'Les médecins de famille', 'Médecine interne']
          },
          slug: ['nhi', 'bac-si-gia-dinh', 'noi-tong-quat'],
          vi: {
            title: ['Nội tổng quát Nhi', 'Y học Gia đình', 'Nội tổng quát']
          }
        },
        user: '5acd7d80d6eddf51bc1fdb82',
        name: 'Hoa Nguyên Hóa',
        title: 'Bs',
        _id: '5f9b95a96d31d6cd9c67f230'
      },
      {
        _id: '5a796c6a15339976d06fa788',
        avatar: {
          url: 'https://storage.googleapis.com/wellcare-user-profile/614a8074e265b9f48fc48e2a/e0b8dfd9-a409-434a-beef-edaa7accb800.jpg'
        },
        minuteAvg: 12.51990898748578,
        name: 'Nguyễn Trí Đoàn',
        order: 11985.24,
        ratingGood: 0.9986928104575163,
        slot: [
          1697898600, 1697896800, 1697895000, 1697894100, 1697893200,
          1697812200, 1697810400, 1697808600, 1697796900, 1697796000,
          1697725800, 1697724000, 1697722200, 1697721300, 1697720400,
          1697639400, 1697637600, 1697635800, 1697634900, 1697634000,
          1697553000, 1697551200, 1697549400, 1697548500, 1697547600,
          1697466600, 1697464800, 1697463000, 1697462100, 1697461200,
          1697380200, 1697378400, 1697376600, 1697375700, 1697374800
        ],
        slug: 'nguyen-tri-doan',
        specialty: {
          en: {
            title: ['Pediatrics', 'Pediatric nutrition']
          },
          fr: {
            title: ['Pédiatrie']
          },
          slug: ['nhi', 'dinh-duong-nhi'],
          vi: {
            title: ['Nội tổng quát Nhi', 'Dinh dưỡng Nhi']
          }
        },
        spoken: ['vi', 'en'],
        title: 'Bs',
        yearsofEXP: 1996
      },
      {
        _id: '5b48876c7d5325b35ca7217b',
        avatar: {
          _id: '651e17d21ff19c7cb50505d0',
          name: 'bsnamphuonghh222.png',
          url: 'https://storage.googleapis.com/wellcare-user-profile/5b48876c7d5325b35ca7217b/bsnamphuonghh222.png'
        },
        minuteAvg: 21,
        name: 'Đỗ Thị Nam Phương',
        order: 11800.31,
        ratingGood: 1,
        slot: [
          1699263000, 1699261200, 1699259400, 1699257600, 1699255800,
          1698658200, 1698656400, 1698654600, 1698652800, 1698651000,
          1698053400, 1698051600, 1698049800, 1698048000, 1698046200,
          1697448600, 1697446800, 1697445000, 1697443200, 1697441400
        ],
        slug: 'do-thi-nam-phuong',
        specialty: {
          en: {
            title: ['Cardiology', 'Internal Medicine']
          },
          fr: {
            title: ['Cardiologie', 'Médecine interne']
          },
          slug: ['tim-mach', 'noi-tong-quat'],
          vi: {
            title: ['Tim mạch', 'Nội tổng quát']
          }
        },
        spoken: [],
        title: 'Bs',
        yearsofEXP: 2001
      },
      {
        _id: '5cb0489c7b08831408a7c2a5',
        avatar: {
          url: 'https://storage.googleapis.com/wellcare-user-profile/614a8077e265b91876c48e64/158f6ea3-181a-47a8-a3e3-95f3a11a0e46.jpg'
        },
        minuteAvg: 9.24363719363734,
        name: 'Tô Viết Thuấn',
        order: 11701.56,
        ratingGood: 1,
        slot: [
          1697889600, 1697886000, 1697882400, 1697863500, 1697862600,
          1697803200, 1697799600, 1697796000, 1697777100, 1697776200,
          1697716800, 1697713200, 1697709600, 1697690700, 1697689800,
          1697630400, 1697626800, 1697623200, 1697604300, 1697603400,
          1697544000, 1697540400, 1697536800, 1697517900, 1697517000,
          1697457600, 1697454000, 1697450400, 1697431500, 1697430600,
          1697339700, 1697337000, 1697334300, 1697332500, 1697331600
        ],
        slug: 'to-viet-thuan',
        specialty: {
          en: {
            title: ['Endocrinology', 'Diabetes', 'Internal Medicine']
          },
          fr: {
            title: ['Endocrinologie', 'Diabète', 'Médecine interne']
          },
          slug: ['noi-tiet', 'dai-thao-duong', 'noi-tong-quat'],
          vi: {
            title: ['Nội tiết', 'Đái tháo đường', 'Nội tổng quát']
          }
        },
        spoken: [],
        title: 'Bs',
        yearsofEXP: 2002
      },
      {
        _id: '59e1ddc3e33f735ea35b8d59',
        avatar: {
          url: 'https://storage.googleapis.com/wellcare-user-profile/614a8072e265b95e88c48e12/453b9a52-94d6-46d9-86ca-9fd2777f6ba9.png'
        },
        minuteAvg: 11.095238095238095,
        name: 'Trần Thị Hồng An',
        order: 11601.37,
        ratingGood: 1,
        slot: [
          1697895000, 1697891400, 1697880600, 1697876100, 1697875200,
          1697808600, 1697805000, 1697794200, 1697789700, 1697788800,
          1697722200, 1697718600, 1697707800, 1697703300, 1697702400,
          1697635800, 1697632200, 1697621400, 1697616900, 1697616000,
          1697549400, 1697545800, 1697535000, 1697530500, 1697529600,
          1697463000, 1697459400, 1697448600, 1697444100, 1697443200,
          1697376600, 1697373000, 1697362200, 1697357700, 1697356800
        ],
        slug: 'tran-thi-hong-an',
        specialty: {
          en: {
            title: [
              'Internal Medicine',
              'Kidney Internal Medicine',
              'Endocrinology'
            ]
          },
          fr: {
            title: [
              'Médecine interne',
              'Rein médecine interne',
              'Endocrinologie'
            ]
          },
          slug: ['noi-tong-quat', 'noi-than', 'noi-tiet'],
          vi: {
            title: ['Nội tổng quát', 'Nội thận', 'Nội tiết']
          }
        },
        spoken: [],
        title: 'Bs',
        yearsofEXP: 1992
      },
      {
        _id: '5b48842e7d5325b35ca72166',
        avatar: {
          mimetype: 'image/png',
          url: 'https://storage.googleapis.com/wellcare-user-profile/614a8075e265b92202c48e37/885a82a7-8c4b-52f8-b8bb-8928906fd83e.png'
        },
        minuteAvg: 11.2,
        name: 'Nguyễn Công Viên',
        order: 11500.37,
        ratingGood: 1,
        slot: [
          1697891400, 1697889600, 1697805000, 1697803200, 1697718600,
          1697716800, 1697632200, 1697630400, 1697545800, 1697544000,
          1697459400, 1697457600, 1697373000, 1697371200
        ],
        slug: 'nguyen-cong-vien',
        specialty: {
          en: {
            title: ['Pediatrics', 'Family Physicians', 'Internal Medicine']
          },
          fr: {
            title: ['Pédiatrie', 'Les médecins de famille', 'Médecine interne']
          },
          slug: ['nhi', 'bac-si-gia-dinh', 'noi-tong-quat'],
          vi: {
            title: ['Nội tổng quát Nhi', 'Y học Gia đình', 'Nội tổng quát']
          }
        },
        spoken: [],
        title: 'Bs',
        yearsofEXP: 1988
      }
    ])
    const providerChosen = ref(providers.value[0])

    const onChooseExpert = (provider: any) => {
      providerChosen.value = provider
    }

    const touch = () => {
      return providerChosen.value
    }

    const submit = () => {
      if (!touch()) return

      emit('submit', {
        question: question.value,
        contentId: contentId.value,
        patient: authenUser.value._id,
        chiefComplaint: 'test',
        provider: providerChosen.value
      })
    }

    return {
      title,
      exploreTitle,
      preferTitle,
      providers,
      providerChosen,
      onChooseExpert,
      touch,
      submit,
      goToPayment: () => true
    }
  }
})
</script>

<style scoped></style>
