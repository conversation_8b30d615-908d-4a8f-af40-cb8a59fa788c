<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12">
      <v-item-group v-model="language" mandatory>
        <v-row>
          <v-col
            v-for="(language, index) in languages"
            :key="index"
            cols="12"
            sm="12"
            md="6"
            lg="6"
            xl="6"
          >
            <v-skeleton-loader v-if="loading" type="image" max-height="58" />
            <v-item
              v-else
              v-slot="{ active, toggle }"
              :disabled="language.isLock"
            >
              <v-card
                class="d-flex align-center pa-3"
                rounded="lg"
                :color="active ? '#f1f8f7' : ''"
                :outlined="!active"
                ripple
                :style="{
                  border: active ? '2px solid var(--v-primary-base)' : '',
                  filter: `grayscale(${language.isLock ? '100%' : '0%'})`
                }"
                min-height="58"
                :disabled="language.isLock"
                :elevation="active ? '4' : '1'"
                @click="toggle"
              >
                <v-scale-transition>
                  <v-icon
                    color="primary"
                    :style="{
                      zIndex: '1'
                    }"
                    size="20"
                    >{{ active ? '$checkCircle' : '$circleOutline' }}</v-icon
                  >
                </v-scale-transition>
                <div
                  class="ml-6 d-flex justify-space-between align-center"
                  :style="{ width: '100%' }"
                >
                  <span class="text-subtitle-1" :style="{ textAlign: 'left' }">
                    {{ $t(language.label) }}
                  </span>
                  <v-chip
                    v-if="language.surcharge"
                    color="rgba(210, 39, 39, 0.10)"
                  >
                    <span
                      class="font-weight-black pa-0"
                      :style="{ color: 'red' }"
                      >+
                      {{
                        $n(language.surcharge, {
                          style: 'currency',
                          currency: 'VND'
                        })
                      }}
                      <span class="font-weight-medium">{{
                        $t('surcharge')
                      }}</span>
                    </span>
                  </v-chip>
                </div>
              </v-card>
            </v-item>
          </v-col>
        </v-row>
      </v-item-group>
      <p class="text-subtitle-1 text--secondary font-italic mt-2">
        {{
          $t(`surcharge {surchargeLang} per slot for consulting in English`, {
            surchargeLang: $n(surchargeLang, {
              style: 'currency',
              currency: 'VND'
            })
          })
        }}
      </p>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useStore
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IProvider } from '../../../models/provider'
import { IStep } from '../../../repositories'
import { calcSurchargeLanguage } from '../../../compositions'

export interface ILanguages {
  isLock: boolean
  label: string
  value: string
  country: string
  surcharge: number
}

const FORMAT_LANG = {
  LABEL: {
    vi: 'vietnamese',
    en: 'english'
  },
  COUNTRY: {
    vi: 'vn',
    en: 'us'
  }
}

export default defineComponent({
  props: {
    currentStep: {
      type: Object as PropType<IStep>,
      default: () => {}
    }
  },
  setup(_, { emit }) {
    const { state }: any = useStore()
    const language = ref<number>(0)
    const surchargeLang = ref<number>(0)
    const loading = ref<boolean>(true)
    // const medium = computed<string>(
    //   () => state?.checkout?.orderItem?.meta?.medium
    // )

    const languages = computed<ILanguages[]>(() => {
      const provider: IProvider = state?.checkout?.provider
      const feeLang = provider?.fee?.lang
      const mediumPriceProvider = (provider?.fee as any).phone
      const sortedKeys = Object.keys(feeLang).sort((a, b) => {
        if (a === 'vi') return -1
        if (b === 'vi') return 1
        return 0
      })

      setTimeout(() => {
        loading.value = false
      }, 500)
      return sortedKeys.map((lang): ILanguages => {
        const isLock = lang !== 'vi' && feeLang[lang] === 0
        const calcLang = calcSurchargeLanguage(
          mediumPriceProvider,
          feeLang[lang]
        )

        if (calcLang) {
          surchargeLang.value = calcLang
        }

        return {
          isLock,
          label: FORMAT_LANG.LABEL[lang],
          value: lang,
          country: FORMAT_LANG.COUNTRY[lang],
          surcharge: calcLang
        }
      })
    })

    const touch = () => {
      return true
    }

    const submit = () => {
      emit('submit', {
        lang: languages.value[language.value].value
      })
    }
    return {
      language,
      languages,
      touch,
      submit,
      surchargeLang,
      loading
    }
  }
})
</script>
