<template>
  <v-row key="choose-medium-component">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col
      v-for="(item, idx) in itemsFormatted"
      :key="item.type"
      cols="12"
      sm="6"
      :class="{ 'py-2': $vuetify.breakpoint.xsOnly }"
    >
      <v-card
        class="pb-3 rounded-lg d-flex flex-column w-card pa-4"
        :class="selectedIndex === idx ? 'active-card' : ''"
        :disabled="item.disabled"
        height="100%"
        hover
        @click="selectItem(item, idx)"
      >
        <div class="flex-row d-flex align-start flex-grow-1">
          <div
            class="pr-2 d-flex flex-column justify-space-between flex-shrink-1 flex-grow-1"
          >
            <v-card-title
              class="flex-row pb-1 pa-0 mt-n1 flex-nowrap align-content-center"
            >
              <v-icon
                v-show="showIconSelected"
                :color="item.disabled ? 'grey' : 'primary'"
              >
                {{ selectedIndex === idx ? '$checkCircle' : '$circleOutline' }}
              </v-icon>
              <span
                class="ml-2 title font-weight-medium"
                style="word-break: break-word"
              >
                {{ $t(item.title) }}
              </span>
              <v-spacer />
            </v-card-title>
            <v-card-text class="pa-0 text--secondary">
              <div
                v-for="(desc, index) in item.description"
                :key="'desc-' + index"
                v-dompurify-html="$t(desc)"
                class="body-2"
              ></div>
              <div class="pt-1 red--text font-weight-medium">
                {{ $t(item.notify) }}
              </div>
            </v-card-text>
          </div>
          <div class="flex-shrink-0 w-icon-square-area">
            <v-icon>{{ item.icon }}</v-icon>
          </div>
        </div>
        <v-card-actions class="mt-auto pa-0 mb-n1">
          <div
            v-show="!item.disabled && showPrice"
            class="red--text font-weight-medium subtitle-1"
          >
            {{ $n(item.fee, { style: 'currency', currency: 'VND' }) }} /
            {{ providerConsultTime + ' ' + $t('minutes') }}
          </div>
          <v-spacer></v-spacer>
          <div>
            <v-btn v-if="item.disabled" color="grey" text>
              <i>{{ $t('locked') }}</i>
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-col>
    <v-dialog v-model="alertTimeslot" max-width="350">
      <v-card rounded="lg">
        <v-card-title class="justify-center h6 d-flex flex-column align-center">
          <div class="warning-icon mt-3">
            <v-icon color="primary" x-large> $messageText </v-icon>
          </div>
        </v-card-title>
        <v-card-text>
          <h3
            v-dompurify-html="
              $t('today schedule of doctor is not fixed', {
                doctor: provider ? provider.name : ''
              })
            "
            class="text--primary text-center text-break mt-2"
          ></h3>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="alertError" persistent max-width="350">
      <w-card-error
        class="rounded-lg"
        @on:try_again="onTryAgain"
        @on:report="onReport"
      />
    </v-dialog>
    <a v-show="false" ref="callSupportEl" href="tel:+84366905905"
      >+84 366 905 905</a
    >
  </v-row>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  useContext,
  watch,
  useStore
} from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useProviderTimeslots, useProductOrder } from '../../../repositories'

export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { commit } = useStore()
    const { $toast, i18n, $config }: any = useContext()
    const { authenUser, orderItem } = useProductOrder()
    const callSupportEl: Ref<HTMLElement | null> = ref(null)
    // check provider is available or not
    const alertTimeslot: Ref<boolean> = ref(false)
    const alertError: Ref<boolean> = ref(false)
    let timeslotCount = 0
    let totalSlots = 0
    let errorCount = 0
    const {
      provider,
      executeSearchProvider,
      onGetProviderTimeslotsSuccess,
      onGetProviderTimeslotsError,
      onSearchProviderSuccess,
      timeslotsData
    } = useProviderTimeslots(
      computed(() => ({
        slug: props.orderItem.product.provider.slug
      })),
      true
    )
    const UIcustomChooseMedium: ComputedRef<any> = computed(
      () => $config?.checkout?.UIcustomize?.chooseMedium
    )
    const showPrice: ComputedRef<boolean> = computed(
      () => UIcustomChooseMedium.value?.showFee ?? true
    )
    const showIconSelected: ComputedRef<boolean> = computed(
      () => UIcustomChooseMedium.value?.showIconSelected ?? true
    )
    const itemsFormatted: Ref<any[]> = ref([])
    const state: any = reactive({
      data: {}
    })
    const selectedIndex: Ref<number> = ref(-1)
    const providerConsultTime: ComputedRef<string | number> = computed(
      () => provider.value?.consultTime || '_'
    )
    const orderItemMetaMedium = computed(() => orderItem.value.meta?.medium)
    // LOGIC
    const getSelected = () => {
      // get data from store
      const index = itemsFormatted.value.findIndex(
        (i) => i.type === orderItem.value.meta?.medium
      )
      selectedIndex.value = index
      state.data = itemsFormatted.value[index]
    }
    // search timeslot of provider, if provider not have any slot, show popup and prevent got to next step
    executeSearchProvider()
    onGetProviderTimeslotsSuccess(() => {
      // maximum is 4 slots; if count slots reach to 4, check total slots
      timeslotCount++
      totalSlots += timeslotsData.length
      if (timeslotCount === 4) {
        alertTimeslot.value = totalSlots === 0
      }
    })
    onGetProviderTimeslotsError(() => {
      errorCount++
      if (errorCount === 4) {
        alertError.value = true
      }
    })
    // before load component, adapter data to render
    onSearchProviderSuccess(() => {
      const items = {
        phone: {
          type: 'phone',
          icon: '$phone',
          title: 'voice call',
          description: [
            'Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly',
            'Cellular Call or Internet Call (using Wellcare app) on schedule'
          ],
          subtitle: 'voice call'
        },
        video: {
          type: 'video',
          icon: '$video',
          title: 'video call',
          description: [
            'Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly',
            'Video Call using Wellcare app on schedule (you can always switch to Cellular Call once the internet is unstable)'
          ],
          subtitle: 'video call'
        }
      }
      const returnItems: any[] = []
      Object.keys(items).forEach((item, _index) => {
        // push items
        returnItems.push({
          ...items[item],
          ...{
            disabled: provider.value?.mediumEnabled[item],
            value: item,
            // fee: provider.value?.fee?.[item]
            fee: provider.value?.fee[item] || null
          }
        })
      })
      itemsFormatted.value = returnItems
      // after adapter success, check data from store to set selected
      getSelected()
    })
    const selectItem = (item, index) => {
      selectedIndex.value = index
      state.data = item
    }
    const onTryAgain = () => {
      if (process.client) {
        window.location.reload()
      }
    }
    const onReport = () => {
      if (callSupportEl.value) {
        callSupportEl.value.click()
      }
    }
    // this function use to validate data before go to next step

    if (selectedIndex.value === -1) {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', true)
    }
    watch(selectedIndex, () => {
      commit(
        'checkout/SET_BUTTON_NEXT_STATE_DISABLE',
        selectedIndex.value === -1
      )
    })
    const touch = () => {
      if (selectedIndex.value === -1) {
        $toast?.global?.appError({
          message: i18n.t('please choose medium before continuing.')
        })
        return false
      }
      if (totalSlots === 0) {
        alertTimeslot.value = true
        return false
      }
      return true
    }
    // this function use to emit data to update order item
    const submit = () => {
      emit('submit', {
        medium: state.data.type,
        fee: state.data.fee,
        user: authenUser.value._id,
        provider: provider.value,
        providerUser: provider.value?._id,
        lang: 'vi'
      })
    }
    watch(provider, () => {
      for (const item of itemsFormatted.value) {
        item.fee = provider.value?.fee[item.type]
        item.disabled = !provider.value?.mediumEnabled[item.type]
      }
      state.data = itemsFormatted.value[0]
      commit('checkout/updateField', {
        path: 'provider',
        value: provider.value
      })
    })
    watch(orderItemMetaMedium, () => {
      getSelected()
    })
    return {
      itemsFormatted,
      showPrice,
      selectItem,
      selectedIndex,
      touch,
      submit,
      showIconSelected,
      providerConsultTime,
      alertTimeslot,
      alertError,
      provider,
      onTryAgain,
      onReport,
      callSupportEl
    }
  }
})
</script>

<style scoped>
.active-card {
  border: 2px solid var(--v-primary-base);
  /* background-color: var(--v-primary-lighten5); */
  background-color: #00968710;
}
.activeCard {
  background-color: #00968710;
  margin: -2px !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}
.activeCard * {
  color: var(--v-primary-base);
}
.w-icon-square-area {
  width: 42px;
  height: 42px;
  display: flex;
  place-items: center;
  place-content: center;
  border-radius: 8px;
}
.w-icon-square-area.active {
  background-color: #0096883d;
}
</style>
