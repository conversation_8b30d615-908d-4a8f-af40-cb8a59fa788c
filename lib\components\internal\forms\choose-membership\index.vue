<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ i18n.t(currentContent) }}
      </h3>
    </v-col>
    <!-- BECOME MEMBERSHIP -->
    <MemberBenefit />

    <!-- <AddonsCheckout
      :benefit-option="benefitOption"
      :update-selected-checkboxes="updateSelectedCheckboxes"
    /> -->
    <!-- <total-price :total-price="totalPrice" /> -->
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onBeforeMount,
  useContext,
  watch,
  useStore
} from '@nuxtjs/composition-api'
import type { PropType, Ref } from '@nuxtjs/composition-api'

import { useScreenSafeArea } from '@vueuse/core'
import { BenefitOption } from '../../../../models'
import { useMappingProduct } from '../../../../composables/use-mapping-product'
import {
  IStep,
  prepareSingleOrder,
  useProductOrder
} from '../../../../repositories'
// import AddonsCheckout from '../../order/add-ons/checkout/index.vue'
import MemberBenefit from './member-benefit.vue'
// import TotalPrice from './total-price.vue'

export default defineComponent({
  components: {
    MemberBenefit
    // TotalPrice,
    // AddonsCheckout
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      default: () => {}
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { bottom } = useScreenSafeArea()
    const { commit, state }: any = useStore()
    const user = computed(() => state?.authen?.user)
    const isMember = computed(() => user.value?.isMember)
    const { $dayjs, $toast, i18n, $axios, $config }: any = useContext()
    const { order } = useProductOrder()
    const { execute: executeProductOrder } = prepareSingleOrder()

    const product = computed(() => props.orderItem?.product)

    const { getBenefitName } = useMappingProduct()

    const currentContent = computed(() => {
      return props.currentStep?.content?.title
    })
    const createdAt = ref()
    const expirationDate = ref()
    const formattedCreatedAt = ref()
    const initialPrice: Ref<number> = ref(0)
    const benefitOption: Ref<BenefitOption[]> = ref([])

    onBeforeMount(() => {
      commit('checkout/SET_ENABLE_BTN_NEXT')
      const rawProduct = [...product.value.includes]
      initialPrice.value = isMember.value ? 0 : product.value.price
      rawProduct.forEach((include: any) => {
        benefitOption.value.push({
          title: include.name || '',
          price: include.price || 0,
          subTotal: include.price || 0,
          slug: include.slug || '',
          state: 'cancelled',
          // isDisabled: ['bac-si-rieng'].includes(include.slug), // Some products are not planned yet, so they need to be hidden
          ...getBenefitName(include.slug)
        })
      })
    })

    // const isProvider = ref<boolean>(false)

    const updateSelectedCheckboxes = (slug: any) => {
      benefitOption.value.forEach((option) => {
        if (option.slug === slug) {
          option.state = option.state !== 'cancelled' ? 'cancelled' : 'pending'
          // if (option.slug === 'personal-doctor-addon') {
          //   if (option.state === 'cancelled') isProvider.value = false
          //   else isProvider.value = true
          // }
        }
      })
    }

    // Compute the total price of selected options
    const totalPrice = computed(() => {
      let price: number = initialPrice.value
      benefitOption.value.forEach((option) => {
        if (option.state === 'pending') {
          price += option?.subTotal || 0
        }
      })
      return price
    })

    watch(
      () => order.value,
      () => {
        createdAt.value = order.value?.createdAt || order.value?.date
        formattedCreatedAt.value = $dayjs(createdAt.value).format('YYYY-MM-DD')
        expirationDate.value = $dayjs(createdAt.value)
          .add(1, 'year')
          .subtract(1, 'day')
          .format('YYYY-MM-DD')
      }
    )

    // Confirm and submit the order
    const submit = () => {
      benefitOption.value
        .filter((option) => option.slug && !option.isDisabled)
        .forEach((option) => {
          executeProductOrder({ product: { slug: option.slug } }).then(
            async (res) => {
              const initialData: any = {
                _id: '',
                description: '',
                title: '',
                state: ''
              }

              const orderItemId = res.results?.orderItems[0]?._id

              if (orderItemId) {
                const optionEntity = benefitOption.value.find(
                  (opt) => opt.slug === option.slug
                )

                if (optionEntity) {
                  Object.assign(initialData, {
                    _id: orderItemId,
                    description: optionEntity.description,
                    title: optionEntity.title,
                    state: optionEntity.state
                  })

                  await $axios.put(
                    `${$config.endpoint}/api/order-item/${orderItemId}`,
                    {
                      update: {
                        ...initialData
                      }
                    }
                  )
                }
              }
            }
          )
        })

      const isValid = computed(() => {
        const benefit = benefitOption.value.find(
          (option) => option.slug === 'personal-doctor-addon'
        )
        return benefit?.state !== 'cancelled'
      })

      // Emit an event with order information

      const payload: any = {}

      if (state.authen?.user?.isMember) {
        payload.customPrice = true
        payload.note = 'paid'
        payload.price = 0
      }
      emit('set-order-item', {
        ...payload,
        description:
          'Membership (all-year waiver of the service fee and other benefits)'
      })

      emit('submit', {
        createdAt: expirationDate.value,
        userOptions: benefitOption.value,
        benefits: [{ key: 'membership-addon' }],
        hasPersonalDoctor: isValid.value
      })
    }

    // Check if user can proceed to payment
    const isAcceptPayment = () => {
      const boughtAskDoctor = computed(() => {
        return user.value?.membership[0]?.benefits.find(
          (benefit: any) => benefit.key === 'ask-doctor'
        )
      })

      if (isMember.value && initialPrice.value === totalPrice.value) {
        $toast.success(i18n.t('you are already a member') as string, {
          duration: 150000
        })
        return false
      } else if (
        isMember.value &&
        initialPrice.value !== totalPrice.value &&
        !!boughtAskDoctor.value
      ) {
        $toast.success(
          i18n.t('You have purchased an additional benefits') as string,
          {
            duration: 1500
          }
        )
        return false
      }
      return true
    }
    return {
      i18n,
      submit,
      bottom,
      totalPrice,
      initialPrice,
      benefitOption,
      currentContent,
      touch: () => true,
      updateSelectedCheckboxes,
      goToPayment: isAcceptPayment
    }
  }
})
</script>

<style scoped>
.benefit-options >>> .v-messages__message {
  font-size: 15px !important;
  line-height: 150% !important;
  color: #4b5563;
}

.benefit-options >>> .v-input--selection-controls {
  margin-top: 5px !important;
}
</style>
