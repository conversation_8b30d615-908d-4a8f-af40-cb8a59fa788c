<template>
  <v-col cols="12" class="pt-0">
    <div
      :style="{ fontSize: '16px' }"
      class="d-flex justify-space-between align-end py-1 font-weight-bold"
    >
      <div class="d-flex">
        <div class="mr-2">
          <span class="primary--text mr-1">✓</span>
          <span class="primary--text pr-1">{{ i18n.t(title) }} </span>
        </div>
        <StatusTag v-if="isMember" content="paid" />
      </div>
      <div class="d-flex flex-column">
        <p
          :class="[
            { 'primary--text': !isMember },
            { 'grey--text text--darken-3': isMember },
            'mb-0',
            'd-flex flex-column justify-center',
            { 'text-decoration-line-through': isMember }
          ]"
        >
          {{
            `${i18n.n(basicPrice, {
              style: 'currency',
              currency: 'VND'
            })}/${i18n.t('year')}`
          }}
        </p>
      </div>
    </div>
    <v-card-subtitle
      :style="{ fontSize: '15px', color: '#666666' }"
      class="py-1 px-1"
    >
      <p v-dompurify-html="i18n.t(description)" class="mb-0 text-14 ml-5"></p>
    </v-card-subtitle>
    <slot></slot>
  </v-col>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  useContext,
  useStore
} from '@nuxtjs/composition-api'
import StatusTag from './status-tag.vue'

export default defineComponent({
  components: { StatusTag },
  props: {
    basicPrice: {
      type: Number,
      default: 250000
    },
    title: {
      type: String,
      default: 'membership'
    },
    description: {
      type: String,
      default:
        'Membership (all-year waiver of the service fee and other benefits)'
    }
  },
  setup() {
    const { state }: any = useStore()
    const { i18n }: any = useContext()
    const isMember = computed(() => state?.authen?.user?.isMember)

    return { i18n, isMember }
  }
})
</script>
