<template>
  <span class="status-tag font-weight-medium">
    {{ $t(content) }}
  </span>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'StatusTag',
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  setup() {
    return {}
  }
})
</script>

<style scoped>
.status-tag {
  text-align: center;
  color: var(--v-primary-base);
  font-size: 12px;
  padding: 4px 6px;
  border-radius: 4px;
  background-color: #e0f3f1;
}
</style>
