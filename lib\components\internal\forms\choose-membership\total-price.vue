<template>
  <v-footer
    app
    fixed
    inset
    padless
    color="w-footer mt-4"
    :style="{
      paddingBottom: isMobile ? '85px' : '50px',
      boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px'
    }"
  >
    <v-container>
      <div class="mt-4 d-flex justify-space-between">
        <v-card-title
          class="pa-0 font-weight-bold"
          :style="{
            fontSize: '18px'
          }"
          >{{ $t('total') }}
        </v-card-title>
        <v-card-title
          class="pa-0 total-price red--text text--darken-1 font-weight-bold"
          :style="{ position: 'relative', fontSize: '16px' }"
        >
          {{ $n(totalPrice, 'currency') }}
        </v-card-title>
      </div>
      <p
        class="text-right"
        :style="{
          fontSize: '16px',
          opacity: totalPricePerMonth > 0 ? '1' : '0',
          marginBottom: totalPricePerMonth <= 0 ? '-15px' : '',
          transition: 'all 0.2s linear'
        }"
      >
        ~ {{ $t('only') }} {{ $n(totalPricePerMonth, 'currency') }}/{{
          $t('month')
        }}
      </p>
    </v-container>
  </v-footer>
</template>

<script>
import { defineComponent, useContext, computed } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    memberBenefitPrice: {
      type: Number,
      default: 0
    },
    totalPrice: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    const { $vuetify } = useContext()
    const isMobile = computed(() => $vuetify.breakpoint.mobile)
    const totalPricePerMonth = computed(() => {
      const totalPriceMonthly = props.totalPrice / 12
      return Math.round(totalPriceMonthly / 1000) * 1000
    })
    return {
      isMobile,
      totalPricePerMonth
    }
  }
})
</script>
<style scoped>
.total-price {
  &::before {
    position: absolute;
    content: 'VND';
    text-align: right;
    top: -20px;
    width: 40px;
    right: 0;
    font-size: 15px;
  }
}
</style>
