<template>
  <v-row :key="'choose-patient-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ $t('Growing Up Together') }}
      </h3>
      <h4 class="body-1 text--secondary">{{ $t('Exclusive for Members') }}</h4>
    </v-col>
    <MemberBenefit :basic-price="initialPrice" :description="description" />
    <!-- <AddonsCheckout
      :benefit-option="benefitOption"
      :loading="isLoading"
      :update-selected-checkboxes="updateSelectedCheckboxes"
    /> -->
  </v-row>
</template>
<script lang="ts">
import {
  ref,
  defineComponent,
  useContext,
  watch,
  computed,
  onMounted,
  useStore
} from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { watchOnce } from '@vueuse/core'
import { useUser } from '@wellcare/nuxt-module-account/repositories'
import {
  useMappingProduct,
  useSearchRelatedProducts
} from '../../../composables'
import {
  useProductOrder,
  useBabyDevelopment,
  prepareSingleOrder
} from '../../../repositories'
import { updateOrderItemUrl } from '../../../repositories/wellcare-api-urls'
// import AddonsCheckout from '../order/add-ons/checkout/index.vue'
import MemberBenefit from './choose-membership/member-benefit.vue'

export default defineComponent({
  components: {
    MemberBenefit
    // AddonsCheckout
  },
  props: {
    currentStep: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    }
  },

  setup(props, { emit }) {
    const { order, product } = useProductOrder()
    const { onSucess: onProductOrderSuccess, execute: executeProductOrder } =
      prepareSingleOrder()
    const createdAt = ref()
    const expirationDate = ref()
    const formattedCreatedAt = ref()
    const { state }: any = useStore()
    const benefitOption = ref<any[]>([])
    const isLoading = ref<boolean>(false)
    const { register } = useBabyDevelopment()
    const { user, renewStoreAuthen } = useUser()
    const { i18n, $dayjs, $axios, $toast }: any = useContext()
    const userOptions = computed(() => {
      return props.orderItem?.meta?.userOptions || []
    })

    const initialPrice: Ref<number> = ref(250000)

    const description = i18n.t(
      'Track your baby_s growth and milestones, and receive weekly advice'
    )

    const { execute: searchRelatedProducts, data } = useSearchRelatedProducts([
      `member-${props.orderItem.product.sku}`,
      `${props.orderItem.product.sku}`
    ])

    const { getBenefitName } = useMappingProduct()

    searchRelatedProducts()

    watchOnce(data, (newValue: any) => {
      isLoading.value = true
      newValue.forEach((include: any) => {
        executeProductOrder({ product: { slug: include.slug } })
        let state = 'cancelled'
        const option = userOptions.value.find((o) => o.slug === include.slug)
        if (option && option?.state) state = option.state
        benefitOption.value.push({
          title: include.name || '',
          isDisabled: false,
          price: include.price || 0,
          slug: include.slug || '',
          state,
          ...getBenefitName(include.slug)
        })
      })
      isLoading.value = false
    })

    watch(userOptions, (newValue) => {
      isLoading.value = true
      if (newValue.length > 0 && data.value.length > 0) {
        const benefits: Array<any> = []
        benefitOption.value.forEach((option, index) => {
          benefits.push({
            ...option,
            ...newValue[index]
          })
        })

        benefitOption.value = [...benefits]
      }
      isLoading.value = false
    })

    onProductOrderSuccess((res) => {
      const orderItem = res.results.orderItems[0]
      benefitOption.value.forEach((option, index) => {
        if (option.slug === orderItem.product.slug) {
          benefitOption.value[index]._id = orderItem._id
        }
      })
    })

    const isValid = computed(() => {
      const benefit = benefitOption.value.find(
        (option) => option.slug === 'personal-doctor-addon'
      )
      return benefit?.state !== 'cancelled'
    })

    const isProvider = ref<boolean>(false)

    const updateSelectedCheckboxes = (slug: any) => {
      benefitOption.value.forEach((option) => {
        if (option.slug === slug) {
          option.state = option.state !== 'cancelled' ? 'cancelled' : 'pending'
          if (option.slug === 'personal-doctor-addon') {
            if (option.state === 'cancelled') isProvider.value = false
            else isProvider.value = true
          }
        }
      })
    }

    watch(
      () => order.value,
      () => {
        createdAt.value = order.value?.createdAt || order.value?.date
        formattedCreatedAt.value = $dayjs(createdAt.value).format('YYYY-MM-DD')
        expirationDate.value = $dayjs(createdAt.value)
          .add(1, 'year')
          .subtract(1, 'day')
          .format('YYYY-MM-DD')
      }
    )

    const touch = async () => {
      for (const benefit of benefitOption.value) {
        if (benefit._id) {
          try {
            await $axios.put(updateOrderItemUrl(benefit._id), {
              update: {
                description: benefit.description,
                title: benefit.title,
                state: benefit.state,
                image: {
                  url: ''
                }
              }
            })
          } catch (e) {
            $toast?.global?.appError({
              message: e.message
            })
          }
        }
      }
      const payload: any = {
        image: {
          // url: '/icons/growing-with-child.jpg'
          url: ''
        },
        title: 'membership',
        description:
          'Membership (all-year waiver of the service fee and other benefits)',

        meta: {
          ...props.orderItem.meta,
          createdAt: expirationDate.value
        }
      }
      if (state.authen?.user?.isMember) {
        payload.customPrice = true
        payload.note = 'paid'
        payload.price = 0
      }
      emit('set-order-item', payload)
      if (
        benefitOption.value.every((benefit) => benefit.state === 'cancelled') &&
        user.value.isMember
      ) {
        await register({
          user: user.value._id,
          baby_date: props.orderItem.meta.baby_date,
          baby_id: props.orderItem.meta.baby_id,
          baby_subcribe: true
        })
        await renewStoreAuthen()
      }
      return true
    }

    const submit = () => {
      emit('submit', {
        userOptions: benefitOption.value,
        hasPersonalDoctor: isValid.value,
        benefits: [{ key: 'growing-up-together' }],
        product: undefined,
        provider: undefined
      })
    }

    onMounted(() => {
      initialPrice.value = product.value.price
    })

    return {
      i18n,
      touch,
      submit,
      // isValid,
      isLoading,
      description,
      initialPrice,
      benefitOption,
      updateSelectedCheckboxes
    }
  }
})
</script>
