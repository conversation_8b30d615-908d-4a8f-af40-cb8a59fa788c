<template>
  <v-row :key="'choose-patient-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ $t(currentStep.content.title) }}
      </h3>
      <h4 class="body-1 text--secondary">
        {{ $t(currentStep.content.subtitle) }}
      </h4>
    </v-col>
    <patient-list
      ref="patientListRef"
      :show-dialog="showDialog"
      @patient="selectPatient"
      @existed-consultation="isExisted = $event"
      @on-close="showDialog = false"
    />
    <h5 class="body-1 mb-2 mt-1 text--secondary">
      {{ $t(currentStep.content.description) }}
    </h5>
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  useContext,
  useStore,
  ref
} from '@nuxtjs/composition-api'
import type { Ref, PropType } from '@nuxtjs/composition-api'

import { IStep } from '../../../../repositories/workflow/flow.interface'
import PatientList from './patient-list.vue'
export default defineComponent({
  components: {
    PatientList
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(_, { emit }) {
    // DECLARE VARIABLE
    const { state: storeState } = useStore()

    const state = reactive({
      patientInfo: {},
      user: (storeState as any).authen.user._id
    })
    const { $toast, i18n } = useContext()
    const isExisted: Ref<boolean> = ref(false)
    const showDialog: Ref<boolean> = ref(false)
    const patientListRef = ref()
    // LOGIC

    const submit = () => {
      if (!state.patient || JSON.stringify(state.patientInfo) === '{}') {
        $toast?.global?.appError({
          message: i18n.t('please choose patient')
        })
        return
      }

      emit('submit', state)
    }
    const selectPatient = (data) => {
      state.patient = data.patient
      state.patientInfo = data.patientInfo
    }
    const touch = () => {
      // Allow to book additional consultations even if have already had another consultation

      if (!state.patient || JSON.stringify(state.patientInfo) === '{}') {
        $toast?.global?.appError({
          message: i18n.t('please choose patient')
        })
        return false
      }

      if (!(state.patientInfo as any)?.avatar?.url) {
        patientListRef.value?.requireAvatar()

        return false
      }
      return true
    }

    return {
      patientListRef,
      state,
      submit,
      selectPatient,
      touch,
      isExisted,
      showDialog,
      goToPayment: () => true
    }
  }
})
</script>
