<template>
  <v-dialog
    v-model="dialog"
    transition="scroll-y-reverse-transition"
    max-width="350"
  >
    <v-card rounded="lg" class="pa-4">
      <div class="font-weight-bold text-center text-18">
        {{ $t('are you sure you want to delete') }}
      </div>
      <v-card-actions class="justify-center pa-0 mt-3">
        <v-btn
          x-large
          text
          block
          tile
          class="font-weight-normal justify-center rounded-lg"
          :loading="loading"
          style="
            background-color: rgb(255, 240, 239);
            letter-spacing: normal;
            color: var(--v-error-base);
          "
          @click="emitDelete"
          >{{ $t('remove') }}</v-btn
        >
      </v-card-actions>
      <v-card-actions class="justify-center pa-0">
        <v-btn
          x-large
          block
          tile
          text
          class="mt-3 font-weight-normal justify-center rounded-lg"
          :disabled="loading"
          style="
            background-color: rgb(248, 248, 248);
            letter-spacing: normal;
            color: var(--v-primary-base);
          "
          @click="$emit('cancel', false)"
          >{{ $t('back') }}</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Component, Vue, Emit, Prop, VModel } from 'vue-property-decorator'
@Component
/* eslint-disable @typescript-eslint/no-unused-vars */
export default class ConfirmDeleteRelationshipComponent extends Vue {
  @VModel({ default: false }) dialog!: boolean
  @Prop({ default: false }) loading!: boolean

  @Emit('executeDelete')
  emitDelete() {
    return null
  }
}
</script>
