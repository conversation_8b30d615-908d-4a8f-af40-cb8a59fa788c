<template>
  <div>
    <v-row>
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ $t(title) }}
        </h3>
      </v-col>
    </v-row>
    <w-scroll-search-input
      v-show="false"
      :key="refreshKey"
      ref="searchComponent"
      outlined
      class="p-2 m-2"
      index="providers"
      :fields="[
        'page.properties.Slug',
        'page.properties.Name',
        'page.properties.Title',
        'page.properties.Status',
        'page.properties.Avatar.url',
        'page.properties.Services',
        'page.properties.Specialties.properties.Name'
      ]"
      :size="150"
      :max-page="100"
      :search-on-input="false"
      :filter="{
        'page.properties.Services.keyword': 'personal-doctor'
      }"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @hits="handleHit"
      @total="handleTotal"
    />

    <ProviderList
      class="mt-6"
      :providers="hits"
      :chosen-provider="providerChosen"
      @on-choose-provider="($data) => onChooseProvider($data)"
    />
  </div>
</template>

<script lang="ts">
import {
  ref,
  useStore,
  computed,
  onMounted,
  useContext,
  nextTick,
  defineComponent
} from '@nuxtjs/composition-api'
import ProviderList from '../choose-provider/provider-list.vue'
import { usePaymentOrder } from '../../../../repositories'

export default defineComponent({
  components: {
    ProviderList
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const hits: any = ref([])
    const total = ref<number>(0)
    const refreshKey = ref<number>(0)
    const searchComponent: any = ref(null)
    const { $axios, $config }: any = useContext()
    const orderItemId = ref()

    const { executeGetOrderItem } = usePaymentOrder(
      computed(() => ({ _id: props.order?._id })),
      computed(() => ({ polling: computed(() => 0) })),
      computed(() => 'pending')
    )

    executeGetOrderItem().then((res) => {
      const orderItemList: any = res.results
      if (orderItemList.length > 0) {
        const personalDoctorOrderItem = orderItemList.find(
          (item) => item.product.sku === 'personal-doctor-addon'
        )
        orderItemId.value = personalDoctorOrderItem?._id
      }
    })

    const sortBy = ref(['_score', 'page.properties.Rating'])
    const sortDesc = ref([false, true])

    const handleSort = (val: string) => {
      sortBy.value = val
      refreshKey.value++
    }

    const handleHit = (data: any) => {
      hits.value = data
      providerChosen.value = data[0]
    }
    const handleTotal = (event: any) => {
      total.value = event
    }

    const step = computed(() => props.currentStep?.step)
    const title = computed(() => props.currentStep?.content?.title)

    const { state }: any = useStore()
    const patientId = computed<boolean>(
      () => state.checkout?.orderItems[0]?.meta?.baby_id
    )
    const loading = ref<boolean>(true)
    const providerChosen = ref<any>({})

    const isExitEminent = computed(() => {
      if (providerChosen.value?.page?.properties?.Services)
        return providerChosen.value?.page?.properties?.Services.includes(
          'eminent'
        )
    })

    const onChooseProvider = (provider: any) => {
      providerChosen.value = provider
    }

    const submit = async () => {
      await $axios.put(
        `${$config.endpoint}/api/order-item/${orderItemId.value}`,
        {
          update: {
            image: {
              url: providerChosen.value?.page?.properties?.Avatar?.url
            },
            meta: {
              _id: orderItemId.value,
              patientId: patientId.value,
              personalDoctorName: providerChosen.value?.page?.properties?.Name,
              personalDoctorTitle:
                providerChosen.value?.page?.properties?.Title,
              personalDoctorSlug: providerChosen.value?.page?.properties?.Slug,
              personalDoctorId: providerChosen.value?._id,
              benefits: [
                {
                  key: 'personal-doctor',
                  provider: providerChosen.value?._id,
                  user: patientId.value,
                  isEminent: isExitEminent.value
                }
              ]
            }
          }
        }
      )

      emit('submit', {
        benefits: [
          {
            isEminent: isExitEminent.value
          }
        ]
      })
      $axios.put(
        `${$config.endpoint}/ecommerce/order/${props.order?._id}/apply-change`
      )
    }

    const goToPayment = () => true

    const touch = () => true

    onMounted(() => {
      loading.value = true

      nextTick(() => {
        if (searchComponent.value) searchComponent.value.scrollNext()
      })
    })

    return {
      hits,
      step,
      touch,
      title,
      submit,
      sortBy,
      sortDesc,
      loading,
      handleHit,
      handleTotal,
      handleSort,
      refreshKey,
      searchComponent,
      providerChosen,
      onChooseProvider,
      goToPayment
    }
  }
})
</script>
