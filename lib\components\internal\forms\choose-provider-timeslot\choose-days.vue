<template>
  <v-row dense>
    <v-col cols="12">
      <h4 class="body-1 mb-n2 font-weight-medium text--secondary">
        {{ $t('select a date') }}
      </h4>
      <v-sheet class="mx-auto mt-3" color="transparent" tile elevation="0">
        <v-slide-group v-model="chooseDay" center-active show-arrows>
          <v-slide-item
            v-for="(item, index) in dates"
            :key="index"
            v-slot="{ active, toggle }"
            class="mx-2"
          >
            <v-card
              width="min(calc(100% - 8px), 120px)"
              ripple
              style="border-radius: 10px; cursor: pointer"
              class="choose-days-class w-card text--secondary my-2"
              :class="{
                'active-day-card active': active
              }"
              @click="toggle"
            >
              <v-row
                align="center"
                justify="center"
                class="fill-height"
                no-gutters
              >
                <v-col cols="12" class="text-center align-center">
                  <v-icon
                    v-if="
                      item.fee &&
                      item.fee.holiday !== 0 &&
                      !!Number(item.fee.isHoliday) &&
                      item.slots.length !== 0 &&
                      haveHolidayCharge
                    "
                    class="holiday-star"
                    small
                    color="red"
                    >$star</v-icon
                  >
                  <h3
                    class="text-h3 font-weight-medium"
                    :class="{ sold: !item.slots }"
                  >
                    {{ item.date.split('-')[0] }}
                  </h3>
                </v-col>
                <v-col cols="12">
                  <v-divider class="my-1"></v-divider>
                </v-col>
                <v-col cols="12" class="text-center">
                  <div
                    v-if="item.surcharge > 0 && showFee"
                    class="mt-1 text-center red--text font-weight-medium"
                    style="
                      font-size: 0.6rem !important;
                      padding: 2px 0;
                      background-color: rgb(255 135 135 / 18%);
                    "
                  >
                    +
                    <strong>
                      {{
                        $n(getHolidayFee(item.surcharge), {
                          style: 'currency',
                          currency: 'VND'
                        })
                      }}
                    </strong>
                    <br />
                    {{ $t(item.surchargeReason) }}
                  </div>
                  <div class="caption font-weight-bold">
                    {{ $t(formatDay(item.date)) }}
                  </div>
                  <div class="text-caption">
                    {{ $t(formatMonth(item.date)) }}
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </v-slide-item>
        </v-slide-group>
      </v-sheet>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import { TimeSlotsData } from '@lib/repositories'
import { defineComponent, ref, watch } from '@nuxtjs/composition-api'
import type { Ref, PropType } from '@nuxtjs/composition-api'

import dayjs from 'dayjs'

export default defineComponent({
  props: {
    dates: {
      type: Array,
      required: true
    },
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    duration: {
      type: Object as PropType<TimeSlotsData>,
      required: true
    },
    provider: {
      type: Object,
      required: true
    },
    showFee: {
      type: Boolean,
      default: true
    },
    resetDate: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    const chooseDay: Ref<any> = ref(-1)
    // LOGIC
    const formatDay = (date) => {
      const isToday = dayjs(date, 'DD-MM-YYYY').isSame(new Date(), 'd')
      // console.log(dayjs(date).day(), ' line 188')
      const days = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday'
      ]
      if (isToday) return 'today'
      return days[dayjs(date, 'DD-MM-YYYY').day()]
    }
    const formatMonth = (date) => {
      const moths =
        'January, February, March, April, May, June, July, August, September, October, November, December'
          .split(', ')
          .map((m) => m.toLowerCase())
      return moths[dayjs(date, 'DD-MM-YYYY').month()]
    }
    const getHolidayFee = (percent) => {
      const fee = props.orderItem?.meta?.fee || 500
      const slot = Math.floor(
        props.duration.consultTime /
          // (props.orderItem?.product?.meta?.consultTime || 15)
          props.provider.consultTime
      )
      return fee * slot * percent
    }
    watch(chooseDay, () => {
      if (chooseDay.value >= 0) {
        emit('choose-day', props.dates[chooseDay.value])
      } else {
        emit('choose-day', null)
      }
    })
    watch(
      () => props.resetDate,
      () => {
        chooseDay.value = -1
      }
    )
    // emit('choose-day', props.dates[0])
    return {
      chooseDay,
      formatDay,
      formatMonth,
      getHolidayFee
    }
  }
})
</script>

<style scoped>
.choose-days-class {
  background-color: hsla(0, 0%, 86%, 0.24) !important;
}

.active-day-card {
  border: 2px solid var(--v-primary-base);
  /* background-color: var(--v-primary-lighten5); */
  background-color: #00968710;
}

.holiday-star {
  position: absolute !important;
  right: -6px !important;
  top: -8px !important;
  background-color: white !important;
  padding: 2px !important;
  border-radius: 50% !important;
}
</style>
