<template>
  <v-row dense>
    <v-col cols="12">
      <h4
        v-show="times.length > 0"
        class="body-1 font-weight-medium text--secondary"
      >
        {{ $t('choose time')
        }}<span class="text-right font-weight-bold">
          (GMT{{ getTimeZone() }})
        </span>
      </h4>
    </v-col>
    <v-col class="pt-0" cols="12">
      <v-slide-group v-model="selectedIndex" center-active show-arrows>
        <v-slide-item
          v-for="(slot, index) in times"
          :key="index"
          v-slot="{ active, toggle }"
        >
          <v-btn
            class="rounded-lg my-2 mx-2"
            :class="{
              'active-btn': active
            }"
            color="lighten-primary"
            rounded
            depressed
            @click="toggle"
          >
            {{ slot.from }}
          </v-btn>
        </v-slide-item>
      </v-slide-group>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  watch
} from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    times: {
      type: Array,
      required: true
    },
    resetTime: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { $dayjs } = useContext()
    const selectedIndex: Ref<number> = ref(-1)
    // LOGIC
    const getTimeZone = () => {
      // Use a regular expression to replace leading zeros
      return $dayjs()
        .format('Z')
        .split(':')[0]
        .replace(/([+-])0*(\d+)/, '$1$2')
    }
    watch(selectedIndex, (val) => {
      if (val >= 0) {
        emit('choose-slot', {
          from: (props?.times[val] as any)?.from,
          to: (props?.times[val] as any)?.to
        })
      } else {
        emit('choose-slot', null)
      }
    })
    watch(
      () => props.resetTime,
      () => {
        selectedIndex.value = -1
      }
    )
    return {
      getTimeZone,
      selectedIndex
    }
  }
})
</script>
<style scoped>
.active-btn {
  border: 2px solid var(--v-primary-base);
  /* background-color: var(--v-primary-lighten5) !important; */
  background-color: #00968710 !important;
}
</style>
