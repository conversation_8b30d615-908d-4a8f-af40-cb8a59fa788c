<template>
  <v-container fluid class="fill-height pa-0">
    <v-row>
      <v-col cols="12" class="mb-n2">
        <div class="d-flex justify-space-between align-center">
          <h3 class="headline text--secondary title-dark font-weight-bold">
            {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
          </h3>
          <avatar-change-patient :patient="patient" />
        </div>
        <p
          v-if="surchargeLang"
          class="text-subtitle-1 font-italic text--secondary"
        >
          {{
            `${$t(
              'you have chosen consulting in English, surcharge {surchargeLang} per slot.',
              {
                surchargeLang: surchargeLang
              }
            )}`
          }}
        </p>
      </v-col>
      <v-col v-if="isLoadingTimeslot" cols="12">
        <v-skeleton-loader type="heading" class="mb-8"> </v-skeleton-loader>
        <v-skeleton-loader type="image" class="mb-8" max-height="80">
        </v-skeleton-loader>
      </v-col>
      <v-col v-else cols="12" class="pt-2">
        <div v-if="showDurations">
          <h4 class="body-1 font-weight-medium text--secondary">
            {{ $t('choose the duration') }}
          </h4>
          <v-form id="w-durations" ref="durationForm" v-model="isValidDuration">
            <v-radio-group
              v-model="duration"
              class="mt-0 durations-group"
              :disabled="isLoadingTimeslot"
              hide-details
            >
              <v-radio
                v-for="(item, index) in timeslotsData"
                :key="`duration-${index}`"
                :value="item"
                class="duration-item w-card"
                active-class="active"
              >
                <template #label>
                  <div class="d-flex align-center ms-4">
                    <span class="duration-time">
                      {{ item.consultTime }}
                      {{ $t('minutes') }}
                    </span>
                    <template v-if="showFee">
                      /
                      <span class="red--text font-weight-medium">
                        {{ getFee(item) }}
                      </span>
                    </template>
                  </div>
                </template>
              </v-radio>
            </v-radio-group>
          </v-form>
        </div>
      </v-col>
      <v-col
        v-if="dates.length > 0"
        id="choose-days-step-choose-provider-timeslot"
        cols="12"
      >
        <choose-days
          ref="chooseDayComponent"
          :reset-date="resetDate"
          :order="order"
          :order-item="orderItem"
          :dates="dates"
          :duration="duration"
          :show-fee="showFee"
          :provider="provider"
          @choose-day="onChooseDay"
        ></choose-days>
      </v-col>
      <v-col id="choose-times-step-choose-provider-timeslot" cols="12">
        <choose-times
          ref="chooseTimeComponent"
          :reset-time="resetTime"
          :order="order"
          :order-item="orderItem"
          :times="times"
          @choose-slot="onChooseSlot"
        ></choose-times>
      </v-col>
      <v-expand-transition>
        <v-col cols="12">
          <v-alert
            v-show="showAlertGuide"
            text
            outlined
            color="warning"
            class="text--darken"
          >
            <v-form
              id="confirm-step-choose-provider-timeslot"
              ref="guideForm"
              v-model="isValidGuideForm"
            >
              <ul class="grey--text text--darken-4">
                <li v-for="(guide, index) in guideOption" :key="'d-' + index">
                  {{ $t(guide) }}
                </li>
              </ul>
              <v-dialog transition="dialog-bottom-transition" max-width="600">
                <template #activator="{ on, attrs }">
                  <v-btn
                    text
                    color="black"
                    :style="{
                      marginLeft: '-12px',
                      textDecoration: 'underline',
                      fontSize: '12px'
                    }"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <span>{{ $t('more details') }}</span>
                    <v-icon size="20">$chevronRight</v-icon>
                  </v-btn>
                </template>
                <template #default="dialog">
                  <v-card>
                    <v-toolbar color="primary" dark>
                      {{ $t('detailed instructions') }}
                    </v-toolbar>
                    <v-card-text class="pt-4 px-4 pb-0">
                      <ul class="grey--text text--darken-4">
                        <li
                          v-for="(guide, index) in guideOptionDetail"
                          :key="'d-' + index"
                        >
                          {{ $t(guide) }}
                        </li>
                      </ul>
                    </v-card-text>
                    <v-card-actions class="justify-end">
                      <v-btn text color="black" @click="dialog.value = false">
                        Close
                      </v-btn>
                    </v-card-actions>
                  </v-card>
                </template>
              </v-dialog>
              <v-checkbox
                v-model="consentGuide"
                class="text-h4 font-weight-bold mt-0"
                :label="$t('i do consent')"
                :rules="[
                  (v) =>
                    !!v ||
                    $t('this field is required, please check to continue')
                ]"
              />
            </v-form>
          </v-alert>
        </v-col>
      </v-expand-transition>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  useContext,
  watch,
  onMounted,
  useStore
} from '@nuxtjs/composition-api'
import type { ComputedRef, Ref, PropType } from '@nuxtjs/composition-api'

import {
  useProviderTimeslots,
  timeslotAdapter,
  IStep,
  IProviderTimeslot,
  TimeSlotsData
} from '../../../../repositories'
import { calcSurchargeLanguage } from '../../../../compositions'
import AvatarChangePatient from '../../shared/avatar-change-patient.vue'
import ChooseDays from './choose-days.vue'
import ChooseTimes from './choose-times.vue'

export default defineComponent({
  components: {
    ChooseDays,
    ChooseTimes,
    AvatarChangePatient
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { $toast, i18n, $config, $dayjs, $vuetify } = useContext()
    const { commit } = useStore()
    const isMobile = computed<boolean>(() => $vuetify.breakpoint.mobile)

    const patient = computed(() => props.orderItem.meta?.patientInfo)

    const UIcustomChooseTimeslot: ComputedRef<any> = computed(
      () => $config.checkout?.UIcustomize?.chooseProviderTimeslot
    )
    const showFee: ComputedRef<boolean> = computed(
      () => UIcustomChooseTimeslot.value?.showFee ?? true
    )

    const duration: Ref<TimeSlotsData | null> = ref(null)
    const consultTime = computed<number>(() => duration.value?.consultTime || 0)
    const slots = computed<number>(() =>
      Math.floor(consultTime.value / provider.value?.consultTime)
    )
    const fee = computed(() => props.orderItem?.meta.provider?.fee?.phone)
    const lang = computed(() => props.orderItem?.meta?.lang)
    const surchargeLang = computed<string>(() => {
      const surcharge = props.orderItem?.meta?.provider?.fee?.lang[lang.value]

      return surcharge
        ? i18n.n(
            calcSurchargeLanguage(fee.value, surcharge) * slots.value || 0,
            {
              style: 'currency',
              currency: 'VND'
            }
          )
        : ''
    })

    const showDurations: Ref<boolean> = ref(true)
    const durationForm: Ref<HTMLFormElement | null> = ref(null)
    const isValidDuration: Ref<boolean> = ref(false)

    const showAlertGuide: Ref<boolean> = ref(false)
    const guideForm: Ref<HTMLFormElement | null> = ref(null)
    const isValidGuideForm: Ref<boolean> = ref(false)
    const guideOption = reactive([
      'the call is automatically terminated when time is up',
      'each 15-minute block',
      'the doctor may reschedule; contact us if it is inconvenient for you',
      'the time slot is on hold for 5 minutes awaiting payment'
    ])
    const guideOptionDetail = reactive([
      'checkbox timeslot',
      'each 15-minute block is good for 3-5 questions',
      'there will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, Wellcare may reschedule again',
      'this timeslot is on hold for 5 minutes. please move to next step!'
    ])
    const consentGuide: Ref<boolean> = ref(false)

    // commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', true)

    watch(consentGuide, () => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
    })

    const chooseTimeComponent: Ref<any> = ref(null)
    const resetTime: Ref<number> = ref(0)
    const times: Ref<any[]> = ref([])

    const chooseDayComponent: Ref<any> = ref(null)
    const resetDate: Ref<number> = ref(0)
    const daySelect: Ref<string | null> = ref(null)

    const state: any = reactive({
      from: '',
      to: ''
    })

    // const isScrollable = computed<boolean>(() => {
    //   const html: any = document.getElementsByTagName('html')[0]
    //   return html.scrollHeight > html.clientHeight
    // })

    // const optionsScroll = computed(() =>
    //   isScrollable.value ? {} : { container: document.body }
    // )
    // REPOSITORIES
    const {
      loading: isLoadingTimeslot,
      provider,
      timeslotsData,
      executeSearchProvider
    } = useProviderTimeslots(
      computed(() => {
        const options: any = {
          slug: props.orderItem.product.provider.slug
        }
        const flow = props.orderItem?.product?.checkoutFlow?.name
        if (flow) options.flow = flow
        return options
      }),
      true
    )
    // ADAPTER
    const timeslots: Ref<IProviderTimeslot[]> = ref([])
    const { dates, getTimeslotsInDate } = timeslotAdapter(timeslots)
    // LOGIC
    const getFee = (item) => {
      const slots = Math.floor(item.consultTime / provider.value.consultTime)
      const fee = props.orderItem.meta?.fee
      return `${i18n.n(fee * slots, { style: 'currency', currency: 'VND' })}`
    }
    const onChooseDay = (day) => {
      if (!day) {
        state.from = ''
        state.to = ''
        resetDate.value++
        return
      }
      times.value = getTimeslotsInDate(day.date)
      daySelect.value = day.date
      // after change day, reset slot
      resetTime.value++
      state.from = ''
      state.to = ''
    }
    const onChooseSlot = (slot: any) => {
      if (!slot) {
        state.from = ''
        state.to = ''
        resetDate.value++
        return
      }
      state.from = slot.from
      state.to = slot.to
      showAlertGuide.value = true
      const medium =
        i18n.t(props.orderItem?.meta?.medium) ||
        i18n.t(props.orderItem?.product?.type?.name) ||
        i18n.t('video')
      const description = `<span>${slot.from} ${
        daySelect.value
      }</span><br/><span>${medium} - ${duration.value!.consultTime} ${i18n
        .t('minutes')
        .toString()
        .toLowerCase()}</span>`
      const dataPayment = {
        description,
        image: {
          url: provider.value.avatar?.url || ''
        }
      }
      emit('set-order-item', dataPayment)
    }
    // this function handle action after user click "next" button
    // use to validate data at this step
    // if validate data success, the fuction submit() will be called
    const touch = () => {
      // validate duration
      durationForm.value!.validate()
      if (!duration.value) {
        $toast?.global?.appError({ message: i18n.t('please select duration') })
        return false
      }
      // validate date
      if (!daySelect.value) {
        $toast?.global?.appError({ message: i18n.t('please select day') })
        // $vuetify.goTo(chooseDayComponent.value, optionsScroll.value)
        document
          .getElementById('choose-days-step-choose-provider-timeslot')
          ?.scrollIntoView({
            behavior: 'smooth'
          })
        return false
      }
      // validate time
      if (state.from === '' || state.to === '') {
        $toast?.global?.appError({
          message: i18n.t('please choose the time before continuing')
        })
        // $vuetify.goTo(chooseTimeComponent.value, optionsScroll.value)
        document
          .getElementById('choose-times-step-choose-provider-timeslot')
          ?.scrollIntoView({
            behavior: 'smooth'
          })
        return false
      }
      // Validate checkbox duration and timeslot
      guideForm.value!.validate()
      if (!isValidGuideForm.value) {
        // console.log('this should scroll', optionsScroll.value)
        // $vuetify.goTo(guideForm.value, optionsScroll.value)
        // console.log(guideForm.value)
        // guideForm.value.scrollIntoView({
        //   behavior: 'smooth'
        // })
        document
          .getElementById('confirm-step-choose-provider-timeslot')
          ?.scrollIntoView({
            behavior: 'smooth'
          })
        return false
      }
      return true
    }
    // this fuction will emit data at this step to parent
    const submit = () => {
      const checkoutFlow = props.orderItem?.product?.checkoutFlow?.name
      const stateData: any = {
        from: null,
        to: null
      }
      if (
        checkoutFlow === 'consultation-therapy' &&
        provider.value.location.length > 0
      ) {
        stateData.location = (provider.value.location[0] as any)._id
      }
      const from = `${daySelect.value} ${state.from}`
      const to = `${daySelect.value} ${state.to}`
      stateData.from = $dayjs(from, 'DD-MM-YYYY HH:mm')
      stateData.to = $dayjs(to, 'DD-MM-YYYY HH:mm')
      emit('submit', stateData, true)
    }
    // WATCHER
    watch(duration, () => {
      const slots = Math.floor(
        duration.value!.consultTime / provider.value.consultTime
      )
      // showAlertDuration.value = true
      emit('set-order-item', {
        quantity: slots
      })
      timeslots.value = duration.value!.slots
      // after change duration value, reset day
      daySelect.value = null
      times.value = []
      resetDate.value++
    })
    // when get timeslot data success, set default duration option
    // base on number of question from previous step
    watch(timeslotsData, () => {
      const questions = props.orderItem.meta?.questions?.length ?? 1
      const slots = Math.ceil(questions / 5)
      duration.value =
        timeslotsData.find(
          (i) => i.consultTime === provider.value.consultTime * slots
        ) || null
    })

    // first load
    onMounted(() => {
      executeSearchProvider()
      if (!isMobile.value)
        commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
      else commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', true)
    })
    return {
      // duration
      showDurations,
      durationForm,
      isValidDuration,
      duration,
      // fee
      getFee,
      showFee,
      // time & timeslot
      chooseTimeComponent,
      timeslotsData,
      isLoadingTimeslot,
      timeslots,
      resetTime,
      times,
      // date
      chooseDayComponent,
      resetDate,
      dates,
      onChooseDay,
      onChooseSlot,
      submit,
      goToPayment: () => true,
      touch,
      showAlertGuide,
      guideForm,
      isValidGuideForm,
      guideOption,
      guideOptionDetail,
      consentGuide,
      slots,
      surchargeLang,
      consultTime,
      provider,
      patient
    }
  }
})
</script>
<style>
.v-input--radio-group--column .v-radio:not(:last-child):not(:only-child) {
  margin-bottom: 12px;
}
.durations-group .v-input--radio-group__input {
  display: flex;
  flex: 1 1 auto;
  flex-direction: row !important;
  justify-content: space-between;
  flex-wrap: wrap;
}
.duration-item {
  background-color: hsla(0, 0%, 86%, 0.24);
}
.v-radio.duration-item.w-card.theme--light {
  border-radius: 8px;
  padding: 12px;
  width: 100%;
  flex-shrink: 0;
}
@media screen and (min-width: 800px) {
  .durations-group .v-input--radio-group__input {
    flex-wrap: nowrap;
  }
}
@media screen and (min-width: 420px) {
  .v-radio.duration-item.w-card.theme--light {
    flex-basis: max(calc(25% - 10px), 186px);
    margin: 5px;
  }
  .durations-group .v-input--radio-group__input {
    margin: -5px;
  }
}
.duration-item span.duration-time,
.duration-item span {
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
}
.duration-item .active {
  background-color: #0096872c;
  box-shadow: inset 0 0 0px 2px var(--v-primary-base);
}
.duration-item .active span.duration-time {
  color: var(--v-primary-base);
}
.v-application--is-ltr .v-input--selection-controls__input {
  margin-right: 3px !important;
}
.v-btn {
  text-transform: none !important;
}
</style>
