<template>
  <v-row key="choose-provider-component">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12" class="pt-2">
      <!-- <h3 class="rounded-lg subtitle-1 font-weight-medium text--secondary">
        {{ $t('a doctor of your choice') }}
      </h3> -->
      <!-- <template #actions>
              <v-icon v-if="panel === 1" color="primary">$checkCircle</v-icon>
              <v-icon v-else>$circleOutline</v-icon>
            </template> -->
      <v-card flat class="mt-4">
        <!-- <w-pagination-search-input
          ref="paginationSearch"
          :index="'catalog_product'"
          :match-fields="['output.name', 'output.provider.specialty.vi.title']"
          :fields="['output.*']"
          :filter="filterQuery"
          :from="from"
          :sort-by="['score.rating']"
          :sort-desc="[true]"
          :hide-details="true"
          class="w-pagination-search"
          clearable
          :clear-icon="'$close-circle'"
          :placeholder="$t('search doctor by name')"
          color="primary"
          outlined
          :size="150"
          @hits="hits = $event"
          @total="total = $event"
        /> -->
        <v-text-field
          v-model="query"
          :placeholder="$t('search doctor by name')"
          outlined
          clearable
        ></v-text-field>
      </v-card>
      <v-container class="pa-0">
        <v-card
          v-if="providerList && providerList.length === 0 && !loading"
          class="d-flex flex-column align-center"
          style="background-color: transparent"
          flat
        >
          <div class="mt-2 text-center text-14">
            {{ $t('empty search') }}.
            {{ $t('please try again') }}
          </div>
        </v-card>
        <v-skeleton-loader
          v-if="loading"
          type="image"
          height="50"
        ></v-skeleton-loader>
        <template v-else>
          <v-card
            v-for="(provider, index) in providerList"
            :key="'w-provider' + index"
            tile
            hover
            class="mt-5 pa-1 rounded-lg d-lfex justify-center align-center"
            style="
              position: relative;
              box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px -2px !important;
            "
            @click="selectItem(provider, index)"
          >
            <v-list-item
              class="pl-2 d-lfex justify-center align-start"
              :class="selectedIndex === index ? 'active-card' : ''"
            >
              <div
                class="active-card-check-icon"
                :style="{
                  opacity: selectedIndex === index ? '1' : '0',
                  visibility: selectedIndex === index ? 'visible' : 'hidden'
                }"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="10" cy="10" r="10" fill="#149E91" />
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M14.4603 6.23449C14.7524 5.91169 15.2509 5.88677 15.5737 6.17883C15.8825 6.45819 15.9188 6.92637 15.6657 7.24906L15.6294 7.29213L9.8845 13.6411C9.46222 14.1078 8.74515 14.1555 8.26457 13.7489L4.44562 10.5178C4.11327 10.2366 4.07182 9.73931 4.35304 9.407C4.62203 9.08913 5.08881 9.03739 5.41974 9.27958L5.464 9.31443L8.76518 12.1075C8.88533 12.2092 9.06459 12.1972 9.17016 12.0806L14.4603 6.23449Z"
                    fill="white"
                  />
                </svg>
              </div>
              <v-list-item-avatar
                size="60"
                class="mr-2"
                style="margin-top: -20px"
              >
                <w-avatar
                  :key="provider.page.properties._id"
                  :name="provider.page.properties.Name"
                  :src="getAvatarUrl(provider)"
                  :user-id="provider.page.properties._id"
                  size="50"
                ></w-avatar>
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title class="font-weight-bold m-card-title">
                  {{ getProviderName(provider) }}
                </v-list-item-title>
                <!-- <v-list-item-subtitle
                  v-if="provider.output.questionProduct"
                  class="text-caption d-flex"
                  style="
                    letter-spacing: normal !important;
                    margin-top: 3px;
                    font-size: 13px !important;
                    font-family: 'Quicksand' !important;
                  "
                  >{{ $t('response before') }}
                  {{ formatTime(provider.output, 'HH:mm') }}
                  {{ formatTime(provider.output, 'DD/MM/YY') }}
                </v-list-item-subtitle> -->
                <div
                  class="d-flex"
                  style="gap: 8px; margin-top: 5px; flex-wrap: wrap"
                >
                  <div
                    v-for="(itemSpecialty, indexSpecialty) in getSpecialty(
                      provider
                    )"
                    :key="'w-specialty' + indexSpecialty"
                    class="rounded-lg text-14"
                    style="
                      background-color: #f4f4f4;
                      color: #808896;
                      padding: 8px 12px;
                    "
                  >
                    {{ itemSpecialty.properties.Name }}
                  </div>
                </div>
              </v-list-item-content>
            </v-list-item>
          </v-card>
        </template>
      </v-container>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  reactive,
  useContext,
  computed
} from '@nuxtjs/composition-api'
import type { Ref, PropType } from '@nuxtjs/composition-api'
import { debouncedRef } from '@vueuse/core'

import { IStep } from '@lib/repositories/workflow/flow.interface'
import { updateOrder } from '../../../../repositories/order/update-order'
import { searchProviderElastic } from '../../../../repositories/provider/use-search-provider-elasic'

export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { execute: executeUpdateOrder } = updateOrder(
      computed(() => props.order)
    )
    const { $toast, $dayjs } = useContext()
    const query = ref('')
    const queryDebounce = debouncedRef(query, 800)
    const { results, loading } = searchProviderElastic(
      computed(() => ({
        from: 0,
        size: 150,
        query: {
          bool: {
            filter: [
              {
                terms: {
                  'page.properties.Status.keyword': ['Active']
                }
              }
            ],
            should: [],
            must: queryDebounce.value
              ? [
                  {
                    multi_match: {
                      fields: [
                        'page.properties.Name^2',
                        'page.properties.Specialties.properties.Name'
                      ],
                      query: queryDebounce.value
                    }
                  }
                ]
              : [],
            must_not: []
          }
        },
        sort: [
          {
            'page.properties.Rating': 'desc'
          },
          '_score'
        ],
        _source: {
          excludes: [],
          includes: ['page.properties', 'page.cover', 'output.meta']
        },
        highlight: {}
      }))
    )

    const providerList: Ref<any[]> = ref([])
    // const pagiantionSearch: Ref<any> = ref(null)
    // const from: Ref<number> = ref(0)
    // const hits: Ref<any[]> = ref([])
    // const filterQuery: Ref<any> = ref({
    //   'output.type.keyword': 'consultation-question-provider',
    //   'output.state': 'published'
    // })
    // const itemPerPage: Ref<number> = ref(10)
    // const total: Ref<number> = ref(1)
    // const page: Ref<number> = ref(1)
    // const totalPage: Ref<number> = ref(1)
    // const panel: Ref<number> = ref(0)
    const state: any = reactive({ data: {} })
    const selectedIndex: Ref<number> = ref(null)
    // LOGIC
    // const getSpecialty = (provider) =>
    //   provider.output?.provider?.specialty?.vi?.title
    //     .join(', ')
    //     .replace(/-/g, ' ') ||
    //   provider.output?.provider?.specialty?.vi?.title.join(', ')
    const getSpecialty = (provider) =>
      provider.page?.properties?.Specialties || []

    watch(results, () => {
      providerList.value = results.value.map((item) => {
        return {
          isSelected: false,
          ...item
        }
      })
    })
    // watch(total, () => {
    //   totalPage.value = Math.ceil(total.value / itemPerPage.value)
    // })
    // const nextPage = () => {
    //   page.value++
    //   from.value += itemPerPage.value
    // }
    // const prevPage = () => {
    //   page.value--
    //   from.value -= itemPerPage.value
    // }
    const selectItem = (item, index) => {
      // for (let i = 0; i < providerList.value.length; i++) {
      //   if (i === index) providerList.value[i].isSelected = true
      //   else providerList.value[i].isSelected = false
      // }

      selectedIndex.value = index
      state.data = item
      emit('set-order-item', {
        image: {
          url: getAvatarUrl(item)
        }
      })
    }
    const formatTime = (time, format) => {
      const expectAnsweredAt = time?.questionProduct?.expectAnsweredAt
      return $dayjs(expectAnsweredAt).format(format)
    }
    const submit = () => {
      if (!touch()) return
      // FIX SSR: remove null item in array
      const propsTags = Array.isArray(props.order?.tags)
        ? props.order?.tags.filter((e) => e)
        : []
      const orderTags = [
        `${props.orderItem?.product?.name} - ${state.data?.output?.provider?.name}`,
        ...propsTags
      ]
      executeUpdateOrder({ _id: props.order._id, tags: orderTags })
      emit('submit', {
        pricingProduct: 'consultation-question-specific',
        provider: state.data?.page?.properties,
        // providerInfo: state.data?.output.provider,
        providerUser: state.data?.page?._id
      })
    }
    const touch = () => {
      if (!state.data?.page?.properties) {
        $toast.error('please choose a doctor')
        return false
      }
      return true
    }
    const getAvatarUrl = (provider) => {
      return provider?.page?.properties?.Avatar?.url ?? ''
    }
    const getProviderName = (provider) => provider.page.properties.Name
    return {
      providerList,
      // pagiantionSearch,
      // from,
      // hits,
      // filterQuery,
      // itemPerPage,
      // total,
      getSpecialty,
      // page,
      // totalPage,
      // nextPage,
      // prevPage,
      selectItem,
      // panel,
      loading,
      formatTime,
      touch,
      submit,
      getAvatarUrl,
      getProviderName,
      selectedIndex,
      query
    }
  }
})
</script>

<style scoped>
.w-pagination-search >>> .v-input__slot {
  font-size: 15px;
  padding-left: 20px !important;
}

.active-card {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;
  transform: all 0.25s linear;
}
.active-card-check-icon {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
}
@media only screen and (max-width: 500px) {
  div >>> .v-expansion-panel-content__wrap {
    padding: 0 16px 16px !important;
  }
}
</style>
