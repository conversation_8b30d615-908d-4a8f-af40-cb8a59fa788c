<template>
  <div>
    <div v-if="provider" class="expert-information mt-8">
      <w-avatar
        :key="refreshKey"
        :src="getAvatar()"
        :name="getName()"
        size="88"
        class="expert-avatar"
      />
      <div class="pt-11">
        <div class="expert-detail">
          <p class="expert-name">{{ getTitle() }}. {{ getName() }}</p>
          <p v-dompurify-html="getHighlight()" class="expert-highlight" />
        </div>
      </div>
    </div>
    <v-skeleton-loader
      v-else
      type="list-item-avatar-three-line, article, actions"
    ></v-skeleton-loader>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'ProviderInfo',
  props: {
    provider: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const refreshKey = ref(0)

    const getAvatar = () =>
      props.provider?.avatar?.url || props.provider?.output?.avatar?.url || ''

    const getTitle = () =>
      props.provider?.title || props.provider?.output?.title || ''

    const getName = () =>
      props.provider?.name || props.provider?.output?.name || ''

    const getHighlight = () =>
      props.provider?.highlight || props.provider?.output?.highlight || ''

    watch(
      () => props.provider,
      () => {
        refreshKey.value++
      },
      {
        deep: true
      }
    )

    return {
      refreshKey,
      getAvatar,
      getTitle,
      getName,
      getHighlight
    }
  }
})
</script>

<style scoped>
.expert-information {
  position: relative;
}
.expert-avatar {
  border: 1px solid #009688;
  position: absolute !important;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.expert-detail {
  background-color: rgba(63, 161, 151, 0.2);
  border: 1px solid #009688;
  border-radius: 8px;
}
.expert-name {
  font-weight: 700;
  margin: 48px 0 8px;
  text-align: center;
}
.expert-highlight {
  font-size: 14px;
  padding: 0 12px;
  display: block;
}
</style>
