<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  computed
} from '@nuxtjs/composition-api'
import { useSearchProvider } from '../../../../repositories'

export default defineComponent({
  name: 'ProviderItem',
  props: {
    provider: {
      type: Object,
      required: true,
      default: () => ({})
    },
    active: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const { i18n } = useContext()
    const dialog = ref(false)
    const locale = computed(() => i18n.locale)

    const searchOption = ref({
      slug: `${props.provider?.page?.properties?.Slug}`,
      locale: locale.value,
      site: 'khamtuxa.vn',
      status: ['Published', 'reindex'],
      type: 'Person'
    })

    const { data, loading, executeQuery } = useSearchProvider(searchOption)

    const openDialog = (event: Event) => {
      event.stopPropagation()
      executeQuery()
      dialog.value = true
    }

    const closeDialog = () => {
      dialog.value = false
    }

    const getAvatar = () => {
      return (
        props?.provider?.page?.properties?.Avatar?.url ||
        'https://cdn-icons-png.flaticon.com/512/9193/9193824.png'
      )
    }

    const getTitle = () => props.provider?.page?.properties?.Title || ''

    const isExitEminent = computed(() => {
      if (props.provider.page?.properties?.Services)
        return props.provider.page?.properties?.Services.includes('eminent')
    })

    const getName = () => props.provider?.page?.properties?.Name || ''

    const getSpecialties = () => {
      let names
      const specialties = props.provider?.page?.properties?.Specialties
      if (specialties && specialties.length > 0) {
        names = specialties.slice(0, 3).map((item) => item?.properties?.Name)
      }
      return names
    }

    const getDescription = () => {
      return (
        props.provider?.page?.properties?.Description ||
        'No description available.'
      )
    }

    return {
      data,
      dialog,
      loading,
      getName,
      getTitle,
      openDialog,
      isExitEminent,
      closeDialog,
      getAvatar,
      searchOption,
      getSpecialties,
      getDescription
    }
  }
})
</script>
<template>
  <div class="rounded-lg d-flex">
    <div style="position: relative">
      <v-img
        :src="getAvatar()"
        class="object-cover rounded-lg"
        style="height: 100px; width: 100px; display: block"
      />
      <div
        v-if="isExitEminent"
        style="
          position: absolute;
          left: 0px;
          bottom: 0px;
          width: 100%;
          opacity: 0.8;
          background: #fef9c3;
          color: #a16207;
        "
        class="d-flex align-center justify-center"
      >
        <span class="text-14">{{ $t('Eminent') }}</span>
        <v-icon left small color="#facc15" class="mx-0">$star</v-icon>
      </div>
    </div>
    <v-card-text class="ml-3 pa-0">
      <div>
        <div class="d-flex align-center">
          <p
            class="font-weight-bold text-16 mb-1"
            style="
              display: -webkit-box;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              line-clamp: 1;
              -webkit-line-clamp: 1;
            "
          >
            {{ getTitle() }}. {{ getName() }}
          </p>
        </div>
        <p class="specialties-text">
          <template v-for="(specialty, index) in getSpecialties()">
            {{ specialty.replace(/-/g, ', ') || specialty
            }}<template v-if="index < getSpecialties().length - 1">, </template
            ><template v-else>.</template>
          </template>
        </p>
      </div>
    </v-card-text>
    <div v-if="active" class="chosen">
      <v-icon color="primary">$checkCircle</v-icon>
    </div>

    <!-- Dialog -->
    <v-dialog v-model="dialog" max-width="600px">
      <v-card>
        <v-card-title class="text-20 font-weight-bold">
          {{ getTitle() }}. {{ getName() }}
          <v-icon color="primary--text" left small>$star</v-icon>
          <!-- <v-chip v-if="isExitEminent" color="primary" small class="ml-2" label>
            Chuyên gia
          </v-chip> -->
        </v-card-title>
        <v-card-text>
          <p>
            <strong>{{ $t('list of specialties') }}:</strong>
            <template v-for="(specialty, index) in getSpecialties()">
              {{ specialty.replace(/-/g, ', ') || specialty
              }}<template v-if="index < getSpecialties().length - 1"
                >, </template
              ><template v-else>.</template>
            </template>
          </p>
          <v-skeleton-loader v-if="loading" type="list-item-three-line" />
          <v-slide-x-transition v-else>
            <p class="mb-0">
              <strong>{{ $t('about') }}</strong
              >: {{ data }}
            </p>
          </v-slide-x-transition>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text color="primary" @click="closeDialog">{{
            $t('close')
          }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped>
.w-full {
  width: 100%;
}
.object-cover {
  object-fit: cover;
}
.chosen {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50% !important;
  height: fit-content;
  position: absolute;
  top: -16px;
  left: 4px;
}
.specialties-text {
  line-height: 1.5;
  margin-bottom: 0;
}
</style>
