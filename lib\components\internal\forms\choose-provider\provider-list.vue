<template>
  <v-row dense>
    <v-list class="product-list">
      <v-list-item-group v-model="modelProvider">
        <v-list-item
          v-for="(provider, index) in providers"
          :key="index"
          class="pa-2 rounded-lg mb-5"
        >
          <ProviderItem
            class="provider-item"
            :provider="provider"
            :active="modelProvider === index"
          />
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  useStore,
  onMounted,
  onUnmounted
} from '@nuxtjs/composition-api'
import ProviderItem from './provider-item.vue'

export default defineComponent({
  name: 'ProviderList',
  components: {
    ProviderItem
  },
  props: {
    providers: {
      type: Array,
      default: () => []
    },
    chosenProvider: {
      type: Object,
      default: () => null
    }
  },
  setup(props, { emit }) {
    const modelProvider = ref(null)
    const { commit }: any = useStore()

    watch(modelProvider, (newValue) => {
      if (Number(modelProvider.value) >= 0) {
        emit('on-choose-provider', props.providers[newValue])
        commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
      } else {
        commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', true)
      }
    })

    onMounted(() => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', true)
    })

    onUnmounted(() => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
    })

    return {
      modelProvider
    }
  }
})
</script>
<style scoped>
.product-list {
  width: 100% !important;
}

.product-list >>> .v-list-item--link:before {
  border-radius: 8px;
  background-color: rgba(0, 150, 135, 0.04) !important;
}
.product-list >>> .v-list-item--active:before {
  border: solid 1px #009688;
  opacity: 1;
}
</style>
