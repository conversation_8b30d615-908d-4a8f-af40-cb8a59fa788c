<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  reactive,
  ref,
  useContext,
  useRoute,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IOrderItem, IStep } from '../../../repositories'
import { mentalHealth } from '../../../utils'

import DialogCheckProviderTimeSlot from '../dialog/check-time-slot.vue'
import ChooseTarget from './choose-target.vue'

export default defineComponent({
  components: {
    ChooseTarget,
    DialogCheckProviderTimeSlot
  },
  props: {
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem<any>>,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { i18n, $toast, $vuetify } = useContext()
    const { commit, state } = useStore()
    const route = useRoute()

    const reason = ref<string | string[]>('')
    const dialog = ref<boolean>(false)
    const stepTarget = reactive<Partial<IStep>>({
      content: {
        title: 'choose target'
      }
    })

    const orderItem = computed<IOrderItem<any>>(() => props.orderItem)
    const meta = computed<any>(() => orderItem.value?.meta)

    const target = computed<string>(
      () => (route.value.query?.target as string) || ''
    )
    const reasons = computed<any>(
      () => mentalHealth[target.value]?.reason || {}
    )
    const locale = computed<string>(() => i18n.locale)
    const isMobile = computed<boolean>(() => $vuetify.breakpoint.mobile)
    const productName = computed<string>(
      () => orderItem.value?.product?.name || ''
    )
    const safeArea = computed(() => state?.app?.safeArea || { top: 0 })

    const touch = (): boolean => {
      const isValid = reason.value.length || reason.value
      if (!isValid) {
        $toast.global.appWarning({
          message: i18n.t('please choose a chief complaint before proceeding.')
        })
        return false
      }
      return true
    }

    const submit = () => {
      emit('submit', {
        target: target.value,
        reason: reason.value
        // expectations: []
      })
    }

    const setDisableButtonNextStep = (val: boolean) => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', val)
    }

    watch(reason, (val) => {
      setDisableButtonNextStep(!val)
    })

    watch(
      () => meta.value?.reason,
      (val) => {
        const isReasonMeta = reasons.value?.data?.find(
          (item) => item[locale.value] === val
        )

        if (isReasonMeta) {
          reason.value = val
          setDisableButtonNextStep(!reason.value)
          return
        }
        setDisableButtonNextStep(true)
      },
      { immediate: true }
    )

    onBeforeMount(() => {
      if (!target.value) {
        dialog.value = true
      }
    })

    return {
      reason,
      reasons,
      locale,
      target,
      touch,
      submit,
      dialog,
      isMobile,
      productName,
      stepTarget,
      safeArea
    }
  }
})
</script>

<template>
  <v-row no-gutters>
    <v-col v-if="reasons && reasons.data" cols="12">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ reasons[locale] }}
      </h3>
    </v-col>
    <v-col cols="12">
      <template v-if="reasons.type === 'radio'">
        <v-radio-group v-model="reason" :disabled="loading">
          <v-row>
            <v-col v-for="(item, index) in reasons.data" :key="index" cols="12">
              <v-radio :label="item[locale]" :value="item[locale]"></v-radio>
            </v-col>
          </v-row> </v-radio-group
      ></template>
      <template v-else-if="reasons.type === 'checkbox'">
        <v-row>
          <v-col v-for="(item, index) in reasons.data" :key="index" cols="12">
            <v-checkbox
              v-model="reason"
              :disabled="loading"
              :label="item[locale]"
              :value="item[locale]"
            ></v-checkbox>
          </v-col>
        </v-row>
      </template>
    </v-col>

    <v-dialog
      v-model="dialog"
      transition="dialog-bottom-transition"
      persistent
      max-width="600"
      :fullscreen="isMobile"
    >
      <template #default="dialog">
        <v-card class="d-flex flex-column justify-space-between">
          <div>
            <v-toolbar
              elevation="1"
              outlined
              class="d-flex justify-center"
              :color="isMobile ? 'white' : 'primary'"
              :style="{
                paddingTop: safeArea.top + 'px',
                color: isMobile ? '#000' : '#fff'
              }"
              ><strong>{{ productName }}</strong></v-toolbar
            >
            <v-card-text
              class="my-2"
              :style="{
                height: '50vh'
              }"
            >
              <ChooseTarget
                :current-step="stepTarget"
                :order-item="orderItem"
              />
            </v-card-text>
          </div>
          <v-card-actions>
            <v-btn
              block
              rounded
              :disabled="!target"
              :class="[
                'text-uppercase',
                {
                  'mb-8': isMobile
                }
              ]"
              color="primary"
              @click="dialog.value = false"
              >{{ $t('next') }}</v-btn
            >
          </v-card-actions>
        </v-card>
      </template>
    </v-dialog>

    <DialogCheckProviderTimeSlot :provider="orderItem.product.provider" />
  </v-row>
</template>
