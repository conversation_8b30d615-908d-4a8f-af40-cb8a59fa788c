<template>
  <v-row :key="'prepare-first-meeting-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12" class="pt-2 px-4">
      <div>
        <v-chip-group v-model="day" color="primary" mandatory>
          <v-chip class="rounded-lg pa-5 chip" value="today" outlined
            ><h4>{{ $t('today') }}</h4></v-chip
          >
          <v-chip class="rounded-lg pa-5 chip" value="tomorrow" outlined
            ><h4>{{ $t('tomorrow') }}</h4></v-chip
          >
        </v-chip-group>

        <template v-for="time in times">
          <v-checkbox
            :key="time.key"
            v-model="timeSlots"
            hide-details="auto"
            :value="{ text: time.text, value: time.value }"
            multiple
            class="checkbox"
          >
            <template #label>
              <div class="d-flex flex-column px-1 ml-3" style="gap: 2px">
                <h4 style="font-size: large">{{ time.label.title }}</h4>
                <small>{{ time.label.brief }}</small>
              </div>
            </template>
          </v-checkbox>
        </template>

        <br />

        <v-form ref="validationProvider">
          <v-textarea
            v-model="displayTimeslot"
            outlined
            :label="$t('time slot')"
            :rules="[(value) => !!value || $t('required field')]"
          >
          </v-textarea>
        </v-form>
      </div>
    </v-col>
  </v-row>
</template>
<!-- eslint-disable @typescript-eslint/no-unused-vars -->

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  onMounted,
  useContext
} from '@nuxtjs/composition-api'
import useRequestTime from '../../../compositions/use-request-time'
export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(_props, { emit: _emit }) {
    const { i18n } = useContext()
    const { times, time, day, timeSlots, displayTimeslot, requestedSlots } =
      useRequestTime()
    watch(time, () => {
      displayTimeslot.value = time.value
      _emit('submit', {
        timeSlots: {
          display: displayTimeslot.value,
          items: timeSlots.value,
          day: day.value
        },
        requestedSlots: requestedSlots.value
      })
    })
    onMounted(() => {
      day.value = _props.orderItem?.meta?.timeSlots?.day || 'today'
      timeSlots.value = _props.orderItem?.meta?.timeSlots?.items || []
      displayTimeslot.value = _props.orderItem?.meta?.timeSlots?.display
    })
    const validationProvider = ref(null)
    const touch = async () => await validationProvider.value.validate()
    const submit = () => {
      _emit('set-order-item', {
        description:
          i18n.t('first meeting') +
          ' ' +
          _props.orderItem?.meta?.patientInfo?.name +
          '. ' +
          i18n.t('requested time') +
          ':' +
          ' ' +
          displayTimeslot.value
      })
      _emit('submit', true)
      // setTimeout(() => router.push(`/payment/order/${_props.order._id}`), 200)
    }
    const goToPayment = () => true
    return {
      times,
      time,
      day,
      timeSlots,
      touch,
      submit,
      validationProvider,
      displayTimeslot,
      goToPayment
    }
  }
})
</script>
<style scoped>
.checkbox >>> span {
  color: var(--v-primary-base) !important;
}
.chip {
  border: 1px solid var(--v-primary-base);
}
</style>
