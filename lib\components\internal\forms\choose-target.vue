<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  useRouter,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IOrderItem, IStep } from '../../../repositories'

export default defineComponent({
  props: {
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem<any>>,
      required: true
    }
  },
  setup(_, { emit }) {
    const { $toast, i18n } = useContext()
    const { commit } = useStore()
    const router = useRouter()

    const target = ref<string>('')

    const targets = [
      {
        label: 'my self',
        value: 'myself',
        cover: '/category-mental-self.png',
        onClick: () => {},
        color: '#51689c'
      },
      {
        label: 'teenager',
        value: 'teenager',
        cover: '/category-mental-kids.png',
        onClick: () => {},
        color: '#b85d50'
      },
      {
        label: 'couples',
        value: 'couples',
        cover: '/category-mental-couple.png',
        onClick: () => {},
        color: '#729486'
      }
    ]

    const touch = (): boolean => {
      if (!target.value) {
        $toast.global.appWarning({
          message: i18n.t(
            'please select at least one option before proceeding.'
          )
        })
        return false
      }
      return true
    }

    const submit = () => {
      emit('submit', {
        target: target.value
        // reason: ''
      })
    }

    watch(
      target,
      (val) => {
        commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', !val)
        router.push({
          query: {
            target: val
          }
        })
      },
      {
        immediate: true
      }
    )

    return {
      target,
      targets,
      touch,
      submit
    }
  }
})
</script>

<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <h3
        class="headline text--secondary title-dark font-weight-bold text-capitalize"
      >
        {{ $t(currentStep.content.title) }}
      </h3>
      <h4 class="body-1 text--secondary">
        {{ $t(currentStep.content.subtitle) }}
      </h4>
    </v-col>

    <v-col cols="12">
      <v-item-group v-model="target">
        <v-row>
          <v-col
            v-for="(item, index) in targets"
            :key="index"
            cols="12"
            md="6"
            sm="6"
          >
            <v-item v-slot="{ active, toggle }" :value="item.value">
              <v-badge
                :value="active"
                left
                overlap
                icon="$check"
                color="primary"
                style="width: 100%"
              >
                <v-card
                  height="100"
                  elevation="0"
                  depressed
                  ripple
                  rounded="lg"
                  :style="{
                    backgroundImage: `linear-gradient(270deg, rgba(21, 12, 7, 0) 0%, ${item.color} 80%), url(${item.cover})`,
                    'background-size': 'cover',
                    'background-position': 'center',
                    border: active ? '4px solid var(--v-primary-base)' : 'none',
                    transition: 'border .5s'
                  }"
                  @click="toggle"
                >
                  <v-card-title
                    class="font-weight-bold white--text d-flex justify-space-between align-center text-capitalize"
                    :style="{
                      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
                    }"
                    >{{ $t(item.label) }}
                  </v-card-title>
                </v-card>
              </v-badge>
            </v-item>
          </v-col>
        </v-row>
      </v-item-group>
    </v-col>
  </v-row>
</template>
