<template>
  <v-row :key="'question-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
      <!-- <h1>
        {{ currentStep }}
      </h1> -->
    </v-col>
    <v-col cols="12" class="pt-2">
      <div class="text-body-2 text-sm-subtitle-1">
        <span v-dompurify-html="$t('pre confirm step question')"></span>
        <a @click="goCheckoutProduct()">
          {{ $t('phone or video call') }}
          <span v-show="$config.checkout.agent === 'aia'"
            >{{ $t('or') }} {{ $t('consultation-inperson') }}</span
          >
        </a>
        <span v-dompurify-html="$t('confirm step question')"></span>
      </div>
      <div class="text-body-2 text-sm-subtitle-1 mt-5">
        {{ $t('before proceeding with short question, please note that:') }}
      </div>
      <div class="text-body-2 text-sm-subtitle-1 mt-5">
        <ul class="text-justify pl-4" style="word-break: break-word">
          <li
            v-dompurify-html="
              $t('question includes only text, no images or video clips.')
            "
            class="text-body-2 text-sm-subtitle-1 text-justify"
          >
            {{ $t('question includes only text, no images or video clips.') }}
          </li>
          <li
            v-dompurify-html="
              $t(
                'our doctors responses will not include any diagnoses or medicines'
              )
            "
            class="text-body-2 text-sm-subtitle-1 text-justify"
          >
            {{
              $t(
                'our doctors responses will not include any diagnoses or medicines'
              )
            }}
          </li>
          <li class="text-body-2 text-sm-subtitle-1 text-justify">
            {{
              $t('Doctor and Wellcare reserve the right to decline, whereas:')
            }}
          </li>
        </ul>
        <div class="text-body-2 text-sm-subtitle-1 text-justify pl-4 mt-1">
          {{
            $t('1. the inquiry is having less information than what is needed.')
          }}
        </div>
        <div class="text-body-2 text-sm-subtitle-1 text-justify pl-4">
          {{
            $t(
              '2. the patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon'
            )
          }}
        </div>
        <div
          v-dompurify-html="
            $t('full refund to your Wellcare’s account when declined')
          "
          class="text-body-2 text-sm-subtitle-1 text-justify pl-4 mb-1"
        >
          {{ $t('full refund to your Wellcare’s account when declined') }}
        </div>
      </div>
    </v-col>
    <v-col cols="12" class="mb-5 px-5 py-0">
      <v-radio-group
        ref="confirmOption"
        v-model="selectMode"
        :disabled="loadingOption"
        :error="error"
        :error-messages="message"
        class="mt-0"
      >
        <v-radio label="Câu hỏi ngắn" value="question">
          <template #label>
            <div class="pa-2 pl-0">
              <div class="text--primary text-body-2 text-sm-subtitle-1">
                {{ $t('continue question') }}
              </div>
            </div>
          </template>
        </v-radio>
        <v-radio label="Khám qua gọi thoại hoặc video" value="indepth">
          <template #label>
            <div>
              <div class="text--primary text-body-2 text-sm-subtitle-1">
                {{ $t('switch to teleconsultation') }}
              </div>
            </div>
          </template>
        </v-radio>
        <v-radio
          v-show="$config.checkout.agent === 'aia'"
          label="Gặp mặt trực tiếp"
          value="inperson"
        >
          <template #label>
            <div>
              <div class="text--primary text-body-2 text-sm-subtitle-1">
                {{ $t('consultation-inperson') }}
              </div>
            </div>
          </template>
        </v-radio>
      </v-radio-group>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  computed,
  // useRoute,
  useStore,
  onMounted,
  useRouter,
  reactive,
  useContext
} from '@nuxtjs/composition-api'
import type { Ref, ComputedRef } from '@nuxtjs/composition-api'

import { Store } from 'vuex'
import { useProductOrder } from '../../../repositories'

export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    // const route = useRoute()
    const { i18n, $vuetify } = useContext()
    const router = useRouter()
    // const { error: nuxtError } = useContext()
    const store: Store<any> = useStore()
    const { orderItem } = useProductOrder()
    // const { provider } = useProviderTimeslots(ref({
    //   slug: props.orderItem?.product?.provider?.slug
    // }))
    const workFlowData = ref(store.state.checkout.workFlowData)
    // const slugProvider: Ref<string> = ref('')
    const selectMode: Ref<string> = ref('')
    // const case: Ref<string> = ref("")
    const loadingOption: Ref<boolean> = ref(false)
    // const templateQuestion: Ref<string> = ref('6018bd221725ef82e5191a0c')
    // const templateIndepth: Ref<string> = ref('5d53895885d660a48291ace8')
    const message: any = ref('')
    const error = ref(false)
    const state = reactive({
      acceptedRuleQuestion: false,
      confirmType: ''
    })
    const confirmOption: Ref<HTMLElement | null> = ref(null)
    const isScrollable = computed<boolean>(() => {
      const html: any = document.getElementsByTagName('html')[0]
      return html.scrollHeight > html.clientHeight
    })

    const optionsScroll = computed(() =>
      isScrollable.value ? {} : { container: document.body }
    )

    const provider: ComputedRef<any> = computed(() => {
      let provider = orderItem.value?.product?.provider
      if (!provider) provider = orderItem.value?.meta?.provider
      return provider
    })

    const step: ComputedRef<number | any> = computed(
      () => store.state.checkout.currentStep?.step
    )
    const title: ComputedRef<number> = computed(
      () => store.state.checkout.currentStep?.content?.title || 'choose package'
    )

    const checkForm = (text = '' as string, state = false as boolean) => {
      error.value = state
      message.value = text
    }

    const goCheckoutProduct = () => {
      // if (!props.orderItem?.product?.provider?.slug) {
      //   return nuxtError({
      //     statusCode: 406,
      //     message: 'provider slug not found on orderItem product'
      //   })
      // }
      // console.log(provider.value?.slug, ' line 186')
      if (selectMode.value === 'inperson')
        router.push({
          path: `/checkout/gap-mat-truc-tiep-${provider.value?.slug}`
        })
      else if (selectMode.value === 'indepth')
        router.push({
          path: `/checkout/${provider.value?.slug}`
        })
    }

    const acceptedRuleQuestion = (question?: boolean | any) => {
      if (typeof question === 'boolean') {
        selectMode.value =
          props.orderItem?.meta?.acceptedRuleQuestion === true
            ? 'question'
            : 'indepth'
      } else {
        selectMode.value = ''
      }
    }

    watch(selectMode, (value) => {
      loadingOption.value = true
      const acceptedRuleQuestion = value === 'question'
      // emit('emit-data', { acceptedRuleQuestion, confirmType: value })
      state.acceptedRuleQuestion = acceptedRuleQuestion
      state.confirmType = value
      loadingOption.value = false
      if (selectMode.value === 'question') {
        const title = i18n.t('ask a question') + ' ' + provider.value?.name
        const url = provider.value?.avatar?.url || ''
        emit('set-order-item', {
          title,
          image: {
            url
          }
        })
        // router.push('/order/' + props.order._id)
      }
      checkForm('', false)
    })

    onMounted(() => {
      // const isWebview = route.value?.query?.webview === 'true'
      // store.commit('checkout/SET_WEBVIEW', isWebview)
      // slugProvider.value = props.orderItem?.meta?.providerInfo?.slug || ''
      acceptedRuleQuestion(props.orderItem?.meta?.acceptedRuleQuestion)
      // const url = provider.value?.avatar?.url || ''
      // console.log(provider.value?.avatar?.url, ' line 219')
      // emit('set-order-item', {
      //   image: {
      //     url
      //   }
      // })
    })
    const touch = () => {
      // console.log(selectMode.value, ' line 199')
      if (selectMode.value === '') {
        error.value = true
        message.value = i18n.t(
          'this field is required, please check to continue'
        )
        $vuetify.goTo(confirmOption.value, optionsScroll.value)
        return false
      }
      return true
    }
    const submit = () => {
      if (!touch()) return
      if (selectMode.value === 'question') {
        emit('submit', {}, true)
        // router.push('/order/' + props.order._id)
      } else goCheckoutProduct()
    }
    return {
      workFlowData,
      selectMode,
      loadingOption,
      step,
      title,
      message,
      error,
      confirmOption,
      checkForm,
      goCheckoutProduct,
      touch,
      goToPayment: () => true,
      submit
    }
  }
})
</script>
