<template>
  <v-row :key="'question-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
      <h4 class="mt-6">{{ $t('ask-for') }}</h4>
      <v-card class="light-shadow" flat color="grey lighten-4">
        <v-skeleton-loader
          v-if="loading || !patient"
          type="image"
          height="60px"
          width="100%"
        ></v-skeleton-loader>
        <v-list-item v-else>
          <v-list-item-avatar>
            <w-avatar
              :key="patientId"
              :src="patientAvatar"
              :user-id="patient._id"
              :name="patient.name"
              size="50"
            ></w-avatar>
          </v-list-item-avatar>
          <v-list-item-content>
            <v-list-item-title class="text-h6">{{
              patient.name
            }}</v-list-item-title>
            <v-list-item-subtitle
              >{{ patientInfor.formattedAge }},
              {{ patientInfor.gender }}</v-list-item-subtitle
            >
          </v-list-item-content>
        </v-list-item>
      </v-card>
      <h4 class="mt-6">{{ $t('expert-verify') }}</h4>
      <v-card class="light-shadow" flat color="grey lighten-4">
        <v-list-item>
          <v-list-item-avatar>
            <w-avatar
              :key="providerId"
              :src="providerAvatar"
              :user-id="provider._id"
              :name="provider.name"
              size="50"
            ></w-avatar>
          </v-list-item-avatar>
          <v-list-item-content>
            <v-list-item-title class="text-h6">
              {{ provider.Title }} {{ provider.Name }}
            </v-list-item-title>
            <v-list-item-subtitle>
              <div class="d-flex flex-wrap">
                <v-chip
                  v-for="s in providerSpecialty"
                  :key="s"
                  class="mr-2 mt-2"
                  small
                  >{{ s.properties.Name }}</v-chip
                >
              </div>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
      </v-card>
    </v-col>
    <v-col cols="12" class="mb-n2">
      <v-card
        v-if="question"
        class="mt-6 light-shadow"
        flat
        color="grey lighten-4"
      >
        <v-card-title class="font-weight-bold text-h6">
          {{ $t('question') }}
        </v-card-title>
        <v-card-text> {{ question }} </v-card-text>
      </v-card>
      <v-card
        v-if="botAnswer"
        class="mt-6 light-shadow"
        flat
        color="grey lighten-4"
      >
        <v-card-title class="font-weight-bold text-h6">{{
          $t('bot-response')
        }}</v-card-title>
        <v-card-text v-dompurify-html="botAnswer"></v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useContext,
  useRoute
} from '@nuxtjs/composition-api'
// import type { PropType } from 'vue'
import markdownit from 'markdown-it'
import {
  ISearchOption,
  usePatients,
  useProductOrder,
  useRetriveMessage
} from '../../../../repositories'
import { dobFormat } from '../../../../composables'

export default defineComponent({
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { i18n, $dayjs, $toast } = useContext()
    const route = useRoute()
    const { execute } = useRetriveMessage()
    const { authenUser } = useProductOrder()
    const { loading, patients, getRelationships } = usePatients(
      ref<ISearchOption>({
        filter: {
          user: authenUser.value._id,
          relationship: { $ne: 'doctor' }
        }
      })
    )
    // const locale = computed(() => i18n.locale)
    const patient = computed(() => {
      const patient = patients.value.find(
        (p) => p?.related?._id === patientId.value
      )
      if (patient) return patient.related
      return patient
    })
    const patientInfor = computed(() => {
      if (!patient.value || !patient.value?.dob) {
        return ''
      }
      const dob = patient.value.dob
      const age = $dayjs().diff($dayjs(dob), 'years')
      const formattedAge = dobFormat(dob)
      const gender = patient.value.gender

      return {
        age,
        formattedAge,
        gender
      }
    })
    const patientAvatar = computed(() => patient.value?.avatar?.url)
    const provider = computed(() => props.orderItem?.meta?.provider)
    const providerAvatar = computed(() => provider.value?.Avatar?.url)
    const providerId = computed(() => provider.value._id)
    const providerSpecialty = computed(() => provider?.value?.Specialties ?? [])
    const question = ref('')
    const botAnswer = ref('')
    const submit = () => {
      emit('submit', {}, true)
    }
    const patientId = ref('')
    const errorDetail = ref({})
    const getData = async () => {
      try {
        const query = route.value.query.data as string
        const data = JSON.parse(atob(query))
        patientId.value = data.patient
        getRelationships()
        const res = await execute(data.query)
        if (Array.isArray(res)) {
          question.value = res.find((m) => m.isHuman === true).content
          const content = res.find((m) => m.isHuman === false).content
          botAnswer.value = markdownit().render(content)
        }
        emit('set-order-item', {
          title: i18n.t('expert-verify'),
          description: provider.value.Title + ' ' + provider.value.Name
        })
      } catch (error) {
        console.error(error)
        errorDetail.value = error
        $toast.error(error)
      }
    }
    onMounted(() => getData())
    return {
      patient,
      patientInfor,
      patientAvatar,
      provider,
      providerAvatar,
      providerSpecialty,
      providerId,
      question,
      botAnswer,
      loading,
      patientId,
      errorDetail,
      patients,
      goToPayment: () => true,
      touch: () => true,
      submit
    }
  }
})
</script>
<style scoped>
.light-shadow {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px !important;
}
</style>
