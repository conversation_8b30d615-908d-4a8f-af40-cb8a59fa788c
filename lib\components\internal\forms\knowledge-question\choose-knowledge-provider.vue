<template>
  <div>
    <v-row>
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ step }}. {{ $t(title) }}
        </h3>
      </v-col>
    </v-row>

    <v-row class="px-3">
      {{ $t('the choice is based on your medical history of') + username }}
    </v-row>

    <ProviderInfo class="mt-3" :provider="chosenCard.provider" />

    <ProviderList
      open
      title="explore other matches"
      :providers="getProviders(products)"
      :chosen-provider="chosenCard.provider"
      @on-choose-provider="onChooseProvider"
    />

    <!-- <provider-list
      v-if="consultations.length > 0"
      title="choose your physician"
      :providers="physicians"
      :provider-chosen="chosenProvider"
      @on-choose-provider="onChooseProvider"
    /> -->

    <w-loading
      :value="loading"
      back-polyine-color="#b1ffff"
      front-polyine-color="#009688"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useStore,
  useRouter,
  watch,
  onMounted,
  useContext,
  useRoute,
  reactive
} from '@nuxtjs/composition-api'
import { useElasticSearch } from '@wellcare/nuxt-module-notion/repositories/use-elastic-search'
import { usePaymentOrder, useProductOrder } from '../../../../repositories'
import ProviderInfo from '../choose-provider/provider-info.vue'
import ProviderList from '../choose-provider/provider-list.vue'

interface ICard {
  provider?: any
  productId?: string
  productSlug?: string
}

export default defineComponent({
  name: 'ChooseKnowledgeProvider',
  components: { ProviderInfo, ProviderList },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const route = useRoute()
    const router = useRouter()
    const { $toast }: any = useContext()
    const { state, commit }: any = useStore()

    const {
      createSingleOrder,
      onCreateSingleOrderSuccess,
      executeUpdateOrderItem
    } = useProductOrder()
    const { executeGetOrderItem, onGetOrderItemSuccess } = usePaymentOrder(
      computed(() => ({ _id: props.order?._id })),
      computed(() => ({ polling: computed(() => 0) })),
      computed(() => 'pending')
    )

    const user = computed(() => state?.authen?.user)
    const username = computed(() => user.value?.name)

    const step = computed(() => props.currentStep?.step)
    const title = computed(() => props.currentStep?.content?.title)

    const chosenCard = reactive<ICard>({
      provider: undefined,
      productId: '',
      productSlug: ''
    })
    const loading = ref<boolean>(true)

    const b64DecodeUnicode = (str: string) => {
      // Going backwards: from bytestream, to percent-encoding, to original string.
      return decodeURIComponent(
        atob(str)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
          })
          .join('')
      )
    }

    const setChosenValue = (
      provider: any,
      productId: string,
      productSlug: string
    ) => {
      chosenCard.provider = provider
      chosenCard.productId = productId
      chosenCard.productSlug = productSlug
    }

    // Search explore provider
    // Database index
    const indexExplore = 'catalog_product'
    // Define query
    const searchExplore = computed(() => {
      return {
        size: 1000,
        _source: {
          include: [
            'id',
            '_id',
            'output.slug',
            'output.provider._id',
            'output.provider.slug',
            'output.provider.name',
            'output.provider.title',
            'output.provider.specialty',
            'output.provider.highlight',
            'output.provider.avatar.url'
          ]
        },
        query: {
          bool: {
            filter: [
              {
                term: {
                  'output.sku.keyword':
                    'consultation-knowledge-question-provider'
                }
              },
              {
                term: {
                  'output.state.keyword': 'published'
                }
              },
              {
                match_phrase: {
                  'output.sellable': true
                }
              },
              {
                exists: {
                  field: 'output.provider.slug.keyword'
                }
              }
            ]
          }
        }
      }
    })
    // Call repository
    const { execute: executeSearchProduct, hits: products } = useElasticSearch(
      indexExplore,
      searchExplore
    )
    // Execute search
    executeSearchProduct().then((response: any) => {
      const product = response?.body?.hits?.hits[0]
      setChosenValue(
        product?.output?.provider,
        product?._id,
        product?.output?.slug
      )

      const fromEduHubSlug = route.value.query?.fromEduHub
        ? JSON.parse(b64DecodeUnicode(route.value.query?.fromEduHub as string))
            ?.providerSlug
        : null

      let providerSlug: string = ''

      if (props.orderItem.meta?.isExpertReview) {
        providerSlug = route.value.query?.providerSlug || fromEduHubSlug
      } else {
        providerSlug = fromEduHubSlug
      }

      if (providerSlug) {
        const chosenProduct = response?.body?.hits?.hits.find(
          (hit: any) => hit?.output?.provider?.slug === providerSlug
        )

        setChosenValue(
          chosenProduct?.output?.provider,
          chosenProduct?._id,
          chosenProduct?.output?.slug
        )
      }

      if (props.orderItem.meta?.isExpertReview) {
        submit()
      }
    })

    // Search physician
    // Database index
    const indexPhysician = 'consultation'
    // Define query
    const searchPhysician = computed(() => {
      return {
        size: 1000,
        query: {
          bool: {
            filter: [
              {
                term: {
                  'user._id.keyword': `${user.value?._id}`
                }
              },
              {
                term: {
                  'patient._id.keyword': `${user.value?._id}`
                }
              },
              {
                term: {
                  'state.keyword': 'COMPLETED'
                }
              },
              {
                term: {
                  'state.keyword': {
                    value: 'FREE'
                  }
                }
              }
            ]
          }
        }
      }
    })
    // Call repository
    const { execute: executeSearchConsultation, hits: consultations } =
      useElasticSearch(indexPhysician, searchPhysician)
    // Execute search
    executeSearchConsultation()

    const getProviders = (items: any) => {
      const providers = []

      items.forEach((item: any) => {
        if (item?.provider) {
          providers.push(item.provider)
        }

        if (item?.output?.provider) {
          providers.push(item.output.provider)
        }
      })

      return providers
    }

    const onChooseProvider = (provider: any) => {
      const card = products.value.find((product: any) => {
        return product?.output?.provider?._id === provider?._id
      })

      setChosenValue(card?.output?.provider, card?._id, card?.output?.slug)
      submit()
    }

    const submit = () => {
      createSingleOrder({
        product: {
          slug: chosenCard.productSlug
        }
      })

      onCreateSingleOrderSuccess((response: any) => {
        if (response.code === 200) {
          const orderAdded = response.results.orderItems[0]
          if (orderAdded) {
            commit('checkout/updateField', {
              path: 'orderItems',
              value: [props.orderItem, orderAdded]
            })
          } else {
            $toast.error('cannot prepare order item')
          }
        } else {
          $toast.error(response.message)
        }
      })

      emit('submit', {
        product: chosenCard.productId,
        provider: chosenCard.provider?._id
      })
    }

    const touch = () => true

    const toggleOrderItemState = (orderItem: any) => {
      orderItem.state = orderItem.state === 'pending' ? 'cancelled' : 'pending'
      executeUpdateOrderItem(orderItem)
    }

    const handleGetOrderItemSuccess = (
      response: any,
      slugCheck: (slug: string) => boolean
    ) => {
      response.results.forEach((orderItem: any) => {
        if (
          orderItem &&
          orderItem.product &&
          slugCheck(orderItem.product.slug)
        ) {
          toggleOrderItemState(orderItem)
        }
      })
    }

    watch(
      () => props.orderItem,
      () => {
        if (!props.orderItem.meta?.isExpertReview) {
          executeGetOrderItem()
          onGetOrderItemSuccess((response: any) => {
            handleGetOrderItemSuccess(response, (slug) =>
              products.value.some(
                (product: any) => product.output.slug === slug
              )
            )
          })
          router.push('/payment/order/' + props.order._id)
        } else if (route.value.query?.providerSlug) {
          executeGetOrderItem()
          onGetOrderItemSuccess((response: any) => {
            handleGetOrderItemSuccess(
              response,
              (slug) => slug === route.value.query?.providerSlug
            )
          })
          router.push('/payment/order/' + props.order._id)
        } else {
          loading.value = false
        }
      },
      {
        deep: true
      }
    )

    onMounted(() => (loading.value = true))

    return {
      step,
      touch,
      title,
      submit,
      loading,
      username,
      products,
      chosenCard,
      getProviders,
      consultations,
      onChooseProvider,
      goToPayment: () => true
    }
  }
})
</script>
