<template>
  <v-row :key="'choose-option-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ step }}. {{ $t(title) }}
      </h3>
    </v-col>

    <v-row class="px-3 font-weight-bold mt-1" style="font-size: 17px">
      <v-col cols="9">{{ $t('Standard') }}</v-col>
      <v-col cols="3" class="text-right">
        {{
          $n(50000, {
            style: 'currency',
            currency: 'VND'
          })
        }}
      </v-col>
    </v-row>

    <div class="pt-2 px-3" style="width: 100%">
      {{ $t('response from Wellcare, with reference') }}
    </div>

    <div class="pt-2 px-3 mt-2" style="width: 100%; font-size: 17px">
      <p class="mb-2 font-weight-bold">{{ $t('addon') }}</p>
      <div class="d-flex align-center justify-space-between">
        <v-checkbox v-model="checkbox" hide-details>
          <template #label>
            <div>
              {{ $t('physicians comment') }}
            </div>
          </template>
        </v-checkbox>
        <span class="font-weight-bold">
          +
          {{
            $n(50000, {
              style: 'currency',
              currency: 'VND'
            })
          }}</span
        >
      </div>
    </div>
  </v-row>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useRoute,
  watch
} from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'ChooseOption',
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const route = useRoute()
    const step = computed(() => props.currentStep?.step)
    const title = computed(() => props.currentStep?.content?.title)

    const checkbox = ref<boolean>(false)

    const submit = () => {
      emit('set-order-item', {
        image: {
          url: '/icons/wellcare.png'
        }
      })
      emit('submit', {
        isExpertReview: checkbox.value,
        product: undefined,
        provider: undefined
      })
    }

    watch(
      () => checkbox.value,
      () => {
        emit('submit', {
          isExpertReview: checkbox.value,
          product: undefined,
          provider: undefined
        })
      }
    )

    const touch = () => true

    onMounted(() => {
      if (
        props.orderItem.meta?.isExpertReview ||
        route.value.query?.fromEduHub ||
        route.value.query?.providerSlug
      )
        checkbox.value = true
    })

    return {
      step,
      title,
      checkbox,
      submit,
      touch
    }
  }
})
</script>

<style scoped>
.v-input--selection-controls {
  margin: 0 !important;
  padding: 0 !important;
}
</style>
