<template>
  <v-container>
    <v-row :key="'knowledge-question-component'">
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ step }}. {{ $t(title) }}
        </h3>
      </v-col>

      <v-col cols="12" class="pt-2">
        <v-form ref="form" v-model="isValid" class="form">
          <v-textarea
            v-model="chiefComplaint"
            :rules="[rules.required, rules.length(25)]"
            outlined
            auto-grow
            validate-on-blur
            counter="250"
            maxlength="250"
            spellcheck="false"
            class="rounded-lg"
            :placeholder="isFocus ? '' : INSTRUCTION"
            :hint="INSTRUCTION"
            @focus="isFocus = true"
            @blur="isFocus = false"
          >
            <template #message="{ message }">
              {{ $t(message) }}
            </template>
          </v-textarea>
        </v-form>
      </v-col>
    </v-row>

    <v-row v-if="videoTitle && videoLink">
      <v-col cols="12">
        <h4>
          {{ $t('ask about the article') + ':' }}
          <a :href="videoLink">{{ videoTitle }}</a>
        </h4>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useContext,
  useRoute,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import DOMPurify from 'dompurify'

export default defineComponent({
  name: 'KnowledgeQuestion',
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const route = useRoute()
    const { i18n }: any = useContext()
    const { state }: any = useStore()
    const user = computed(() => state?.authen?.user)

    const step = computed(() => props.currentStep?.step)
    const title = computed(() => props.currentStep?.content?.title)

    const INSTRUCTION = i18n.t(
      'unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
    )
    const videoTitle = ref<string>('')
    const videoLink = ref<string>('')
    const form = ref<any>(null)
    const isValid = ref<boolean>(false)

    const chiefComplaint = ref<string>('')
    const rules = {
      required: (value: any) =>
        !!value || 'this field is required, please enter to continue',
      length: (length: any) => (value: any) =>
        value?.length >= length || 'minimum 25 characters'
    }
    const isFocus = ref<boolean>(false)
    const chiefComplaintLength = computed(() => chiefComplaint.value.length)

    const submit = () => {
      const orderItemMeta = props.orderItem?.meta
      const data: any = {
        chiefComplaint: DOMPurify.sanitize(chiefComplaint.value)
      }

      if (route.value.query?.fromEduHub) {
        data.isExpertReview = true
      }

      if (!orderItemMeta?.patient || !orderItemMeta?.patientInfo) {
        data.patient = user.value?._id
        data.patientInfo = user.value
      }

      emit('submit', data)
    }

    const touch = () => {
      form.value.validate()

      if (chiefComplaintLength.value < 25) {
        isValid.value = false
        return false
      }
      return true
    }

    watch(
      () => props.orderItem,
      () => {
        if (props.orderItem?.meta?.chiefComplaint) {
          chiefComplaint.value = props.orderItem.meta.chiefComplaint.slice(
            0,
            250
          )
        }

        if (
          !props.orderItem?.meta?.providerInfo &&
          props.orderItem?.meta?.provider
        ) {
          setTimeout(() => {
            emit('submit', {
              providerInfo: props.orderItem.meta.provider
            })
          }, 850)
        }
      },
      {
        deep: true
      }
    )

    const b64DecodeUnicode = (str: string) => {
      // Going backwards: from bytestream, to percent-encoding, to original string.
      return decodeURIComponent(
        atob(str)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
          })
          .join('')
      )
    }

    onMounted(() => {
      if (route.value.query?.fromEduHub) {
        videoTitle.value = JSON.parse(
          b64DecodeUnicode(route.value.query?.fromEduHub as string)
        )?.videoTitle

        videoLink.value = JSON.parse(
          b64DecodeUnicode(route.value.query?.fromEduHub as string)
        )?.videoLink
      }
    })

    return {
      step,
      form,
      title,
      rules,
      isFocus,
      isValid,
      INSTRUCTION,
      chiefComplaint,
      videoTitle,
      videoLink,
      touch,
      submit
    }
  }
})
</script>

<style scoped>
div >>> .v-input__slot {
  align-items: flex-start !important;
}

div >>> .v-label {
  text-align: justify !important;
}

.form >>> .v-text-field__slot {
  font-size: 15px;
  padding: 10px;
}

.form >>> .v-text-field__details {
  display: flex;
  flex-direction: column-reverse;
}

.form >>> .v-messages__message {
  line-height: 25px;
  font-size: 14px !important;
  margin-top: 4px;
}

.form >>> .v-counter {
  position: absolute;
  top: -24px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  max-width: 80px;
  color: #009688;
  border-radius: 5px;
  transform: translateY(8px);
  background-color: #f8f8f9;
}
</style>
