<template>
  <v-card elevation="0" color="transparent">
    <v-form
      :key="key"
      ref="formRef"
      v-model="valid"
      class="d-flex align-center flex-wrap"
      @submit="submit"
    >
      <div class="mr-2">
        <label>{{ $t('height') }}</label>
        <w-number-input
          suffix="cm"
          :rules="rules"
          :range="[0, 250]"
          :default-val="Number(height)"
          :increment="0.5"
          @change="(v) => onChangeNumber({ value: v, type: 'height' })"
        />
      </div>
      <div>
        <label>{{ $t('weight') }}</label>
        <w-number-input
          suffix="kg"
          :rules="rules"
          :range="[0, 250]"
          :default-val="Number(weight)"
          :increment="0.5"
          @change="(v) => onChangeNumber({ value: v, type: 'weight' })"
        />
      </div>
    </v-form>
    <v-slide-x-transition>
      <span v-if="!isExpDate" class="caption red--text">
        *{{ $t('please update the index') }}
      </span>
    </v-slide-x-transition>
    <w-loading v-model="loading" absolute :size-loading="2" />
  </v-card>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onBeforeMount,
  reactive,
  ref,
  toRefs,
  useContext,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useObservation } from '../../../../repositories'
import { calcConvertUnitValue, CONVERT } from '../../../../utils'

export interface IBodyIndex {
  height: string
  weight: string
}

export default defineComponent({
  name: 'BodyIndex',
  props: {
    label: {
      type: String,
      default: ''
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const { $vuetify, i18n, $dayjs } = useContext()
    $dayjs.extend(relativeTime)

    const { commit } = useStore()
    const { resObs, executeSearch, executeImportCreate, loading } =
      useObservation()

    const key = ref<number>(0)
    const formRef = ref()
    const valid = ref<boolean>(false)
    const name = ref<string>(props.label)
    const year = ref<number>(1)
    const isExpDate = ref<boolean>(true)
    const notChanged = ref<boolean>(true)

    const index = reactive<IBodyIndex>({
      height: '',
      weight: ''
    })

    const dense = computed(() => $vuetify.breakpoint.mobile)
    const rules = computed(() => [(v: number) => !!v || i18n.t('required')])
    const patientId = computed<string>(
      () =>
        props.orderItem?.meta?.patientInfo?._id ||
        props.orderItem?.meta?.baby_info?._id ||
        ''
    )

    watch(index, () => {
      notChanged.value = false
    })

    const submit = async () => {
      formRef.value.validate()

      if (isExpDate.value && notChanged.value) {
        return true
      } else if (!valid.value) {
        return false
      } else if (index.height && index.weight) {
        try {
          const data = Object.keys(index).map((item) => ({
            key: item,
            name: CONVERT[item].name,
            observedAt: $dayjs().toISOString(),
            unit: CONVERT[item].unit,
            user: patientId.value,
            value: index[item]
          }))
          const res = await executeImportCreate(data)

          if (res.code === 200) {
            commit(
              'checkout/SET_SUMMARIZE_BODY_INDEX',
              `${index.height}cm, ${index.weight}kg, ${$dayjs(
                res.results[0]?.updatedAt
              ).fromNow()}`
            )
            isExpDate.value = true
            notChanged.value = true
            return valid.value
          } else {
            return false
          }
        } catch (error) {
          console.error(error)
          return false
        }
      } else {
        return false
      }
    }

    const onChangeNumber = (val: { value: string; type: string }) => {
      index[val.type] = val.value
    }

    const refresh = () => {
      key.value += 1
    }

    onBeforeMount(async () => {
      try {
        await executeSearch({
          filter: JSON.stringify({
            user: patientId.value,
            name: ['Person Height', 'Person Weight']
          }),
          limit: 1,
          sort: '-observedAt'
        })
        const resultsSearch = resObs.value?.results

        if (resultsSearch?.length) {
          const updatedAt = resultsSearch[0]?.updatedAt || $dayjs()

          // Kiểm tra nếu targetDate trước currentDate và trong vòng 1 năm
          const currDate = $dayjs()
          const targetDate = $dayjs(updatedAt)

          isExpDate.value =
            targetDate.isBefore(currDate) &&
            targetDate.isAfter(currDate.subtract(year.value, 'year'))

          for (const i of resultsSearch) {
            index[i?.key] = calcConvertUnitValue({
              unit: i?.unit,
              value: i?.value
            })
          }

          refresh()

          commit(
            'checkout/SET_SUMMARIZE_BODY_INDEX',
            `${index.height}cm, ${index.weight}kg, ${$dayjs(
              updatedAt
            ).fromNow()}`
          )

          setTimeout(() => {
            notChanged.value = true
          }, 100)

          if (!isExpDate.value) {
            index.height = ''
            index.weight = ''
          }
        } else {
          commit('checkout/SET_SUMMARIZE_BODY_INDEX', '')
          notChanged.value = false
        }
      } catch (error) {
        console.error(error)
      }
    })

    return {
      dense,
      valid,
      name,
      formRef,
      rules,
      key,
      submit,
      onChangeNumber,
      isExpDate,
      year,
      loading,
      ...toRefs(index)
    }
  }
})
</script>
