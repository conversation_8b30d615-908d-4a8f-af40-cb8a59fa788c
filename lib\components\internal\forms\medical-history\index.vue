<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <div class="d-flex justify-space-between align-center">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
        </h3>
        <avatar-change-patient :key="key" :patient="patient" />
      </div>
    </v-col>
    <v-col>
      <stepper-validated
        ref="stepperValidatedRef"
        :key="key"
        :items="items"
        :order="order"
        :order-item="orderItem"
      />
    </v-col>
  </v-row>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  useStore
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import AvatarChangePatient from '../../shared/avatar-change-patient.vue'
import StepperValidated from '../../shared/stepper-validated.vue'
import { IStepperItems, IUser } from '../../../../models'
import { IStep } from '../../../../repositories'
import { useRerender } from '../../../../composables'

export default defineComponent({
  name: 'MedicalHistory',
  components: { StepperValidated, AvatarChangePatient },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      default: () => {}
    }
  },
  setup(props, { emit }) {
    const { commit } = useStore()
    const { key, rerenderSrc } = useRerender()

    const stepperValidatedRef = ref()

    const items: IStepperItems[] = [
      {
        key: 'medicalRecord',
        label: 'medical record',
        component: 'prepare-medical-record',
        required: true,
        isFirstOpen: true
      },
      {
        key: 'bodyIndex',
        label: 'body index',
        component: 'body-index',
        required: true
        // isSkip: true
      },
      {
        key: 'vaccination',
        label: 'vaccinations',
        component: 'vaccination',
        info: 'Vaccines prevent millions of deaths worldwide every year and are the best way to not only protect ourselves and our children against certain preventable diseases themselves but also against the dangerous complications or consequences that they can bring.'
      }
    ]

    const patient = computed<IUser>(() => props.orderItem?.meta?.patientInfo)

    const next = () => {
      return stepperValidatedRef.value?.next()
    }

    const back = () => {
      stepperValidatedRef.value?.back()
    }

    const touch = () => {
      return next()
    }

    const submit = () => {
      emit('submit', {})
    }

    rerenderSrc({
      source: () => patient.value?._id,
      onChanged: (newVal, oldVal) => {
        if (newVal !== oldVal) {
          commit('checkout/SET_SUMMARIZE_MEDICAL_RECORD', '')
          commit('checkout/SET_SUMMARIZE_BODY_INDEX', '')
          commit('checkout/SET_SUMMARIZE_VACCINATION', '')
          commit('checkout/SET_DATA_VACCINATION', null)
        }
      }
    })

    onMounted(() => {
      commit('checkout/SET_BUTTON_NEXT_STATE_DISABLE', false)
    })

    return {
      items,
      stepperValidatedRef,
      next,
      back,
      touch,
      submit,
      patient,
      key
    }
  }
})
</script>
