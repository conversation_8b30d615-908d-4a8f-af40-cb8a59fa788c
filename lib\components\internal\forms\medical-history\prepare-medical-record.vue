<template>
  <v-row>
    <!-- <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col> -->
    <v-col cols="12" class="pb-0">
      <v-form ref="medicalRecord" v-model="isValidForm" lazy-validation>
        <v-col v-if="showInputFields('reason')" cols="12" class="px-0">
          <h4
            v-show="showLabel"
            class="body-1 font-weight-medium text--secondary"
          >
            {{ $t('chief complaint') }}
          </h4>
          <v-text-field
            v-model="reason"
            :placeholder="showLabel ? '' : $t('chief complaint')"
            :rules="rules.reason"
            name="reason"
            class="rounded-lg"
            outlined
            :readonly="isMentalHealth"
            :hint="isMentalHealth ? '' : $t('the main reason from 7-10 words')"
            :loading="loading"
            :disabled="loading"
            :maxlength="isMentalHealth ? null : 50"
            :counter="isMentalHealth ? null : 50"
            @click="
              () => {
                if (isMentalHealth) chiefComplaintDialog = true
              }
            "
          ></v-text-field>
        </v-col>
        <form-expectation
          :name="expectationFor"
          @update-expectations="expectations = $event"
        />
        <v-col
          v-if="showInputFields('chiefComplaint')"
          cols="12"
          class="px-0 mt-n3"
        >
          <h4
            v-show="showLabel"
            class="body-1 font-weight-medium text--secondary"
          >
            {{ $t('detailed description') }}
          </h4>
          <v-textarea
            v-model="chiefComplaint"
            :placeholder="
              showLabel
                ? $t('Detailed description placeholder').replace(/\\n/g, '\n')
                : $t('detailed description')
            "
            :hint="$t('Detailed description placeholder').replace(/\\n/g, '\n')"
            :rules="rules.chiefComplaint"
            :loading="loading"
            :disabled="loading"
            class="rounded-lg"
            name="chiefComplaint"
            rows="7"
            no-resize
            outlined
          ></v-textarea>
        </v-col>
        <v-col v-if="showInputFields('questions')" cols="12" class="px-0 mt-n3">
          <h4
            v-show="showLabel"
            class="body-1 font-weight-medium text--secondary"
          >
            {{ $t('questions') }}
          </h4>
          <template v-for="(question, index) in questions">
            <v-text-field
              :key="index"
              v-model="questions[index]"
              outlined
              :placeholder="
                $t('questions') +
                ' ' +
                $t('number').toString().toLowerCase() +
                ' ' +
                (index + 1).toString()
              "
              :name="`question-${index}`"
              :hint="
                $t(
                  'make a list of concerns but keep it short and focused. Each one is a question.'
                )
              "
              :rules="index === 0 ? rules.questions : []"
              counter="100"
              maxlength="100"
              :append-icon="index === 0 ? '' : '$close-circle'"
              :loading="loading"
              :disabled="loading"
              class="mb-5 rounded-lg"
              @click:append="removeQuestion(index)"
            ></v-text-field>
          </template>
          <v-expand-transition>
            <v-alert
              v-show="questions.length >= 20"
              class="mt-3"
              outlined
              text
              color="warning"
            >
              <div>
                {{ $t('maximum is {maximum} questions', { maximum: 20 }) }}
              </div>
            </v-alert>
          </v-expand-transition>
        </v-col>
      </v-form>
    </v-col>
    <v-col cols="12">
      <h4 class="body-1 font-weight-medium text--secondary">
        {{ $t('attachments') }}
        <span>({{ fileTotal > 0 ? fileTotal : $t('no data') }})</span>
      </h4>
      <v-alert
        v-if="!canShowUpload && isIOS"
        text
        color="warning"
        class="my-2"
        outlined
      >
        <div
          v-dompurify-html="
            $t('please add related pictures or videos after successful booking')
          "
        ></div>
      </v-alert>
      <!-- waiting to render after data sync -->
      <file-component
        v-if="!loading"
        :order="order"
        :order-item="orderItem"
        :search-files-option="searchFilesOption"
        :upload-option="uploadOption"
        :user-id="patient._id"
        class="mt-5 pt-2"
        @onTotal="fileTotal = $event"
      />
    </v-col>
    <w-dialog
      :title="$t('chief complaint')"
      :value="chiefComplaintDialog"
      :mobile-attr="{
        fullscreen: false,
        eager: true
      }"
      :desktop-attr="{
        width: '600px',
        scrollable: true,
        eager: true
      }"
      @onClose="chiefComplaintDialog = false"
    >
      <form-chiefcomplaint
        :reason="reason"
        @update-cheifcomplaint="updateChiefcomplaint"
        @type-cheifcomplaint="reason = $event"
      />
    </w-dialog>
    <v-dialog
      v-model="shouldShowExistedComponent"
      max-width="350"
      transition="fade-transition"
      persistent
    >
      <w-existed-consultation
        :patient="patient"
        :provider="existedProvider"
        :consultation="existedConsultation"
        @on-close="$router.back()"
      />
    </v-dialog>
  </v-row>
</template>

<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  useContext,
  watch,
  useRoute,
  useStore
} from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { watchDebounced } from '@vueuse/core'
import DOMPurify from 'dompurify'
import {
  ISearchOption,
  UseDob,
  useProductOrder,
  searchConsultation
} from '../../../../repositories'
import FileComponent from '../../shared/file/index.vue'
import FormChiefcomplaint from '../../widgets/picker/form-chiefcomplaint.vue'
import FormExpectation from '../../widgets/picker/form-expectation.vue'

function textToHtml(s: string) {
  if (s.trim() === '') return
  const newStr = s
    .split('\n')
    .map((item) => `<p>${item}</p>`)
    .join('')
  return `<div>${newStr}</div>`
}
function htmlToText(s: string) {
  const divEl = document.createElement('div')
  divEl.innerHTML = s
  const pEls = divEl.querySelectorAll('p')
  if (pEls.length === 0) return divEl.textContent ?? ''
  return Array.from(pEls)
    .map((el) => el.textContent)
    .join('\n')
}

const DEBOUNCE_TIME = 1000

export default defineComponent({
  components: {
    FileComponent,
    FormChiefcomplaint,
    FormExpectation
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const name = ref<string>(props.label)
    // ========================= DECLARE VARIABLE BLOCK ======================================
    const { state, commit } = useStore()
    const { setOrderItemMeta } = useProductOrder()
    const { i18n, $config, app, $vuetify } = useContext()
    const route = useRoute()
    const isIOS = computed(() => {
      const osLists = [
        'iPad Simulator',
        'iPhone Simulator',
        'iPod Simulator',
        'iPad',
        'iPhone',
        'iPod'
      ]
      return osLists.includes(app?.$ua?._parsed?.os)
    })

    // store data
    const { authenUser } = useProductOrder()

    const loading: Ref<boolean> = ref(true)

    // UI config
    const UIcustom: ComputedRef<any> = computed(
      () => $config.checkout?.UIcustomize?.prepareMedicalRecord
    )
    const canShowUpload: ComputedRef<boolean> = computed(
      () => UIcustom.value?.canUploadFile ?? true
    )
    const showLabel: ComputedRef<boolean> = computed(
      () => UIcustom.value?.showLabel ?? false
    )
    const summarizeMedicalRecord = computed(
      () =>
        (state as any)?.checkout?.stepperValidDated?.medicalRecord?.summarize
    )

    // form data
    const medicalRecord: Ref<any> = ref(null)
    const isValidForm: Ref<boolean> = ref(false)
    const reason: Ref<string> = ref('')
    const chiefComplaint: Ref<string> = ref('')
    // for chiefcomplain option
    const chiefComplaintDialog: Ref<boolean> = ref(false)
    const expectationFor: Ref<string> = ref('')
    const expectations: Ref<any> = ref([])
    const questions: string[] = reactive([''])
    const rules: any = {
      reason: [() => reason.value.trim() !== '' || i18n.t('Value is required')],
      chiefComplaint: [
        () => chiefComplaint.value.trim() !== '' || i18n.t('Value is required')
      ],
      questions: [
        () => questions[0].trim() !== '' || i18n.t('Value is required'),
        () => questions.length <= 20 || i18n.t('maximum is 20 questions')
      ]
    }

    // set default infor is user infor, because choose-patient step may be removed (in some case)
    const patient: ComputedRef<any> = computed(() => {
      const pInfor = props.orderItem.meta?.patientInfo
      if (!pInfor) return authenUser.value
      return pInfor
    })

    // dialog existed consultation
    const shouldShowExistedComponent = ref(false)
    const existedConsultation = ref(null)
    const existedProvider = ref(null)

    const isScrollable = computed<boolean>(() => {
      const html: any = document.getElementsByTagName('html')[0]
      return html.scrollHeight > html.clientHeight
    })
    const optionsScroll = computed(() =>
      isScrollable.value
        ? { offset: 100 }
        : { container: document.body, offset: 100 }
    )

    // data to emit
    const stateData: any = reactive({
      reason: '',
      chiefComplaint: '',
      questions: []
    })

    // files
    const searchFilesOption = ref<ISearchOption>({
      fields:
        'tags, description, url, labels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, capturedAt, duration',
      filter: {
        labels: 'order:' + props.order._id,
        user: patient.value._id
      }
    })
    const uploadOption = ref({
      project: 'emr',
      file: null,
      labels: 'order:' + props.order._id,
      user: patient.value._id
    })
    const fileTotal: Ref<number> = ref(0)

    const patientInfo: any = reactive({
      name: '',
      dob: '',
      id: '',
      avatar: ''
    })

    const isMentalHealth = ref<boolean>(false)
    // ========================================== END OF VARIABLE BLOCK ===============================================

    // ========================================== REPOSITORIES BLOCK ==================================================
    // search existed consultation
    const paramsSearch: Ref<any> = ref({
      // query to check if CURRENT PATIENT have any WAITING consultation with CURRENT PROVIDER or not
      filter: {
        patient: patient.value?._id,
        state: {
          $in: ['WAITING']
        },
        type: 'indepth',
        provider: props.orderItem.meta?.provider
      },
      populate: JSON.stringify([
        {
          path: 'provider'
        },
        {
          path: 'patient'
        }
      ]),
      sort: '-time'
    })
    const {
      execute: executeSearchConsultations,
      onSucess: onSearchConsultationsSuccess
    } = searchConsultation(paramsSearch)
    onSearchConsultationsSuccess((data) => {
      const results: any[] = data.results
      const product = props.orderItem?.product ?? ''
      if (results.length > 0 && !product.slug.includes('dat-cau-hoi')) {
        existedProvider.value = results[0]?.provider
        existedConsultation.value = results[0]
        shouldShowExistedComponent.value = true
      }
    })
    // format dob
    const dobOption: Ref<string> = ref('')
    const { response: patientDob, formatDob } = UseDob(dobOption)
    // ======================================== END OF REPOSITORIES BLOCK ==========================================================

    // ======================================== LOGIC BLOCK ========================================================================
    const showInputFields = (_field) => {
      return true
    }
    const removeQuestion = (index) => {
      if (index > 0) questions.splice(index, 1)
    }

    const saveStateData = () => {
      stateData.reason = DOMPurify.sanitize(reason.value)
      const htmlChiefComplaint = textToHtml(chiefComplaint.value)
      stateData.chiefComplaint = DOMPurify.sanitize(htmlChiefComplaint)
      // DOMPurify.sanitize(chiefComplaint.value)
      stateData.questions = questions
        .map((question) => DOMPurify.sanitize(question))
        .filter((question) => question.length > 0)
    }

    // validate data in this step
    const valid = () => {
      medicalRecord.value.validate()
      const inputs = medicalRecord.value.inputs
      if (inputs.length > 0) {
        for (const input of inputs) {
          if (!input?.valid) {
            $vuetify.goTo(input, optionsScroll.value)
            return input?.focus()
          }
        }
      }
      if (!reason.value || !chiefComplaint.value || !questions[0]) return false

      return isValidForm
    }

    // Validate data and emit it to parent component
    /**
     * Validate data in this step and emit it to parent component
     * @return {Boolean} - Whether data was valid or not
     */
    const submit = async () => {
      // Check if data is valid
      const isValidated = valid()
      if (!isValidated) return false

      // Check if it's the last step of the form
      if (summarizeMedicalRecord.value) return true

      // Get order item meta
      const orderItemMeta = props.orderItem?.meta

      // Check data and update patient information if needed
      if (!orderItemMeta.patient || !orderItemMeta?.patientInfo) {
        stateData.patient = patient.value._id
        stateData.patientInfo = patient.value
      }

      // Add expectations and expectationFor to the data
      if (expectations.value.length > 0) {
        stateData.expectations = expectations.value
      }
      if (expectationFor.value) {
        stateData.expectationFor = expectationFor.value
      }

      // Format text chief-complaint to html
      stateData.chiefComplaint = textToHtml(stateData.chiefComplaint)

      // Commit data to the store and save it
      commit('checkout/SET_SUMMARIZE_MEDICAL_RECORD', stateData?.reason)
      await setOrderItemMeta(stateData)

      // Return true if data was valid
      return true
    }
    const updateChiefcomplaint = (data) => {
      reason.value = data.text
      chiefComplaintDialog.value = !data.isFinished
      expectationFor.value = data.name
    }
    // get patient information
    const getAvatarUrl = (patient) => patient?.avatar?.url || ''
    const getPatientName = (patient) => patient?.name || ''
    const getPatientGender = (patient) => {
      const gender = patient?.gender.toUpperCase()
      switch (gender) {
        case 'M':
          return 'male'
        case 'F':
          return 'female'
        case 'U':
          return 'other'
        default:
          return 'unknow'
      }
    }
    const updatePatientInfor = (patient) => {
      dobOption.value = patient?.dob
      formatDob()
      patientInfo.name = getPatientName(patient)
      patientInfo.avatar = getAvatarUrl(patient)
      patientInfo.gender = getPatientGender(patient)
      patientInfo.dob = patientDob.value
      patientInfo.id = patient?._id
    }
    // =========================================== END OF LOGIC BLOCK ===================================================

    // =========================================== WATCHER BLOCK ========================================================
    watch(patient, () => {
      updatePatientInfor(patient.value)
    })
    watch(chiefComplaint, (value) => {
      saveStateData()
      emit('emit-data-record', { chiefComplaint: textToHtml(value) })
    })
    watch(reason, (value) => {
      saveStateData()
      commit('checkout/SET_SUMMARIZE_MEDICAL_RECORD', value)
      emit('emit-data-record', { reason: value })
    })
    watch(questions, (value) => {
      // limit the number of question is 20
      if (questions.length >= 20) return
      // auto add an empty question after existed question
      if (questions[questions.length - 1].length > 0)
        questions[questions.length] = ''
      saveStateData()
      emit('emit-data-record', { questions: value })
    })

    // WatchDebounced
    watchDebounced(
      [
        () => stateData.chiefComplaint,
        () => stateData.reason,
        () => stateData.questions
      ],
      () => {
        setOrderItemMeta(stateData)
      },
      {
        debounce: DEBOUNCE_TIME,
        deep: true
      }
    )

    // ============================================== END OF WATCHER BLOCK =====================================

    // ============================================== FIRST LOAD ===============================================
    onMounted(() => {
      loading.value = true
      // we settimeout here because the data in store may be not sync done
      setTimeout(() => {
        const orderItemMeta = props.orderItem.meta
        const checkoutFlow = route.value.query?.flow
        // convert html string to string
        if (orderItemMeta?.chiefComplaint) {
          stateData.chiefComplaint = htmlToText(orderItemMeta.chiefComplaint)
          chiefComplaint.value = htmlToText(orderItemMeta.chiefComplaint)
        }

        let formData: any = {}
        if (localStorage)
          formData = JSON.parse(localStorage?.getItem('formData') ?? '{}')
        // if formData in localstorage is not empty => user checkout product mental health
        if (
          Object.keys(formData).length > 0 &&
          checkoutFlow === 'consultation-therapy'
        ) {
          isMentalHealth.value = true
        }
        /**
         * If the orderItem has metadata, use it.
         * Otherwise, if the checkout product pertains to mental health and the formData localStorage is not empty,
         * use that instead.
         */
        // logic read reason
        let reasonData = ''
        if (orderItemMeta?.reason) reasonData = orderItemMeta.reason
        else if (formData?.reason && isMentalHealth.value)
          reasonData = formData.reason
        stateData.reason = reasonData
        reason.value = reasonData
        // logic read question
        if (orderItemMeta?.questions?.length > 0) {
          stateData.questions = orderItemMeta.questions
          questions.push(...orderItemMeta.questions)
          questions.shift()
        }
        // logic read expectation
        let expectationForData = ''
        if (orderItemMeta?.expectationFor)
          expectationForData = orderItemMeta.expectationFor
        else if (formData?.expectationFor && isMentalHealth.value) {
          const localExpectationFor = formData?.expectationFor
          if (localExpectationFor === 'myself') expectationForData = 'for-me'
          else if (localExpectationFor === 'couple')
            expectationForData = 'for-couple'
        }
        stateData.expectationFor = expectationForData
        expectationFor.value = expectationForData

        updatePatientInfor(patient.value)
        // if (configAgent.value === 'aia')
        if (isMentalHealth.value) {
          paramsSearch.value.filter.provider = orderItemMeta?.provider._id
          executeSearchConsultations()
        }
        // Re-update the user ID in the query search and option upload, as the data in the state may be delayed.
        let pId = orderItemMeta?.patientInfo?._id
        if (!pId) pId = authenUser.value._id
        searchFilesOption.value.filter.user = pId
        uploadOption.value.user = pId
        loading.value = false
        // after read data from localStorage, remove it (because we dont need it any more)
        // localStorage.removeItem('formData')
      }, 1500)
    })
    return {
      name,
      medicalRecord,
      isValidForm,
      reason,
      chiefComplaint,
      questions,
      showInputFields,
      removeQuestion,
      patientInfo,
      loading,
      valid,
      canShowUpload,
      submit,
      rules,
      isIOS,
      fileTotal,
      searchFilesOption,
      uploadOption,
      chiefComplaintDialog,
      updateChiefcomplaint,
      expectationFor,
      expectations,
      showLabel,
      patient,
      shouldShowExistedComponent,
      existedConsultation,
      existedProvider,
      isMentalHealth
    }
  }
})
</script>
