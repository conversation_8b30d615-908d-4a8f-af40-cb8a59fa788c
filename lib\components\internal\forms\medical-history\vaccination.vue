<template>
  <v-card elevation="0" color="transparent">
    <span class="font-italic text--secondary text-justify caption">
      {{
        $t(
          'false information may lead to misdiagnosis and inappropriate treatment decisions'
        )
      }}
    </span>
    <v-form ref="formRef" v-model="valid" class="v-form">
      <w-vaccination-overview
        v-show="vaccines.length"
        ref="vaccinationRef"
        :class="[{ 'w-vaccination': !vaccines.length }, 'pt-4']"
        :user-id="patientId"
        @click:add="open"
      />
      <v-radio-group
        v-if="!vaccines.length"
        v-model="radioGroup"
        class="ml-2"
        :rules="rules"
        @change="onChangeRadio"
      >
        <template v-for="(item, i) in reasons">
          <v-radio :key="i" :value="i" class="mb-4" :ripple="false">
            <template #label>
              <span v-html="$t(item.label)"></span>
              <span v-if="item.required" class="red--text">*</span>
            </template>
          </v-radio>

          <v-slide-y-transition :key="`vaccine-${i}`">
            <div v-if="radioGroup === 0 && radioGroup === i" class="mb-4">
              <w-vaccination-overview
                ref="vaccinationRef"
                :class="[{ 'w-vaccination': !vaccines.length }]"
                :user-id="patientId"
                @click:add="open"
              />

              <span
                v-show="!isValidVaccines"
                :class="['red--text mt-2 caption']"
                >{{ $t('required') }}</span
              >
            </div>
          </v-slide-y-transition>
        </template>
      </v-radio-group>
    </v-form>
    <v-dialog
      v-model="dialog"
      :fullscreen="$vuetify.breakpoint.mobile"
      max-width="600"
    >
      <v-card>
        <v-card
          :class="[
            'd-flex justify-space-between align-center pa-4',
            {
              'pt-8': $vuetify.breakpoint.mobile
            }
          ]"
          :style="{
            zIndex: '1'
          }"
        >
          <div class="d-flex align-center">
            <v-btn class="mr-2" icon @click="dialog = false">
              <v-icon>$close</v-icon>
            </v-btn>
            <p class="title font-weight-medium mb-0">
              {{ $t('vaccinations') }}
            </p>
          </div>
          <v-spacer />
        </v-card>

        <v-card
          class="overflow-auto"
          elevation="0"
          :style="{
            height: $vuetify.breakpoint.mobile ? '93vh' : '80vh'
          }"
        >
          <w-vaccination-editor
            ref="vaccinationRef"
            class="rounded-xl"
            :user-id="patientId"
          />
        </v-card>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useStore,
  watch,
  nextTick
} from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'Vaccination',
  props: {
    label: {
      type: String,
      default: ''
    },
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const { i18n } = useContext()
    const { state, commit } = useStore()
    const vaccinationRef = ref()
    const name = ref<string>(props.label)
    const dialog = ref<boolean>(false)
    const radioGroup = ref<number>(null)
    const formRef = ref()
    const valid = ref<boolean>(false)

    const reasons = [
      {
        label: 'Vaccine & Date',
        required: true
      },
      {
        label: 'None or Unknown'
      }
      // {
      //   label: 'Refuse to provide. Affirm a fully in-time vaccination.'
      // }
    ]

    const rules = computed(() => [
      (v: number) => {
        return v !== null || i18n.t('required')
      },
      (v) => {
        return v !== '' || i18n.t('required')
      }
    ])

    const vaccines = computed(() => (state as any)?.phr?.vaccination?.vaccines)

    const patientId = computed<string>(
      () => props?.orderItem?.meta?.patientInfo?._id || ''
    )

    const isValidVaccines = computed<boolean>(() => {
      const valid: boolean | any = Array.isArray(vaccines.value)
        ? Boolean(vaccines.value?.length)
        : true

      return valid || radioGroup.value === 1
    })

    const submit = () => {
      formRef.value.validate()

      if (!valid.value) return false

      return isValidVaccines.value
    }

    const open = () => {
      dialog.value = true
    }

    const onChangeRadio = (key: number) => {
      commit('checkout/SET_DATA_VACCINATION', key)

      switch (key) {
        case null:
          commit('checkout/SET_SUMMARIZE_VACCINATION', '')
          break
        case 0:
          commit(
            'checkout/SET_SUMMARIZE_VACCINATION',
            i18n.t('needle', {
              vaccines: vaccines.value?.length
            })
          )
          break
        case 1:
          commit('checkout/SET_SUMMARIZE_VACCINATION', reasons[key].label)
          break
        default:
          break
      }
    }

    watch(
      [vaccines, radioGroup],
      () => {
        const checked = (state as any)?.checkout?.stepperValidDated?.vaccination
          ?.data

        radioGroup.value =
          vaccines.value?.length && checked !== null ? 0 : checked
        onChangeRadio(radioGroup.value)
      },
      {
        immediate: true
      }
    )

    nextTick(() => {})

    return {
      reasons,
      dialog,
      vaccinationRef,
      patientId,
      isValidVaccines,
      name,
      radioGroup,
      valid,
      formRef,
      rules,
      vaccines,
      onChangeRadio,
      submit,
      open
    }
  }
})
</script>

<style scoped>
.v-form >>> div {
  margin-left: 0px !important;
}

.w-vaccination >>> div {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
</style>
