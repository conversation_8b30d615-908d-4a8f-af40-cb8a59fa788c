<template>
  <v-container fluid class="fill-height pa-0">
    <v-row :key="'choose-medium-component'">
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ step }}. {{ $t(title) }}
        </h3>
      </v-col>
      <v-col cols="12" class="pt-2" @submit.prevent="submit">
        <form id="w-spatial" ref="spatialForm">
          <v-radio-group
            v-model="chosenDeliveryType"
            class="spatial-group"
            hide-details
          >
            <template #label>
              <h4 class="body-1 font-weight-medium text--secondary">
                {{ $t('chọn địa điểm') }}
              </h4>
            </template>
            <v-radio
              v-for="(item, index) in deliveryTypes"
              :key="`spatial-${index}`"
              :value="item.value"
              class="w-card spacial-item"
            >
              <template #label>
                <p class="mb-0 d-flex align-center">
                  <span class="place"> {{ $t(item.text) }} </span>
                </p>
              </template>
            </v-radio>
          </v-radio-group>
          <v-radio-group
            v-model="chosenPharmacy"
            class="drugstore-group"
            hide-details
          >
            <template #label>
              <h4 class="body-1 font-weight-medium text--secondary">
                {{ $t('chọn nhà thuốc') }}
              </h4>
            </template>
            <v-radio
              v-for="(item, index) in drugstores"
              :key="`drugstore-${index}`"
              :value="item.value"
              :disabled="item.disabled"
              class="w-card drugstore-item"
            >
              <template #label>
                <p class="mb-0 d-flex align-center">
                  <span class="place"> {{ $t(item.text) }} </span>
                </p>
              </template>
            </v-radio>
          </v-radio-group>
        </form>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  watch,
  useRoute,
  useContext,
  watchEffect
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IOrder, IOrderItem } from '@lib/repositories'
import { IStep } from '@lib/repositories/workflow/flow.interface'
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'

export default defineComponent({
  // name: "medicine-fullfillment-type",
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem<any>>,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  emits: ['emit-data'],
  setup(props, { emit }) {
    // const store: any = useStore()
    const { $toast } = useContext()

    const state = reactive({
      deliveryTypes: null,
      orgName: null,
      consultation: null
    })
    const rules = {
      deliveryTypes: { required },
      orgName: { required },
      consultation: { required }
    }
    const v$ = useVuelidate(rules, state)
    // required consultation
    const route = useRoute()
    const consultation = computed(() => route.value.query.consultation)
    watch(consultation, () => {
      // emit('emit-data', { consultation: consultation.value })
      state.consultation = consultation.value
    })
    if (!consultation.value) $toast.error('Missing consultation')
    const step = computed<number | any>(() => props.currentStep.step)
    const title = computed<String | any>(() => props.currentStep.content.title)
    // choose delivery
    const chosenDeliveryType = ref('')
    const deliveryTypes = reactive([
      {
        value: 'homeDelivery',
        text: 'Giao thuốc tới nhà'
      },
      {
        value: 'storePickup',
        text: 'Nhận Tại Nhà Thuốc'
      }
    ])
    watch([chosenDeliveryType], () => {
      // emit('submit', { deliveryType: chosenDeliveryType.value })
      // store.commit(
      //   "checkout/SET_NEXT_STEP",
      //   chosenDeliveryType.value === "storePickup"
      //     ? "medicine-pending-delivery"
      //     : "address"
      // );
      state.deliveryTypes = chosenDeliveryType.value
    })
    chosenDeliveryType.value = 'homeDelivery'

    // choose pharmacy
    const drugstores = reactive([
      {
        value: 'pharmacity',
        text: 'Nhà thuốc Pharmacity',
        disabled: false
      },
      {
        value: 'longChau',
        text: 'Nhà Thuốc Long Châu',
        disabled: true
      }
    ])
    const chosenPharmacy = ref('')
    watch(chosenPharmacy, (choice) => {
      // emit('submit', { orgName: choice })
      state.orgName = choice
    })
    watchEffect(() => {
      state.consultation = consultation.value
    })
    chosenPharmacy.value = 'pharmacity'
    const submit = () => {
      // if (!state.orgName || !state.deliveryTypes || !state.consultation) {
      //   $toast.error('please full fill field')
      //   return
      // }
      // v$.value.$touch()
      // v$.value.$validate().then((isValidated: boolean) => {
      //   console.log(isValidated, 'line 175')
      //   if (isValidated) emit('submit', state)
      // })
      emit('submit', state)
    }
    return {
      step,
      title,
      chosenDeliveryType,
      deliveryTypes,
      chosenPharmacy,
      drugstores,
      submit,
      state,
      rules,
      v$
    }
  }
})
</script>
