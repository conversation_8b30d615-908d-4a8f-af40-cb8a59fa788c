<template>
  <v-card flat color="transparent">
    <v-card-title>
      <h3>{{ $t('processing the prescription') }}</h3>
    </v-card-title>
    <v-list color="transparent text-body-1" three-line>
      <div v-for="(item, index) in stepsCheck" :key="index">
        <v-list-item>
          <v-list-item-content>
            <v-list-item-title class="text-break text-wrap font-weight-bold">
              <v-chip
                v-if="!item.completed && !item.failed"
                :color="currStep === index ? 'primary' : ''"
                >{{ index + 1 }}</v-chip
              >
              <v-icon v-else-if="item.completed" color="primary">
                $check
              </v-icon>
              <v-icon v-else-if="item.failed" color="red"> $close </v-icon>
              {{ $t(item.title) }}
            </v-list-item-title>
            <div
              class="text-break text-wrap"
              :class="{ 'red--text': item.failed }"
            >
              {{ $t(item.content) }}
            </div>
            <div
              v-if="item.subcontent"
              class="text-break text-wrap text-caption"
            >
              {{ $t(item.subcontent) }}
            </div>
            <!-- <v-list-item-subtitle
                v-dompurify-html="item.content"
                class="text-break text-wrap"
                :class="{ 'red--text': item.failed }"
              ></v-list-item-subtitle> -->
            <!-- <v-list-item-subtitle
                v-if="item.subcontent"
                v-dompurify-html="item.subcontent"
                class="text-break text-wrap"
              ></v-list-item-subtitle> -->
          </v-list-item-content>
        </v-list-item>
        <!-- <v-btn
            v-if="index === 2"
            class="float-end"
            color="primary"
            href="tel:18006821"
          >
            {{ $t('Gọi Phramacity') }}
          </v-btn> -->
      </div>
    </v-list>
  </v-card>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref
} from '@nuxtjs/composition-api'
import type { PropType, Ref } from '@nuxtjs/composition-api'

import {
  IOrder,
  IPopulatedOrderItem,
  updateOrder,
  updateOrderItem
} from '../../../../repositories'

export default defineComponent({
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IPopulatedOrderItem<any>>,
      required: true
    },
    isUpdate: {
      type: Boolean,
      default: true
    },
    hashValue: {
      type: String,
      default: ''
    },
    linkPdf: {
      type: String,
      required: true
    }
  },
  setup(props) {
    // const updateOrderItemData = ref({
    //   _id: props.orderItem._id,
    //   paymentTerms: "cod",
    // });
    const currStep = ref(0)
    const stepsCheck = reactive([
      {
        completed: false,
        failed: false,
        title: 'electronically sign prescriptions',
        content: 'processing, please wait'
      },
      {
        completed: false,
        failed: false,
        title: 'transfer the prescription to the pharmacy',
        content: ''
      },
      {
        completed: false,
        failed: false,
        title: 'the pharmacy confirms',
        content: 'your prescription has been transferred to the pharmacy',
        subcontent: ''
      },
      {
        completed: false,
        failed: false,
        title: 'delivery prescription in progress',
        content: ''
      }
    ])

    const orderItemId = computed<string>(() => props.orderItem._id ?? '')
    const orderId = computed<string>(() => props.order._id)

    const checkErr = ref(false)

    const updateOrderItemData = computed<any>(() => {
      return {
        _id: orderItemId.value,
        paymentTerms: 'cod',
        tags: ['hash:' + props.hashValue]
      }
    })
    const {
      response,
      execute: executeUpdateOrderItem,
      onSucess
    } = updateOrderItem(updateOrderItemData)
    let countFetch = 0
    const interval = setInterval(() => {
      fetch(props.linkPdf, { method: 'HEAD' }).then((res) => {
        if (res.status === 200) {
          stepsCheck[0].completed = true
          stepsCheck[0].content = ''
          currStep.value = 1
          clearInterval(interval)
          executeUpdateOrderItem()
        } else if (countFetch >= 5) {
          stepsCheck[0].failed = true
          stepsCheck[0].content = 'please try again'
          clearInterval(interval)
        }
        countFetch++
      })
    }, 1500)
    const updatedData: Ref<any> = ref({
      _id: orderId.value,
      state: 'placed'
    })
    const { execute } = updateOrder(updatedData)
    onSucess(() => {
      // console.log(response.value, 'line 143')
      if (response.value.code === 200) {
        stepsCheck[1].content = ''
        stepsCheck[1].completed = true
        currStep.value = 2
        if (props.isUpdate) execute()
      } else {
        stepsCheck[1].failed = true
        stepsCheck[1].subcontent = 'please try again'
      }
    })

    return {
      checkErr,
      stepsCheck,
      currStep
    }
  }
})
</script>
