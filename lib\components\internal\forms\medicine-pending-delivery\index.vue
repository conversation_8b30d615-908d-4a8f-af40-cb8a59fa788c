<template>
  <div>
    <div v-if="!loadedPdf">
      <div v-if="!isRefuse">
        <v-skeleton-loader
          class="mb-8"
          max-height="120"
          type="image"
        ></v-skeleton-loader>
        <v-skeleton-loader
          class="mb-8"
          max-height="120"
          type="image"
        ></v-skeleton-loader>
        <v-skeleton-loader
          class="mb-8"
          max-height="120"
          type="image"
        ></v-skeleton-loader>
      </div>
      <div v-else class="mx-auto text-center">
        <!-- <v-img
          class="mx-auto my-5"
          max-width="150"
          :src="require('@/lib/static/icons/icons8-medical-report-66.png')"
        /> -->
        <h3>{{ $t('the prescription has been canceled') }}</h3>
        <v-btn class="my-5" color="primary" @click="dialog = true">
          {{ $t('reorder the prescription') }}
        </v-btn>
      </div>
    </div>

    <div v-else>
      <prescription-ready
        v-if="orderItem.meta.deliveryTypes === 'storePickup'"
        :order="order"
        :order-item="orderItem"
        :hash-value="hashValue"
        :is-update="isUpdate"
        :link-pdf="linkPdf"
        @submit="$emit('submit', $event)"
      />
      <home-delivery
        v-else
        :order="order"
        :order-item="orderItem"
        :hash-value="hashValue"
        :is-update="isUpdate"
        :link-pdf="linkPdf"
      />
    </div>
    <v-dialog
      v-model="dialog"
      width="350"
      transition="scroll-y-reverse-transition"
      persistent
    >
      <v-card class="text-center" rounded="lg">
        <v-card-title class="text-break d-flex justify-center">
          <h4 class="text-center">{{ $t('confirm prescription order') }}</h4>
        </v-card-title>
        <v-card-text class="text-subtitle-1">
          {{ $t('please choose to continue') }}
        </v-card-text>
        <v-divider style="border-color: #757575" />
        <v-card-actions class="d-flex justify-space-around">
          <v-btn class="none-uppercase" text @click="refuseConfirm">{{
            $t('cancel')
          }}</v-btn>
          <v-divider style="border-color: #757575" vertical />
          <v-btn
            class="none-uppercase"
            text
            color="primary"
            @click="acceptConfirm"
            >{{ $t('accept') }}</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, ref, watch } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import prescriptionAdapter from '../../../../models/adapters/prescription.adapter'
import {
  createPdf,
  useHandwrittenSignature,
  IOrder,
  getPrescription,
  IPopulatedOrderItem,
  hashObject,
  searchOrderItem,
  getLinkCDN
} from '../../../../repositories'
import prescriptionReady from './prescription-ready.vue'
import homeDelivery from './home-delivery.vue'

export default defineComponent({
  // name: "MedicinePendingDelivery",
  components: {
    prescriptionReady,
    homeDelivery
  },
  props: {
    orderItem: {
      type: Object as PropType<IPopulatedOrderItem<any>>,
      required: true
    },
    order: {
      type: Object as PropType<IOrder>,
      required: true
    }
  },
  setup(props, { emit }) {
    const consultation = computed(() => props.orderItem.meta.consultation)

    const dialog = ref<boolean>(true)
    const isConfirm = ref<boolean>(false)
    const isRefuse = ref<boolean>(false)

    // props pass to home-delivery component
    const hashValue = ref<string>('')
    const isUpdate = ref<boolean>(true)

    const optionSearchOderItem = ref({
      locale: 'vi',
      fields: 'description,tags,state,meta',
      limit: 1,
      sort: '-createdAt',
      filter: {}
    })
    const {
      response: resultsSearch,
      execute: excecuteSearch,
      onSucess: searchSuccess
    } = searchOrderItem(optionSearchOderItem)

    const {
      data,
      loaded,
      loading,
      execute: executeGetPrescription
    } = getPrescription(consultation)
    // const dataPdf = ref({});
    const linkPdf = ref(props.orderItem?.meta?.attachment)
    const pdfOption = ref({
      userId: '',
      template: 'prescription',
      data: {}
    })
    const {
      response,
      onSucess,
      execute,
      loaded: loadedPdf
    } = createPdf(pdfOption)

    watch(loaded, () => {
      if (loaded.value !== true) return
      const type = 'prescription-deliver'
      emit('submit', {
        type
      })
      const item = computed(() => ({
        orderItem: props.orderItem,
        signData: useHandwrittenSignature(
          data.value.consultation?.provider?.name
        )
      }))
      const dataPdfTmp: any = prescriptionAdapter(data, item)
      hashValue.value = hashObject(dataPdfTmp, 'date')
      pdfOption.value = {
        ...pdfOption.value,
        userId: data.value.consultation?.user?._id,
        data: dataPdfTmp
      }
      optionSearchOderItem.value.filter = {
        tags: {
          $in: ['hash:' + hashValue.value]
        },
        state: {
          $in: 'placed'
        }
      }
      excecuteSearch()
      // console.log("hash tmp value line 134: ", hashDataPdfTmp);
      // if (hashPdfData.value !== hashDataPdfTmp) {
      //   execute();
      // } else {
      //   loadedPdf.value = true;
      // }
    })

    searchSuccess(() => {
      // console.log(resultsSearch.value, "line 149");
      const res = resultsSearch.value.results[0]
      if (resultsSearch.value.total > 0) {
        isUpdate.value = false
        linkPdf.value = res.meta?.attachment?.url
        loadedPdf.value = true
      } else {
        isUpdate.value = true
        execute()
      }
    })

    onSucess(() => {
      const res = response.value.results
      const pdfUrl = response.value.results.url
      const pdfCDN = getLinkCDN(pdfUrl)
      linkPdf.value = pdfCDN
      res.cdnUrl = pdfCDN
      emit('submit', {
        attachment: res
      })
    })

    const acceptConfirm = () => {
      isConfirm.value = true
      isRefuse.value = false
      dialog.value = false
    }

    const refuseConfirm = () => {
      isRefuse.value = true
      dialog.value = false
    }

    watch(isConfirm, () => {
      if (isConfirm.value) executeGetPrescription()
    })
    return {
      linkPdf,
      consultation,
      data,
      loading,
      loaded,
      loadedPdf,
      isUpdate,
      hashValue,
      dialog,
      isConfirm,
      acceptConfirm,
      refuseConfirm,
      isRefuse
    }
  }
})
</script>
<style lang="css" scoped>
.card--prescriptionReady {
  border: none;
  box-shadow: none !important;
}
</style>
