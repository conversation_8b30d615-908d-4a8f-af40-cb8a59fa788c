<template>
  <v-card flat class="mx-auto" color="transparent">
    <v-card-title class="text-center">
      <h1>{{ $t('Đang xử lý ...') }}</h1>
    </v-card-title>
    <v-card-text class="mt-5 text-body-1">
      <v-list color="transparent" three-line>
        <div v-for="(item, index) in stepsCheck" :key="index">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title>
                <v-chip
                  v-if="!item.completed && !item.failed"
                  :color="currStep === index ? 'primary' : ''"
                  >{{ index + 1 }}</v-chip
                >
                <v-icon v-else-if="item.completed" color="success">
                  $check
                </v-icon>
                <v-icon v-else-if="item.failed" color="red"> $close </v-icon>
                {{ item.title }}
              </v-list-item-title>
              <v-list-item-subtitle
                v-dompurify-html="item.content"
                :class="item.failed ? 'red--text' : ''"
              ></v-list-item-subtitle>
              <v-list-item-subtitle
                v-if="item.subcontent"
                v-dompurify-html="item.subcontent"
              ></v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
          <v-btn
            v-if="index === 1"
            :disabled="disabledBtn"
            class="float-end"
            depressed
            outlined
            :href="linkPdf"
          >
            <v-icon>$filePdfBox</v-icon> {{ $t(statusPdfBtn) }}
          </v-btn>
        </div>
      </v-list>
    </v-card-text>
    <!-- <v-card-actions class="d-flex justify-center">
      <v-btn depressed outlined :href="linkPdf">
        <v-icon>$filePdfBox</v-icon> {{ $t(statusPdfBtn) }}
      </v-btn>
    </v-card-actions> -->
    <v-card-text class="text-center mt-5">
      {{
        $t(
          'Bạn cũng có thể yêu cầu Giao thuốc tới nhà hoặc Gọi để được tư vấn thêm.'
        )
      }}
    </v-card-text>
    <v-card-actions class="d-flex justify-center">
      <v-btn depressed color="primary" @click="onDeliveryMedicineHome">
        {{ $t('Giao thuốc tới nhà') }}
      </v-btn>
    </v-card-actions>
    <v-card-text class="d-flex justify-center align-center">
      <div style="height: 1px; width: 30px; background-color: #bdbdbd"></div>
      <p class="my-0 mx-3">{{ $t('hoặc') }}</p>
      <div style="height: 1px; width: 30px; background-color: #bdbdbd"></div>
    </v-card-text>
    <v-card-actions class="d-flex justify-center">
      <v-btn depressed outlined href="tel:18006821">
        {{ $t('Gọi Phramacity') }}
      </v-btn>
    </v-card-actions>
  </v-card>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  reactive
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import {
  IOrder,
  IPopulatedOrderItem,
  updateOrder,
  updateOrderItem
} from '../../../../repositories'
// import prescriptionAdapter from "../../../models/adapters/prescription.adapter";

export default defineComponent({
  // name: "medicine-pending-delivery",
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IPopulatedOrderItem<any>>,
      required: true
    },
    isUpdate: {
      type: Boolean,
      default: true
    },
    hashValue: {
      type: String,
      default: ''
    },
    linkPdf: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    // const router = useRouter()

    const orderItemId = computed<string>(() => props.orderItem._id)
    const orderId = computed<string>(() => props.order._id)

    const statusPdfBtn = ref('Tải toa thuốc')
    const disabledBtn = ref(false)

    const currStep = ref(0)
    const stepsCheck = reactive([
      {
        completed: false,
        failed: false,
        title: 'Ký điện tử toa thuốc',
        content: 'Đang xử lý, vui lòng chờ trong giây lát ...'
      },
      {
        completed: false,
        failed: false,
        title: 'Mua tại nhà thuốc',
        content: 'Hãy tải và in toa thuốc để mua tại nhà thuốc'
      }
    ])

    const onDeliveryMedicineHome = () => {
      emit('submit', {
        deliveryTypes: 'homeDelivery'
      })
      // router.push('/checkout/mua-thuoc-pharmacity/address')
    }

    const updateOrderItemData = computed<any>(() => {
      return {
        _id: orderItemId.value,
        paymentTerms: 'cod',
        tags: ['hash:' + props.hashValue]
      }
    })
    const { execute: executeUpdateOrderItem, onSucess } =
      updateOrderItem(updateOrderItemData)
    // executeUpdateOrderItem();
    let countFetch = 0
    const interval = setInterval(() => {
      fetch(props.linkPdf, { method: 'HEAD' }).then((res) => {
        if (res.status === 200) {
          stepsCheck[0].completed = true
          stepsCheck[0].content = ''
          currStep.value = 1
          clearInterval(interval)
          executeUpdateOrderItem()
        } else if (countFetch >= 5) {
          stepsCheck[0].failed = true
          stepsCheck[0].content =
            'Đã có lỗi xảy ra, vui lòng thử lại sau ít phút.'
          clearInterval(interval)
        }
        countFetch++
      })
    }, 1500)
    const updatedData = ref({
      _id: orderId.value,
      state: 'placed'
    })
    const { execute } = updateOrder(updatedData)
    onSucess(() => {
      if (props.isUpdate) execute()
    })
    return {
      onDeliveryMedicineHome,
      // onDownload,
      // loadingPdf,
      statusPdfBtn,
      stepsCheck,
      currStep,
      disabledBtn
    }
  }
})
</script>
<style lang="css" scoped>
.card--prescriptionReady {
  border: none;
  box-shadow: none !important;
}
</style>
