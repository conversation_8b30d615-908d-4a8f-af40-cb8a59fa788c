<template>
  <v-row>
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ $t(currentStepTitle) }}
      </h3>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'
import { useScreenSafeArea } from '@vueuse/core'
import { IStep } from '../../../repositories'

export default defineComponent({
  props: {
    currentStep: {
      type: Object as PropType<IStep>,
      default: () => {}
    }
  },
  setup(props) {
    const { bottom } = useScreenSafeArea()
    const currentStepTitle = props.currentStep?.content?.title || ''
    return {
      bottom,
      currentStepTitle
    }
  }
})
</script>
