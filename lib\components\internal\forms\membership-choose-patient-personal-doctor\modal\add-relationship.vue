<template>
  <w-form-user
    ref="formUser"
    :relate-id="relateId"
    :show-relationship="!isMainUser"
    require-avatar
    :default-data="defaultData"
    :w-attrs-date-field="{
      max: isMainUser
        ? $dayjs().subtract(16, 'year').toISOString()
        : $dayjs().toISOString(),
      min: $dayjs().subtract(90, 'year').toISOString()
    }"
    :btn-submit="{ text: 'save' }"
    @on:submit-form="onSubmitForm"
    @on:submit-success="onSubmitFormSuccess"
  />
</template>

<script lang="ts">
import { computed, defineComponent, ref } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { useUser } from '@wellcare/nuxt-module-account/repositories'
import { IUser } from '../../../../../models/user'

export default defineComponent({
  props: {
    textNote: {
      type: String,
      default: ''
    },
    persistent: {
      type: Boolean,
      default: false
    },
    isShowBtnClose: {
      type: Boolean,
      default: true
    },
    typeActionForm: {
      type: String as PropType<'create' | 'update' | 'delete'>,
      default: 'create'
    },
    defaultData: {
      type: Object as PropType<IUser>,
      default: () => ({} as IUser)
    }
  },
  setup({ typeActionForm, defaultData }, { emit }) {
    // DECLARE
    const { user } = useUser()
    const relateId = computed(() => user.value._id)
    const formUser = ref<any>(null)
    const loading = computed<boolean>(() => formUser.value?.loadingForm)

    const onSubmitForm = () => {
      formUser.value.submitFormDataFn(typeActionForm)
    }

    const onSubmitFormSuccess = () => {
      emit('user-added', 'add')
    }

    const isMainUser = computed<boolean>(
      () => defaultData.userId === relateId.value
    )

    return {
      relateId,
      formUser,
      loading,
      onSubmitForm,
      onSubmitFormSuccess,
      isMainUser
    }
  }
})
</script>

<style scoped>
label:focus-within {
  color: var(--v-primary-base);
}

label span:first-child {
  padding-left: 8px;
}

.w-label__required::after {
  content: '*';
  color: red;
}

.w-label__required:first-letter {
  text-transform: uppercase;
}

.v-btn.white--text.primary.v-btn--active >>> .v-icon {
  color: white;
}
</style>
