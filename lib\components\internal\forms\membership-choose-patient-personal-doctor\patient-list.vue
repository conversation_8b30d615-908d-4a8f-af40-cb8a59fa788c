<template>
  <v-row v-if="loading">
    <v-col v-for="i in 4" :key="`loading-patient-${i}`" cols="12" sm="6">
      <v-card rounded="lg" flat>
        <v-card-text class="py-1">
          <v-skeleton-loader
            type="list-item-avatar-two-line"
          ></v-skeleton-loader>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
  <v-container v-else>
    <v-row>
      <template v-for="(item, index) in patients">
        <v-col
          :key="item.related._id"
          cols="12"
          sm="6"
          :class="{ 'py-2': $vuetify.breakpoint.xsOnly }"
        >
          <v-badge
            :value="selectedIndex === index"
            left
            overlap
            icon="$check"
            color="primary"
            style="width: 100%"
          >
            <v-card
              class="py-2 rounded-lg d-flex align-center"
              :style="{
                'box-shadow':
                  'rgba(149, 157, 165, 0.2) 0px 8px 24px !important',
                ...(getInfo(item.related).age > 16 && {
                  'box-shadow': 'none !important',
                  'background-color': 'rgb(203 203 203 / 15%)'
                })
              }"
              :disabled="getInfo(item.related).age > 16"
              hover
              height="90"
              :class="selectedIndex === index ? 'active-card' : ''"
              @click="selectPatient(index)"
            >
              <v-list-item style="width: 100%">
                <w-avatar
                  :name="item.related.name"
                  :user-id="item.related._id"
                  :src="getAvatarUrl(item)"
                  size="60"
                  class="mr-2"
                />
                <v-list-item-content style="cursor: pointer">
                  <v-list-item-title
                    class="font-weight-medium"
                    :style="{
                      width: item.removable
                        ? '190px !important'
                        : '250px !important',
                      'word-wrap': 'break-word !important'
                    }"
                  >
                    {{ item.related.name }}
                  </v-list-item-title>
                  <v-list-item-subtitle
                    >{{
                      `${getInfo(item.related).formattedAge}, ${
                        getInfo(item.related).gender
                      }, ${$t(item.relationship)}`
                    }}
                  </v-list-item-subtitle>
                  <v-list-item-subtitle
                    v-if="!getAvatarUrl(item)"
                    class="red--text"
                  >
                    {{ $t('require-avatar') }}
                  </v-list-item-subtitle>
                </v-list-item-content>
                <v-list-item-action class="ml-1">
                  <v-btn
                    v-if="!getAvatarUrl(item)"
                    icon
                    @click="
                      () => {
                        selectPatient(index)
                        requireAvatar()
                      }
                    "
                  >
                    <v-icon color="primary" size="20">$edit</v-icon>
                  </v-btn>
                  <!-- <v-btn
                    v-else-if="
                      item.removable && getInfo(item.related).age <= 16
                    "
                    icon
                    @click.prevent="showDelConfirmDialog(item)"
                  >
                    <v-icon size="20" color="error">$trashOutline</v-icon>
                  </v-btn> -->
                </v-list-item-action>
              </v-list-item>
            </v-card>
          </v-badge>
        </v-col>
      </template>
    </v-row>
    <v-btn
      v-show="canAddRelationship"
      outlined
      color="primary"
      class="mt-5 my-3 rounded-lg"
      style="letter-spacing: normal; height: 44px"
      float
      @click="onOpenFormRelationship"
    >
      <v-icon> $accountPlus </v-icon>
      <span class="ms-2">
        {{ $t('add') }}
      </span>
    </v-btn>

    <w-bottom-sheet-wrapper
      ref="bottomSheetWrapperRef"
      max-width="500"
      is-btn-close
      :fullscreen="$vuetify.breakpoint.mobile"
      :persistent="persistentDialogRelationship"
    >
      <template #title>
        <h4>{{ $t('baby information') }}</h4>
      </template>
      <template #fluid>
        <add-relationship
          :key="key"
          :type-action-form="typeActionForm"
          :default-data="defaultDataFrom"
          @user-added="refreshList"
        />
      </template>
    </w-bottom-sheet-wrapper>
    <confirm-delete
      v-model="confirmDelete"
      @cancel="confirmDelete = false"
      @executeDelete="deletePatient"
    />
  </v-container>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  nextTick,
  useContext,
  watch,
  useStore,
  onBeforeMount,
  onMounted
} from '@nuxtjs/composition-api'
import { IUser } from '@wellcare/nuxt-module-data-layer'
import {
  useProductOrder,
  usePatients,
  ISearchOption
} from '../../../../repositories'
import { useRerender, dobFormat } from '../../../../composables'
import addRelationship from './modal/add-relationship.vue'
import confirmDelete from './modal/comfirm-delete.vue'

export default defineComponent({
  components: {
    addRelationship,
    confirmDelete
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  setup(_props, { emit }) {
    let addNewRelation = false

    const { commit } = useStore()
    const { orderItem, authenUser } = useProductOrder()
    const { $config, $dayjs } = useContext()
    const { key, rerenderSrc } = useRerender()

    const bottomSheetWrapperRef = ref()
    const itemDelete = ref<any>({})
    const confirmDelete = ref<boolean>(false)
    const selectedIndex = ref<number>(-1)
    const searchOption = ref<ISearchOption>({
      filter: {
        user: authenUser.value._id,
        relationship: { $ne: 'doctor' }
      }
    })
    const persistentDialogRelationship = ref<boolean>(false)
    const typeActionForm = ref<'create' | 'update' | 'delete'>('create')
    const isFocusRequireAvatar = ref<boolean>(false)
    const textNote = ref<string>(
      'please fill out the information below and include your avatar'
    )
    // search consultation api, to check
    const patientId = ref<string>('')

    const initForm = {
      name: '',
      dob: '',
      relationship: 'child',
      gender: '',
      avatar: {
        url: ''
      }
    }
    const defaultDataFrom = reactive<any>(initForm)

    const {
      executeDelete,
      // onSucess,
      loading,
      patients,
      getRelationships
    } = usePatients(searchOption)

    const choosePatientUICustom = computed<any>(
      () => $config?.checkout?.UIcustomize?.choosePatient
    )
    const canAddRelationship = computed<boolean>(() => {
      if (!choosePatientUICustom.value?.canAddRelationship) return true
      return choosePatientUICustom.value?.canAddRelationship
    })

    const getInfo = (item: IUser) => {
      if (!item || !item.dob) {
        return ''
      }

      const age = $dayjs().diff($dayjs(item.dob), 'years')
      const formattedAge = dobFormat(item.dob)
      const gender = item?.gender

      return {
        age,
        formattedAge,
        gender
      }
    }

    const onOpenFormRelationship = () => {
      bottomSheetWrapperRef.value?.toggleOpen()
      isFocusRequireAvatar.value = false
      typeActionForm.value = 'create'
      // Clear form when open create
      for (const i of Object.keys(defaultDataFrom)) {
        if (i !== 'relationship') {
          defaultDataFrom[i] = ''
        }
      }
    }

    // LOGIC
    const emitPatient = () => {
      const patient = patients.value[selectedIndex.value]

      emit('patient', {
        patient: patient?.related._id,
        patientInfo: patient?.related
      })
    }

    const requireAvatar = () => {
      isFocusRequireAvatar.value = false
      // Retrieve the selected patient's information from the patients array using the given index
      const patientSelect = patients.value[selectedIndex.value]

      // Check if the selected patient's related information, including avatar URL, is missing or undefined
      if (!patientSelect?.related?.avatar?.url) {
        isFocusRequireAvatar.value = true
        // // Increment the refreshKey value to trigger a refresh
        // refreshKey.value = Date.now()

        // Set the typeActionForm value to 'update'
        typeActionForm.value = 'update'

        // Enable the addition of a new relationship
        bottomSheetWrapperRef.value?.toggleOpen()

        // Display the persistent dialog for relationship
        persistentDialogRelationship.value = true

        // Store the selected patient's relationship in defaultDataFrom object
        defaultDataFrom.relationship = patientSelect?.relationship

        // Iterate through the properties of the patientSelect.related object
        for (const i of Object.keys(patientSelect?.related)) {
          // Update defaultDataFrom with values from patientSelect.related object
          defaultDataFrom[i === '_id' ? 'userId' : i] =
            i === 'dob'
              ? $dayjs(patientSelect.related[i]).format('YYYY-MM-DD') // Format date using dayjs
              : patientSelect.related[i]
        }
      }
    }

    const getAvatarUrl = (p) => {
      return p?.related?.avatar?.url || ''
    }
    // when a patient is selected, check if patient have any pending consultation
    const selectPatient = (index: number) => {
      selectedIndex.value = index
      const patient = patients.value[index]
      patientId.value = patient?.related?._id
      emitPatient()
    }

    const scrollToSelected = async () => {
      await nextTick()
      const selectedElement = document.querySelector('.active-card')
      if (selectedElement) {
        selectedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
    }

    const refreshList = async (type: string) => {
      if (type === 'add') addNewRelation = true
      await getRelationships().then(() => {
        const index = getSelectedIndex(patients.value)
        if (index >= 0) {
          selectedIndex.value = index
          patientId.value = patients.value[index].related._id
        }
      })

      await nextTick()

      emitPatient()

      scrollToSelected()
    }

    const showDelConfirmDialog = (data: any) => {
      confirmDelete.value = true
      itemDelete.value = data
    }

    const deletePatient = () => {
      executeDelete(itemDelete.value._id)
      itemDelete.value = null
      confirmDelete.value = false
    }

    // default is 0
    // if new relationship has been added, index will be length - 1
    // if have patient in store, find index in list
    const getSelectedIndex = (patientList: any[]) => {
      if (isFocusRequireAvatar.value) return selectedIndex.value
      if (addNewRelation) {
        addNewRelation = false
        return patientList.length - 1
      }
      const patientId = orderItem.value?.meta?.patientInfo?._id || ''
      const index = patientList.findIndex(
        (el) => el?.related?._id === patientId
      )
      return index
      // if (index > -1) return index
      // return -1
    }

    watch(selectedIndex, () => {
      commit('checkout/updateField', {
        path: 'btnNextState',
        value: {
          isDisabled: selectedIndex.value < 0
        }
      })
    })

    // after get list patient, get selected index (which patient is choosen)
    // then check patient have any pending consultation
    // onSucess(() => {
    //   const index = getSelectedIndex(patients.value)
    //   if (index >= 0) {
    //     selectedIndex.value = index
    //     patientId.value = patients.value[index].related._id
    //   }
    // })

    onBeforeMount(() => {
      // FIRST LOAD
      getRelationships()

      // rerender component
      rerenderSrc({
        source: typeActionForm
      })
    })

    onMounted(() => {
      if (selectedIndex.value < 0) {
        commit('checkout/updateField', {
          path: 'btnNextState',
          value: {
            isDisabled: false
          }
        })
      }
    })
    return {
      textNote,
      key,
      authenUser,
      loading,
      patients,
      selectPatient,
      refreshList,
      confirmDelete,
      showDelConfirmDialog,
      deletePatient,
      getAvatarUrl,
      requireAvatar,
      selectedIndex,
      canAddRelationship,
      onOpenFormRelationship,
      persistentDialogRelationship,
      typeActionForm,
      defaultDataFrom,
      bottomSheetWrapperRef,
      getInfo
    }
  }
})
</script>
<style scoped>
.active-card {
  border: 2px solid var(--v-primary-base);
  background-color: #00968710;
}
</style>
