<template>
  <div>
    <v-row>
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ $t('personal doctor') }}
        </h3>
        <h4 class="body-1 text--secondary">
          {{ $t('Exclusive for Members') }}
        </h4>
      </v-col>
      <MemberBenefit :basic-price="initialPrice" :description="description" />
      <v-col cols="12" class="mb-n2">
        <div
          v-for="(option, index) in benefitOption"
          :key="`${option._id}${index}`"
        >
          <div
            class="py-1 benefit-options d-flex justify-space-between align-center"
          >
            <div
              style="width: 100%"
              class="d-flex justify-space-between grey--text text--darken-4 w-100 font-weight-bold"
            >
              <div class="d-flex">
                <span class="primary--text mr-1">✓</span>
                <span class="primary--text pr-1">{{ $t(option.title) }} </span>
              </div>
              <div class="primary--text">
                {{
                  `${$n(option.price, {
                    style: 'currency',
                    currency: 'VND'
                  })}`
                }}
              </div>
            </div>
          </div>
          <v-card-subtitle
            :style="{ fontSize: '15px', color: '#666666' }"
            class="py-1 px-0 pl-5"
          >
            <p v-dompurify-html="$t(option.description)" class="mb-0"></p>
          </v-card-subtitle>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import {
  ref,
  computed,
  useRoute,
  useStore,
  onMounted,
  useContext,
  defineComponent
} from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'
import { useMappingProduct } from '../../../composables/index'
import {
  prepareSingleOrder,
  useSearchProviderBySlug
} from '../../../repositories'
import { BenefitOption } from '../../../models'
import MemberBenefit from './choose-membership/member-benefit.vue'

export default defineComponent({
  components: { MemberBenefit },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    // Test slug: checkout/personal-doctor?patientId=6662ad97ebfba8081f4fb256121221&providerSlug=nhuan-wellcare-test
    const route = useRoute()
    const { state }: any = useStore()
    const { $axios, $config }: any = useContext()
    const initialPrice = ref(250000)
    const isEminent = computed(
      () => props.orderItem?.product?.meta?.isEminent ?? false
    )
    const description =
      'Membership (all-year waiver of the service fee and other benefits)'
    const patientId = computed(
      () => state.checkout?.orderItems[0]?.meta?.patientInfo?._id
    )

    const providerSlug = computed(() => {
      const rawSlug =
        route.value?.query?.providerSlug ||
        route.value?.params?.product ||
        state.checkout?.orderItems[0]?.meta?.personalDoctorSlug

      if (!rawSlug) return ''

      const cleanedSlug = rawSlug.replace(
        /(-membership|-personal-doctor)+$/,
        ''
      )
      return cleanedSlug
    })

    const { execute, response, results } = useSearchProviderBySlug(providerSlug)
    execute().then((res) => {
      console.log(res)
    })

    const { execute: executeProductOrder } = prepareSingleOrder()

    const submit = async () => {
      await executeProductOrder({
        product: {
          slug: 'personal-doctor-addon'
        }
      }).then(async (res) => {
        const orderItemId = res?.results?.orderItems[0]?._id

        await $axios.put(`${$config.endpoint}/api/order-item/${orderItemId}`, {
          update: {
            image: {
              url: results.value?.avatar?.url
            },
            meta: {
              _id: orderItemId,
              patientId: patientId.value,
              personalDoctorId: results.value?._id,
              personalDoctorName: results.value?.name,
              personalDoctorTitle: results.value?.title,
              personalDoctorSlug: results.value?.slug,
              benefits: [
                {
                  key: 'personal-doctor',
                  user: patientId.value,
                  provider: results.value?._id,
                  isEminent: isEminent.value
                }
              ]
            }
          }
        })
      })

      emit('submit', {
        benefits: [
          {
            user: patientId.value,
            isEminent: isEminent.value
          }
        ]
      })
      $axios.put(
        `${$config.endpoint}/ecommerce/order/${props.order?._id}/apply-change`
      )
    }

    const goToPayment = () => true
    const touch = () => {
      const payload: any = {
        title: 'membership',
        description:
          'Membership (all-year waiver of the service fee and other benefits)',
        meta: {
          ...props.orderItem.meta
        }
      }
      if (state.authen?.user?.isMember) {
        payload.customPrice = true
        payload.note = 'paid'
        payload.price = 0
      }
      emit('set-order-item', payload)
      return true
    }

    const product = computed(() => props.orderItem?.product)
    const benefitOption: Ref<BenefitOption[]> = ref([])
    const { getBenefitName } = useMappingProduct()

    onMounted(() => {
      const rawProduct = [...product.value.includes]
      rawProduct.forEach((include: any) => {
        benefitOption.value.push({
          title: include.name || '',
          price: include.price || 0,
          slug: include.slug || '',
          state: include.slug || 'published',
          ...getBenefitName(include.slug)
        })
      })
    })

    return {
      touch,
      submit,
      results,
      response,
      patientId,
      description,
      goToPayment,
      benefitOption,
      initialPrice
    }
  }
})
</script>

<style scoped>
.benefit-options >>> .v-messages__message {
  font-size: 14px !important;
  line-height: 150% !important;
  color: #4b5563;
}

.benefit-options >>> .v-input--selection-controls {
  margin-top: 5px !important;
}

.benefit-options >>> .v-input__slot {
  display: flex;
  align-items: flex-start;
}
</style>
