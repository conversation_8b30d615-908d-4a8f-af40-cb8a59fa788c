<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  computed,
  watch,
  useStore,
  onBeforeMount
} from '@nuxtjs/composition-api'
import isToday from 'dayjs/plugin/isToday'

export default defineComponent({
  name: 'FormDueDate',
  props: {},
  setup(_, { emit }) {
    const { $dayjs, i18n, $vuetify } = useContext()
    $dayjs.extend(isToday)
    const { commit, state } = useStore()

    const formRef = ref()
    const form = ref()
    const dueDate = ref<string>('')
    const dueDateFormatted = ref<string>('')
    const datePickerRef = ref()

    const wAttrsDateField = {
      min: $dayjs().toISOString(),
      max: $dayjs().add(38, 'week').toISOString()
    }

    const isMobile = computed<boolean>(() => $vuetify.breakpoint.mobile)

    const getPregnancyDueDate = computed<string>(
      () => (state as any)?.checkout?.pregnancy?.dueDate
    )

    const rules = computed(() => [
      (v: string) => {
        return !!v || i18n.t('required field 1')
      },
      (v: string) =>
        !$dayjs(v).isToday() ||
        i18n.t('dueDateValid: 1', {
          date: dueDate.value
        })
    ])

    const save = (val: { dateRaw: string; dateFormatted: string }) => {
      dueDate.value = val.dateRaw
      dueDateFormatted.value = val.dateFormatted
    }

    const valid = async () => {
      const valid = await formRef.value.validate()
      if (!valid) return false
      emit('on:save', dueDate.value)
      return true
    }

    watch(dueDate, () => {
      commit('checkout/SET_PREGNANCY_DUE_DATE', dueDate.value)
    })

    onBeforeMount(() => {
      dueDate.value = getPregnancyDueDate.value
    })

    return {
      form,
      formRef,
      dueDate,
      rules,
      datePickerRef,
      wAttrsDateField,
      save,
      valid,
      isMobile,
      dueDateFormatted
    }
  }
})
</script>

<template>
  <v-form ref="formRef" v-model="form" class="form">
    <v-text-field
      v-model="dueDateFormatted"
      readonly
      outlined
      class="rounded-lg"
      :placeholder="$t('Input your due date')"
      :rules="rules"
      @click="datePickerRef.open()"
    >
      <template #append>
        <v-btn small icon @click="datePickerRef.open()">
          <v-icon>$calendar</v-icon>
        </v-btn>
      </template></v-text-field
    >
    <w-picker-date-v2
      ref="datePickerRef"
      :default-value="dueDate"
      :w-attrs="wAttrsDateField"
      year-month-day
      :title="$t('expected due date')"
      @on:save="save"
    />
  </v-form>
</template>

<style scoped>
.form >>> .v-messages__message {
  line-height: 25px;
  color: #2a2a2a;
  margin-left: -8px;
}
</style>
