<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  useContext,
  useRouter,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import { useUser } from '@wellcare/nuxt-module-account/repositories'
import { watchOnce } from '@vueuse/core'
import confetti from 'canvas-confetti'

import { useProductOrder, usePregnancy } from '../../../../repositories'
import TotalPrice from '../choose-membership/total-price.vue'
import FormDueDate from './form-due-date.vue'
import Packages from './packages.vue'

export default defineComponent({
  name: 'PregnancyDiary',
  components: {
    FormDueDate,
    Packages,
    TotalPrice
  },
  props: {
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { state, commit } = useStore()
    const { $dayjs, $vuetify } = useContext()
    const router = useRouter()
    const {
      register: registerPregnancyDiary,
      onRegisterSuccess,
      loading: loadingPregnancy
    } = usePregnancy()
    const { updateOrderItem, loading: loadingOrder } = useProductOrder()
    const { renewStoreAuthen, loading: loadingUser } = useUser()

    const formDueDateRef = ref()
    const packageRef = ref()
    const total = ref<number>(0)
    const isGoToPayment = ref<boolean>(true)
    const weekDueDate = ref<number | string>('--')
    const isBottom = ref<boolean>(false)

    onMounted(() => {
      commit('checkout/SET_PREGNANCY', true)

      window.addEventListener('scroll', checkIsBottom)

      // Initial check in case the page is already scrolled to bottom on mount
      checkIsBottom()
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', checkIsBottom)
    })

    const pregData = reactive({
      dueDate: ''
    })

    const getPackageMemberPrice = computed<number>(
      () => (state as any)?.checkout?.pregnancy?.packages?.membership?.price
    )

    const loading = computed<boolean>(
      () => loadingPregnancy.value || loadingUser.value || loadingOrder.value
    )

    const getIsMembership = computed<boolean>(
      () => (state as any)?.authen?.user?.isMember
    )

    // const getMainOrderItem = computed(
    //   () => (state as any).checkout?.mainOrderItem
    // )

    const dueDate = computed(() => (state as any)?.checkout?.pregnancy.dueDate)

    const isShowBtnScroll = computed<boolean>(
      () => $vuetify.breakpoint.height < 800 && !isBottom.value
    )

    const isScrollable = computed<boolean>(() => {
      const html: any = document.getElementsByTagName('html')[0]
      return html.scrollHeight > html.clientHeight
    })
    const optionsScroll = computed(() =>
      isScrollable.value
        ? { offset: -200 }
        : { container: document.body, offset: 100 }
    )

    const checkIsBottom = () => {
      const scrollPosition = window.scrollY + window.innerHeight
      const pageHeight = document.documentElement.scrollHeight
      isBottom.value = scrollPosition >= pageHeight
    }

    const scrollToBottom = () => {
      $vuetify.goTo(packageRef.value, optionsScroll.value)
    }

    const getPackages = (val: { totalPrice: number }) => {
      total.value = val.totalPrice
    }

    const touch = async (): Promise<boolean> => {
      const valid = await formDueDateRef.value.valid()

      if (!valid) {
        $vuetify.goTo(formDueDateRef.value, {
          offset: 400
        })
        return false
      }

      return true
    }

    const isPositive = (number: number): boolean => {
      return number >= 0
    }

    const submit = async () => {
      if (getIsMembership.value && !packageRef.value?.addon.length) {
        isGoToPayment.value = false

        updateOrderItem({
          _id: props.orderItem?._id,
          state: 'cancelled'
        })

        await registerPregnancyDiary({
          preg_date: pregData.dueDate,
          preg_subscribe: true
        })
      } else {
        emit('set-order-item', {
          _id: props?.orderItem._id,
          title: 'pregnancy program',
          description: 'Pregnancy week-by-week and Asynchronous telehealth'
        })

        emit('submit', {
          preg_date: pregData.dueDate
        })

        packageRef.value.submit()
      }
    }

    const goToPayment = (): boolean => {
      return isGoToPayment.value
    }

    const fire = () => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: {
          y: 0.5
        }
      })
    }

    onRegisterSuccess(async () => {
      await renewStoreAuthen()
      router.push('/membership/health-programs/pregnancy-diary')
    })

    // Watch once fire on first mounted
    watchOnce(dueDate, () => {
      fire()
    })

    watch(
      dueDate,
      (newVal, oldVal) => {
        const current = $dayjs().startOf('day')
        const pregDate = $dayjs(newVal)
        const result = 42 - pregDate.diff(current, 'week')

        weekDueDate.value = isPositive(result) ? result : '--'

        if (newVal && oldVal && newVal !== oldVal) {
          fire()
        }
      },
      {
        immediate: true
      }
    )

    return {
      total,
      formDueDateRef,
      packageRef,
      pregData,
      getPackages,
      submit,
      touch,
      dueDate,
      weekDueDate,
      goToPayment,
      getPackageMemberPrice,
      loading,
      isShowBtnScroll,
      scrollToBottom
    }
  }
})
</script>

<template>
  <div class="pb-6">
    <w-loading v-model="loading" :opacity="0.8" :size-loading="3" />
    <v-row class="pregnancy-diary" :style="{ marginBottom: '-100px' }">
      <div class="pregnancy-wrapper pa-3 d-flex justify-center flex-column">
        <v-img
          src="https://storage.googleapis.com/cms-gallery/677e67dc45f9f3582bb88e72/pregnancy.png"
          class="mt-4"
          contain
          height="200"
        />

        <v-slide-y-transition>
          <p
            v-if="dueDate && weekDueDate > 0"
            class="text-center font-weight-bold text-18 font-italic"
            style="margin-top: 10px; color: #009688; padding: 0 16px"
          >
            {{
              $t('congratulations! you are {weekDueDate} weeks pregnant.', {
                weekDueDate: weekDueDate
              })
            }}
          </p>
        </v-slide-y-transition>
        <div
          class="mt-3"
          style="
            background: rgb(255 255 255 / 40%);
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            padding: 18px 15px 0px 15px;
            border-radius: 16px;
          "
        >
          <div class="mb-0 text-h7 font-weight-bold">
            {{ $t('Due date calculator') }}:
          </div>
          <div class="mt-2">
            <FormDueDate
              ref="formDueDateRef"
              @on:save="
                (val) => {
                  const dueDate = $dayjs(val).toISOString()
                  pregData.dueDate = dueDate
                }
              "
            />
          </div>
        </div>
      </div>

      <v-col cols="12">
        <Packages
          ref="packageRef"
          :due-date="pregData.dueDate"
          :order-item="orderItem"
          @on:change="getPackages"
        />
      </v-col>
      <v-col cols="12">
        <v-slide-y-reverse-transition
          ><template v-if="total"><TotalPrice :total-price="total" /></template
        ></v-slide-y-reverse-transition>
      </v-col>
    </v-row>
    <v-btn
      v-if="isShowBtnScroll"
      fab
      fixed
      bottom
      right
      small
      icon
      elevation="4"
      :style="{
        backgroundColor: 'var(--v-primary-base)',
        bottom: total ? '210px' : '130px',
        color: 'white'
      }"
      @click="scrollToBottom"
      ><v-icon>$chevronDown</v-icon></v-btn
    >
  </div>
</template>

<style>
.text-h7 {
  font-size: 16px;
  color: #251202;
}

.pregnancy-wrapper {
  background-image: linear-gradient(180deg, #ffc398, rgb(185 253 253 / 61%));
  width: 100%;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}
</style>
