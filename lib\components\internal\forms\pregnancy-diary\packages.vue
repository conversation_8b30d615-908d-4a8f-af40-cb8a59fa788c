<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import { watchOnce } from '@vueuse/core'
import { getPricePregnancy } from '../../../../composables'
import { prepareSingleOrder, useProductOrder } from '../../../../repositories'
import StatusTag from '../choose-membership/status-tag.vue'

// Hàm kiểm tra xem một đối tượng có tồn tại trong mảng không
const objectExists = (obj, array) => {
  for (const item of array) {
    if (item.slug === obj.slug) {
      return true
    }
  }
  return false
}

export default defineComponent({
  name: 'Packages',
  components: {
    StatusTag
  },
  props: {
    dueDate: {
      type: String,
      default: ''
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(_, { emit }) {
    const { $dayjs, i18n } = useContext()
    const { state, commit } = useStore()
    const { hits, products, loading } = getPricePregnancy()
    const { order, updateOrderItem } = useProductOrder()
    const { execute: reloadOrder, onSucess: onProductOrderSuccess } =
      prepareSingleOrder()
    const model: any = ref(null)
    const addon = ref([])
    const orderItems = ref([])

    const getOrderCreated = computed<string>(
      () => order.value?.createdAt || order.value?.date
    )

    const getExpirationDate = computed<string>(() =>
      $dayjs(getOrderCreated.value)
        .add(1, 'year')
        .subtract(1, 'day')
        .format('YYYY-MM-DD')
    )

    const getDueDate = computed<string>(
      () => (state as any).checkout?.pregnancy?.dueDate
    )

    const isMember = computed<boolean>(
      () => (state as any).authen?.user.isMember
    )

    const membership = computed<any>(
      () => (state as any).authen?.user.membership[0]
    )

    const getPriceMember = computed<number>(
      () => products.value?.member?.price || 0
    )

    const getDesMember = computed<string>(
      () =>
        products.value?.member?.description ||
        'Free access to Pregnancy by week and HealthGPT for member'
    )

    const getAddon = computed(() => products.value?.addon)

    const totalPrice = computed(() => {
      let total = getPriceMember.value

      for (const item of addon.value) {
        total += item.price
      }

      return total
    })

    const isWarning = computed<boolean>(() => {
      if (!getDueDate.value || !isMember.value) return false

      const expMember = $dayjs(membership.value?.endAt)
      const dueDate = $dayjs(getDueDate.value)

      return dueDate.isAfter(expMember)
    })

    const updateOrderProductSingle = (products: any[]) => {
      for (const item of products) {
        const param = {
          product: {
            slug: item.output.slug
          }
        }

        reloadOrder(param)
      }
    }

    const submit = () => {
      const getAddon = [products.value?.member, ...addon.value]

      for (const item of orderItems.value) {
        if (objectExists(item?.product, getAddon)) {
          switch (item?.product?.slug) {
            case 'membership':
              updateOrderItem({
                ...item,
                title: 'membership',
                description: i18n.t(
                  `12 months of validation until {getExpirationDate}`,
                  // 'effective for 12 months'
                  {
                    getExpirationDate: getExpirationDate.value
                  }
                )
              })
              break
            case 'all-year-combo-10-questions':
              updateOrderItem({
                ...item,
                title: 'asynchronous telehealth',
                description:
                  '10 Knowledge questions or follow-up messages with your personal doctor'
              })

              break
            default:
              break
          }
        } else {
          updateOrderItem({
            ...item,
            state: 'cancelled'
          })
        }
      }
    }

    watchOnce(hits, (newVal: any[]) => {
      updateOrderProductSingle(newVal)
    })

    watch([addon, products], () => {
      emit('on:change', {
        totalPrice: totalPrice.value
      })
    })

    watch(orderItems, (newVal: any) => {
      commit('checkout/SET_PREGNANCY_PACKAGES', newVal)
    })

    onProductOrderSuccess((res) => {
      orderItems.value.push(...res.results?.orderItems)
    })

    return {
      model,
      addon,
      products,
      loading,
      getPriceMember,
      getDesMember,
      getAddon,
      isMember,
      membership,
      isWarning,
      submit
    }
  }
})
</script>

<template>
  <v-row v-if="products">
    <v-col cols="12">
      <div data-v-50b6c2aa="" class="mt-4 d-flex justify-space-between">
        <div class="d-flex">
          <div class="mr-2 font-weight-bold">
            {{ $t('basic') }}
          </div>
          <StatusTag v-if="isMember" content="paid" />
        </div>
        <div class="text-subtitle font-weight-bold">
          <p v-if="getPriceMember" class="mb-0">
            {{
              `${$n(getPriceMember, {
                style: 'currency',
                currency: 'VND'
              })}/${$t('year')}`
            }}
          </p>
          <p v-else class="text-decoration-line-through mb-0">
            {{
              `${$n(250000, {
                style: 'currency',
                currency: 'VND'
              })}/${$t('year')}`
            }}
          </p>
        </div>
      </div>
      <v-card-subtitle :style="{ fontSize: '15px' }" class="py-1 px-0"
        >{{ $t(getDesMember) }}.</v-card-subtitle
      >

      <v-scale-transition>
        <v-alert
          v-if="isWarning"
          type="warning"
          text
          dense
          color="warning"
          border="left"
        >
          {{
            $t('warning: expMembership', {
              date: $dayjs(membership.endAt).format('DD/MM/YYYY')
            })
          }}.
        </v-alert>
      </v-scale-transition>
    </v-col>

    <!-- <v-col cols="12">
      <div :style="{ fontSize: '16px' }" class="font-weight-bold">
        {{ $t('additional benefits') }}
      </div>
      <div v-for="(item, index) in getAddon" :key="index">
        <div class="d-flex benefit-options">
          <v-checkbox
            v-model="addon"
            :value="item"
            :messages="$t(item.description)"
            style="width: 100%"
          >
            <template #label>
              <div
                style="width: 100%"
                class="d-flex justify-space-between grey--text text--darken-4 w-100 font-semi-bold"
              >
                <div class="d-flex flex-column justify-center">
                  {{ $t(item.name) }}
                </div>

                <div>
                  {{
                    `${$n(item.price, {
                      style: 'currency',
                      currency: 'VND'
                    })}/${$t('year')}`
                  }}
                </div>
              </div>
            </template>
          </v-checkbox>
        </div>
      </div>
    </v-col> -->
  </v-row>
</template>

<style scoped>
.benefit-options >>> .v-messages__message {
  font-size: 15px !important;
  line-height: 150% !important;
  color: #4b5563;
}

.benefit-options >>> .v-input--selection-controls {
  margin-top: 5px !important;
}
</style>
