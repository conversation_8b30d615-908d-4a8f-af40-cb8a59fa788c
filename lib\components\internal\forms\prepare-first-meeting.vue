<template>
  <v-row :key="'prepare-first-meeting-component'">
    <template v-if="initializing"> <skeleton /> </template>
    <template v-else>
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
        </h3>
      </v-col>
      <v-col cols="12" class="pt-2">
        <div>
          <v-form>
            <v-radio-group v-model="radioGroup" @change="onOptionChange">
              <v-radio
                v-for="option in options"
                :key="option.label"
                :value="option.key"
                class="mb-3 radio rounded-lg px-3 py-4"
                active-class="active"
              >
                <template #label>
                  <h4 class="ml-3 black--text" style="font-size: large">
                    {{ $t(option.label) }}
                  </h4>
                </template>
              </v-radio>
            </v-radio-group>
          </v-form>
        </div>
      </v-col>
    </template>
  </v-row>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useRoute,
  computed,
  onMounted,
  useStore,
  useContext
} from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { ISearchOption } from '../../../repositories'
import { useProvider } from '../../../repositories/provider/use-provider'
import { usePatients } from '../../../repositories/user/use-patients'
import Skeleton from './skeleton.vue'
export default defineComponent({
  components: { Skeleton },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(_, { emit }) {
    const { state }: any = useStore()
    const { i18n } = useContext()
    const authUser = computed(() => state?.authen?.user)
    const radioGroup = ref(0)
    const options = ref([
      {
        label: 'for check-up',
        key: 1
      },
      {
        label: 'for diagnosis',
        key: 0
      }
    ])
    const route = useRoute()
    const patientId = computed(
      () => route.value?.query?.patient_id || '62fb25b587cdc7706808b83b' // mock
    )
    const providerId = computed(
      () => route.value?.query?.provider_id || '5f9b95a96d31d6cd9c67f230' // mock
    )
    const providerOptions = computed(() => ({
      filter: {
        _id: providerId.value
      },
      fields: 'user avatar name title'
    }))

    const searchOption: Ref<ISearchOption> = ref({
      filter: {
        user: authUser.value._id,
        related: patientId.value,
        relationship: { $ne: 'doctor' }
      }
    })

    const {
      execute: executeSearchPatients,
      loading: searchPatientsLoading,
      patients
    } = usePatients(searchOption)
    const patient = computed(() =>
      authUser.value === patientId.value ? patients.value[0] : patients.value[1]
    )

    const {
      executeSearchProvider,
      loading: searchProviderLoading,
      provider
    }: any = useProvider(providerOptions)

    const touch = () => true
    const submit = () => {
      emit('set-order-item', {
        image: {
          url: provider?.value?.avatar?.url || provider?.value?.avatar
        },
        title: provider?.value?.title + ' ' + provider?.value?.name,
        description:
          i18n.t('first meeting') + ' ' + patient?.value?.related?.name
      })
      emit('submit', {
        applyVoucher: false,
        membershipProduct: true,
        noCost: true,
        user: authUser.value,
        provider: provider.value,
        providerUser: provider.value._id,
        patient: patient.value._id,
        patientInfo: patient.value.related
      })
    }
    const onOptionChange = (data: number) => {
      localStorage.setItem('first_meeting_option', data.toString())
      if (data === 0) {
        emit('set-step-meta', {
          step: 2,
          meta: {
            skip: false
          }
        })
        return
      }
      emit('set-step-meta', {
        step: 2,
        meta: {
          skip: true
        }
      })
    }
    const init = () => {
      const cacheOption =
        parseInt(localStorage.getItem('first_meeting_option')) || 0
      onOptionChange(cacheOption)
      radioGroup.value = cacheOption
      executeSearchProvider()
      executeSearchPatients()
    }
    const initializing = computed(
      () => searchPatientsLoading.value || searchProviderLoading.value
    )
    onMounted(() => init())
    return {
      options,
      radioGroup,
      touch,
      submit,
      provider,
      initializing,
      patient,
      onOptionChange
    }
  }
})
</script>
<style scoped>
.radio {
  border: 1px solid silver;
  transition: 0.1s linear;
}
.radio >>> span {
  color: var(--v-primary-base) !important;
}
.radio.active {
  border-color: var(--v-primary-lighten1) !important;
}
</style>
