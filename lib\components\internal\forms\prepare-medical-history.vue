<template>
  <v-row :key="'prepare-first-meeting-component'">
    <v-col cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
      </h3>
    </v-col>
    <v-col cols="12" class="pt-2">
      <h4 style="font-size: 18px; font-weight: bold" class="px-4">
        {{ $t('patient information') }}
      </h4>
      <patient-card :patient-info="patient" :loading="false" />
    </v-col>
    <v-col cols="12" class="pt-2">
      <w-card
        :title="$t('medical history')"
        elevation="0"
        class="transparent _override_card"
      >
        <template #actions><div></div></template>
        <template #description>
          <v-form ref="validationProvider">
            <v-textarea
              v-model="medicalHistory"
              :placeholder="$t('medical history detailed description')"
              :hint="$t('medical history detailed description')"
              validate-on-blur
              :rules="[(v) => !!v || $t('required field')]"
              class="rounded-lg px-4"
              hide-details="auto"
              rows="7"
              no-resize
              outlined
              @blur="updateMedicalHistory"
            ></v-textarea>
          </v-form>
        </template>
      </w-card>
      <div class="my-8" />
      <w-vaccination :user-id="patient._id" />
      <div class="my-8" />
      <w-card
        :title="$t('lab tests')"
        elevation="0"
        class="transparent _override_card"
      >
        <template #actions><div></div></template>
        <template #description>
          <file
            :order="order"
            :order-item="orderItem"
            :user-id="patient._id"
            :search-files-option="searchFilesOption"
            :upload-option="uploadOption"
            class="px-4"
          />
        </template>
      </w-card>

      <template v-if="isAdult"
        ><div class="my-8" />
        <w-body-index-input ref="bodyIndexInput" :user-id="patient._id"
      /></template>

      <template v-else
        ><div class="my-8" />
        <w-growth-chart-input
          ref="growthChartInput"
          :age="patientAge"
          :date-of-birth="$dayjs(patient.dob).format('YYYY-MM-DD')"
      /></template>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import DOMPurify from 'dompurify'
import { ISearchOption } from '@lib/repositories'
import {
  defineComponent,
  ref,
  computed,
  useContext,
  onMounted
} from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import duration from 'dayjs/plugin/duration'
import PatientCard from '../shared/patient-card.vue'
import File from '../shared/file/index.vue'
export default defineComponent({
  components: {
    File,
    PatientCard
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const { $dayjs, $vuetify } = useContext()
    $dayjs.extend(duration)
    const validationProvider = ref(null)
    const bodyIndexInput = ref()
    const growthChartInput = ref()
    const medicalHistory = ref()
    const patient = computed(() => props.orderItem?.meta?.patientInfo)
    const patientAge = computed(() =>
      $dayjs
        .duration(Date.now() - new Date(patient.value.dob).getTime())
        .asYears()
    )
    const isScrollable = computed<boolean>(() => {
      const html: any = document.getElementsByTagName('html')[0]
      return html.scrollHeight > html.clientHeight
    })

    const optionsScroll = computed(() =>
      isScrollable.value ? {} : { container: document.body }
    )
    const isAdult = computed(() => patientAge.value >= 16)
    const searchFilesOption: Ref<ISearchOption> = ref({
      count: true,
      fields:
        'tags, description, url, labels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, capturedAt, duration',
      filter: {
        user: patient.value._id,
        labels: 'history-test-labs'
      }
    })
    const uploadOption: any = ref({
      project: 'emr',
      file: null,
      labels: 'history-test-labs',
      user: patient.value._id
    })
    const touch = async () => {
      const isValidated = await validationProvider.value.validate()
      if (!isValidated) {
        $vuetify.goTo(validationProvider.value, optionsScroll.value)
        return false
      }
      return true
    }
    const updateMedicalHistory = () => {
      emit('submit', {
        medicalHistory: DOMPurify.sanitize(medicalHistory.value)
      })
    }
    const submit = () => {
      if (isAdult.value) bodyIndexInput.value.createObservations()
      else growthChartInput.value.createObservations()
    }

    onMounted(
      () => (medicalHistory.value = props.orderItem?.meta?.medicalHistory || '')
    )
    return {
      medicalHistory,
      bodyIndexInput,
      growthChartInput,
      touch,
      submit,
      isAdult,
      patientAge,
      patient,
      validationProvider,
      searchFilesOption,
      uploadOption,
      updateMedicalHistory
    }
  }
})
</script>
<style scoped>
._override_card {
  cursor: unset !important;
}
._override_card:hover {
  background: unset !important;
}
._override_card >>> h4 {
  font-size: large;
}
</style>
