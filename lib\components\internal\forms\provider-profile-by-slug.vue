<template>
  <div>
    <v-row>
      <v-col cols="12" class="mb-n2">
        <h3 class="headline text--secondary title-dark font-weight-bold">
          {{ $t('personal doctor') }}
        </h3>
      </v-col>
    </v-row>
    <ProviderInfo class="mt-3" :provider="results" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  computed,
  // useRoute,
  useStore
} from '@nuxtjs/composition-api'
import { useSearchProviderBySlug } from '../../../repositories'
import ProviderInfo from './choose-provider/provider-info.vue'

export default defineComponent({
  components: { ProviderInfo },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    currentStep: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    // checkout/nguyen-thai-anh-khoa-wellcare-test-membership-personal-doctor
    // const route = useRoute()
    const { state }: any = useStore()
    const patientId = computed(
      () => state.checkout?.orderItems[0]?.meta?.patient
    )
    const providerBySlug = computed(
      () =>
        props.orderItem?.meta?.provider?.slug ||
        state.checkout?.orderItems[0]?.meta?.personalDoctorSlug ||
        ''
    )
    const { execute, response, results } =
      useSearchProviderBySlug(providerBySlug)
    execute()

    const submit = () => {
      emit('submit', {
        product: results.value?._id,
        patientId: patientId.value,
        personalDoctorId: results.value?._id,
        personalDoctorName: results.value?.name,
        personalDoctorSlug: results.value?.slug,
        benefits: [
          {
            key: 'personal-doctor',
            user: patientId.value,
            provider: results.value?._id
          }
        ],
        plan: {
          key: 'membership'
        }
      })
    }

    const goToPayment = () => true
    const touch = () => true
    return {
      touch,
      submit,
      results,
      response,
      patientId,
      goToPayment
    }
  }
})
</script>
