<template>
  <v-row :key="'question-component'">
    <v-col v-if="!hideTitle" cols="12" class="mb-n2">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ step }}. {{ $t(title) }}
      </h3>
    </v-col>
    <v-col cols="12" class="pt-2">
      <v-form ref="shortQuestion" v-model="isValidForm" class="short-question">
        <v-textarea
          v-model="chiefComplaint"
          :rules="[rules.required, rules.length(25)]"
          maxlength="250"
          auto-grow
          validate-on-blur
          outlined
          counter="250"
          class="rounded-lg"
          :placeholder="isFocus ? '' : $t(UIcustomQuestion.placeholder)"
          :hint="
            $t(
              'unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.'
            )
          "
          name="input-7-4"
          @focus="isFocus = true"
          @blur="isFocus = false"
        >
          <template #message="{ message }">
            {{ $t(message) }}
          </template>
        </v-textarea>
      </v-form>
      <!-- <div
        v-if="chiefComplaintLength === 250"
        class="text-caption text--secondary"
      >
        {{
          $t(
            "keep it short & simple, straight to the point (maximum 250 characters)."
          )
        }}
      </div>
      <v-expand-transition>
        <div v-if="isAutoSave" class="text-caption">
          {{ $t("auto saved") }}
        </div>
      </v-expand-transition>
      -->
    </v-col>
  </v-row>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Component, Emit, Prop, Vue, Ref } from 'vue-property-decorator'
import DOMPurify from 'dompurify'

@Component({})
/* eslint-disable @typescript-eslint/no-unused-vars */
export default class QuestionComponent extends Vue {
  @Ref('shortQuestion')
  shortQuestion: any

  @Prop()
  order: any

  @Prop()
  orderItem: any

  @Prop({ default: false, type: Boolean })
  hideTitle: boolean

  @Prop({ type: Object, required: true })
  currentStep

  chiefComplaint: string = ''
  //   currentStep: any // vuex map field
  rules = {
    required: (value) =>
      !!value || 'this field is required, please enter to continue',
    length: (length) => (value) =>
      value?.length >= length || 'minimum 25 characters'
  }

  query: any
  isAutoSave: boolean = false
  isValidForm: boolean = false
  isFocus: boolean = false

  @Emit('submit')
  emitData(data: any) {
    return data
  }

  get step(): number {
    return this.currentStep?.step
  }

  get title(): string {
    return this.currentStep?.content?.title || 'short question'
  }

  get chiefComplaintLength() {
    return this.chiefComplaint?.length || 0
  }

  get UIcustomQuestion() {
    return this.$config?.checkout?.UIcustomize?.question
  }

  beforeMount() {
    if (this.orderItem) {
      if (this.orderItem?.meta?.chiefComplaint)
        this.chiefComplaint = this.orderItem.meta?.chiefComplaint || ''
    }
  }

  mounted() {
    if (this.orderItem?.meta?.chiefComplaint) {
      const question = this.orderItem.meta.chiefComplaint
      this.chiefComplaint = question.slice(0, 250)
    }
    if (
      this.orderItem &&
      this.orderItem.meta &&
      !this.orderItem.meta.providerInfo
    ) {
      if (this.orderItem?.meta?.provider) {
        // may store state is not complete sync data from previous step
        // so we need wait a few time before update
        setTimeout(() => {
          this.emitData({
            providerInfo: this.orderItem.meta.provider
          })
        }, 850)
      }
    }
  }

  // onValidate(): boolean {
  //   const isValid = (
  //     this.shortQuestion as Vue & {
  //       validate: () => boolean
  //     }
  //   ).validate()
  //   this.isValidForm = isValid
  //   const inputs = [...this.shortQuestion.inputs]
  //   if (inputs.length > 0)
  //     inputs.reverse().forEach((input) => {
  //       if (input.valid === false) {
  //         return input?.focus()
  //       }
  //     })
  //   return isValid
  // }

  submit() {
    // if (!this.touch()) return
    // step choose-patient before may be removed, so we need to check
    const orderItemMeta = this.orderItem?.meta
    const stateData: any = {
      chiefComplaint: DOMPurify.sanitize(this.chiefComplaint)
    }
    // check meta order here, if data empty -> add default data is user infor into it
    if (!orderItemMeta.patient || !orderItemMeta.patientInfo) {
      stateData.patient = this.$store.state.authen.user._id
      stateData.patientInfo = this.$store.state.authen.user
    }
    this.emitData(stateData)
  }

  touch() {
    this.shortQuestion.validate()
    if (this.chiefComplaintLength < 25) {
      this.isValidForm = false
      return false
    }
    return true
  }
}
</script>

<style scoped>
div >>> .v-input__slot {
  align-items: flex-start !important;
}
div >>> .v-input--checkbox {
  margin: 0 !important;
}
div >>> .v-label {
  text-align: justify !important;
}
.short-question >>> .v-messages__message {
  line-height: 25px !important;
  font-size: 13px !important;
}
</style>
