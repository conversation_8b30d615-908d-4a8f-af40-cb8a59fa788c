<template>
  <v-container>
    <h3 class="headline text--secondary title-dark font-weight-bold">
      {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
    </h3>
    <p class="text--secondary">{{ $t(currentStep.content.subtitle) }}</p>
    <patient-list @patient="state.patients = $event" />
    <p class="text--secondary">
      {{ $t('for the first 2 member', { fee: 3000 }) }}
    </p>
  </v-container>
</template>
<script lang="ts">
import { defineComponent, reactive, useContext } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { IOrderItem } from '../../../../models/order-item/props_order-item'
import { IOrder } from '../../../../models/order/props-order'
import { IStep, useProductOrder } from '../../../../repositories'
import PatientList from './patient-list.vue'

export default defineComponent({
  components: {
    PatientList
  },
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem>,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(_, { emit }) {
    // DECLARE VARIABLE
    const { orderItem } = useProductOrder()
    const { $toast } = useContext()
    const state = reactive({
      patients: []
    })
    // LOGIC
    const touch = () => {
      const patients = orderItem.value.meta?.patients || []
      if (state.patients.length === 0 && patients.length === 0) {
        $toast.error('Please choose relative')
        return false
      }
      if (state.patients.length === 0) state.patients = patients
      return true
    }
    const submit = () => {
      emit('submit', { applyVoucher: false, ...state })
    }
    return {
      touch,
      submit,
      state
    }
  }
})
</script>
