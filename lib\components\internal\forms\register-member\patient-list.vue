<template>
  <div>
    <v-row v-if="loading">
      <v-col v-for="i in 4" :key="`loading-patient-${i}`" cols="12" sm="6">
        <v-card rounded="lg" flat>
          <v-card-text class="py-1">
            <v-skeleton-loader
              type="list-item-avatar-two-line"
            ></v-skeleton-loader>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-else>
      <template v-for="(item, index) in patientRelative">
        <v-col
          :key="item.related._id"
          cols="12"
          sm="6"
          :class="{ 'py-2': $vuetify.breakpoint.xsOnly }"
        >
          <v-badge
            :value="item.isSelected"
            left
            overlap
            icon="$check"
            color="primary"
            style="width: 100%"
          >
            <v-card
              class="py-2 rounded-lg"
              hover
              :class="item.isSelected ? 'active-card' : ''"
            >
              <v-list-item>
                <v-list-item-avatar class="mr-2">
                  <w-avatar
                    :name="item.related.name"
                    :user-id="item.related._id"
                    :src="getAvatarUrl(item)"
                    size="40"
                  />
                </v-list-item-avatar>
                <v-list-item-content
                  style="cursor: pointer"
                  @click="selectPatient(index)"
                >
                  <v-list-item-title class="font-weight-medium">
                    {{ item.related.name }}
                  </v-list-item-title>
                  <v-list-item-subtitle>{{
                    $t(item.relationship)
                  }}</v-list-item-subtitle>
                </v-list-item-content>
                <!-- <v-list-item-action>
                  <v-btn
                    v-if="item.removable"
                    icon
                    @click="showDelConfirmDialog(item)"
                  >
                    <v-icon size="20" color="error">$trashOutline</v-icon>
                  </v-btn>
                </v-list-item-action> -->
              </v-list-item>
            </v-card>
          </v-badge>
        </v-col>
      </template>
    </v-row>
    <v-btn
      outlined
      color="primary"
      class="mt-5 my-3"
      float
      @click="addRelationship = true"
    >
      <v-icon> $accountPlus </v-icon>
      {{ $t('add other') }}
    </v-btn>
    <add-relationship
      v-if="addRelationship"
      @cancel="addRelationship = false"
      @user-added="refreshList"
    />
    <confirm-delete
      v-if="confirmDelete"
      @cancel="confirmDelete = false"
      @executeDelete="deletePatient"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, ref } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'
import {
  useProductOrder,
  usePatients,
  ISearchOption
} from '../../../../repositories'
import AddRelationship from '../choose-patient/modal/add-relationship.vue'
import ConfirmDelete from '../choose-patient/modal/comfirm-delete.vue'

export default defineComponent({
  components: {
    AddRelationship,
    ConfirmDelete
  },
  setup(_, { emit }) {
    // INIT DATA
    const { authenUser, orderItem } = useProductOrder()
    const patientRelative: any[] = reactive([])
    const addRelationship: Ref<boolean> = ref(false)
    const confirmDelete: Ref<boolean> = ref(false)
    const itemDelete: Ref<any> = ref(null)
    let isDelete = false
    // REPOSITORIES
    const searchOption: Ref<ISearchOption> = ref({
      filter: {
        user: authenUser.value._id,
        relationship: { $ne: 'doctor' }
      }
    })
    const {
      execute: getListPatient,
      onSucess: onGetListSuccess,
      executeDelete: executeDelPatient,
      loading,
      patients
    } = usePatients(searchOption)
    // FIRST LOAD
    getListPatient()
    // LOGIC
    onGetListSuccess(() => {
      setTimeout(() => {
        getSelected(patients.value)
      }, 500)
    })
    const getSelected = (patients) => {
      // get data from backend in store
      const storeArray: any[] = orderItem.value?.meta?.patients || []
      // if exist patient, mark patient selected
      for (const patient of patients) {
        let isSelected = false
        // check patient exist in store
        if (storeArray.some((e) => e._id === patient.related._id)) {
          isSelected = true
        }
        // if patient exist in array, change variable selected, else push to the end
        const index = patientRelative.findIndex(
          (el) => el.related._id === patient.related._id
        )
        if (index !== -1) {
          patientRelative[index].isSelected = patientRelative[index].isSelected
            ? true
            : isSelected
        } else {
          patientRelative.push({
            ...patient,
            isSelected
          })
        }
      }
      // if list was refreshed after delete, remove patient not exist (the last)
      if (isDelete) patientRelative.pop()
      isDelete = false
    }
    const getAvatarUrl = function (p) {
      return p?.related?.avatar?.url || ''
    }
    const selectPatient = function (i: number) {
      patientRelative[i].isSelected = !patientRelative[i].isSelected
      const statePatients = patientRelative
        .filter((p) => p.isSelected)
        .map((e) => ({
          _id: e.related._id,
          name: e.related.name
        }))
      emit('patient', statePatients)
    }
    const showDelConfirmDialog = function (p) {
      itemDelete.value = p
      confirmDelete.value = true
    }
    const refreshList = function () {
      getListPatient()
      addRelationship.value = false
    }
    const deletePatient = () => {
      if (itemDelete.value) {
        executeDelPatient(itemDelete.value._id).then(() => {
          itemDelete.value = null
          confirmDelete.value = false
          // mark delete to check refresh list
          isDelete = true
        })
      }
    }
    return {
      patientRelative,
      loading,
      getAvatarUrl,
      selectPatient,
      showDelConfirmDialog,
      addRelationship,
      confirmDelete,
      refreshList,
      deletePatient
    }
  }
})
</script>
