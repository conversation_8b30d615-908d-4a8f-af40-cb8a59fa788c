<template>
  <v-container>
    <h3 class="headline text--secondary title-dark font-weight-bold">
      {{ currentStep.step }}. {{ $t(currentStep.content.title) }}
    </h3>
    <v-form ref="requestForm" v-model="isValid" lazy-validation>
      <!-- <v-expansion-panels v-model="panel" multiple flat tile>
        <v-expansion-panel v-for="(item, index) of patientsData" :key="index">
          <v-expansion-panel-header disable-icon-rotate>
            <h4 class="text-secondary">
              {{ index + 1 + '. ' + $t('for ') + item.name }}
            </h4>
            <template #actions>
              <v-icon
                :color="item.medicalDescription === '' ? 'warning' : 'success'"
              >
                {{ item.medicalDescription === '' ? '$alert' : '$check' }}
              </v-icon>
            </template>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-textarea
              v-model="item.medicalDescription"
              :label="$t('medical description') + '*'"
              :rules="[(v) => !!v || $t('Value is required')]"
              outlined
              auto-grow
              no-resize
            />
            <v-text-field
              v-model="item.providerDescription"
              :label="$t('Other request')"
              outlined
            />
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels> -->
      <v-stepper v-model="step" color="transparent" vertical tile flat>
        <template v-for="(item, index) in patientsData">
          <v-stepper-step
            :key="'step' + index"
            :step="index + 1"
            :complete="item.medicalDescription !== ''"
          >
            {{ item.name }}
          </v-stepper-step>
          <v-stepper-content :key="'content' + index" :step="index + 1">
            <v-card color="transparent" tile flat>
              <v-card-text>
                <v-textarea
                  v-model="item.medicalDescription"
                  :label="$t('medical description') + '*'"
                  :rules="[(v) => !!v || $t('Value is required')]"
                  outlined
                  auto-grow
                  no-resize
                  dense
                />
                <v-text-field
                  v-model="item.providerDescription"
                  :label="$t('Other request')"
                  outlined
                  dense
                />
              </v-card-text>
              <v-card-actions>
                <v-btn v-show="step > 1" small text @click="step--">{{
                  $t('back')
                }}</v-btn>
                <v-btn
                  v-show="step < patientsData.length"
                  small
                  color="primary"
                  @click="step === patientsData.length ? step : step++"
                  >{{ $t('next') }}</v-btn
                >
              </v-card-actions>
            </v-card>
          </v-stepper-content>
        </template>
      </v-stepper>
    </v-form>
  </v-container>
</template>
<script lang="ts">
import { computed, defineComponent, ref, watch } from '@nuxtjs/composition-api'
import type { ComputedRef, PropType, Ref } from '@nuxtjs/composition-api'
import { IOrderItem } from '../../../models/order-item/props_order-item'
import { IOrder } from '../../../models/order/props-order'
import { IStep } from '../../../repositories'

export default defineComponent({
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem>,
      required: true
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const patients: ComputedRef<any[]> = computed(
      () => props.orderItem.meta?.patients || []
    )
    const patientsData: Ref<any[]> = ref([
      {
        name: 'loading ...',
        medicalDescription: '',
        providerDescription: ''
      }
    ])
    const requestForm: Ref<HTMLFormElement> = ref(null)
    const isValid: Ref<boolean> = ref(false)
    const step: Ref<number> = ref(1)
    let isSubmit = false
    // FIRST LOAD
    setTimeout(() => {
      patientsData.value = (props.orderItem.meta?.patients || []).map((e) => ({
        _id: e._id,
        name: e.name,
        medicalDescription: e?.medicalDescription || '',
        providerDescription: e?.providerDescription || ''
      }))
    }, 500)
    // LOGIC
    const touch = () => {
      requestForm.value.validate()
      for (let i = 0; i < patientsData.value.length; i++) {
        if (patientsData.value[i].medicalDescription.trim() === '') {
          step.value = i + 1
          return false
        }
      }
      if (!isValid.value) return false
      return true
    }
    const submit = () => {
      isSubmit = true
      emit('submit', {
        patients: patientsData.value
      })
    }
    watch(patients, () => {
      if (patients.value && !isSubmit) {
        patientsData.value = patients.value.map((e) => ({
          _id: e._id,
          name: e.name,
          medicalDescription: e?.medicalDescription || '',
          providerDescription: e?.providerDescription || ''
        }))
      }
    })
    return {
      touch,
      submit,
      goToPayment: () => true,
      patientsData,
      requestForm,
      isValid,
      step
    }
  }
})
</script>
