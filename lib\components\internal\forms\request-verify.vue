<template>
  <div>
    <!-- <v-toolbar class="header-box" style="padding-top: top">
      <div
        class="full-size d-flex align-center justify-space-between font-weight-bold"
      >
        <v-btn style="margin-left: -16px" icon @click="handleBackConversation">
          <v-icon color="black" size="32">$chevronLeft</v-icon>
        </v-btn>
        <p class="step-name">{{ $t('HealthGPT Verification') }}</p>
      </div>
    </v-toolbar> -->
    <div class="main-content">
      <!-- Info -->
      <div class="pt-3 pl-5 pr-3 d-flex align-center">
        <w-avatar
          :key="refreshKey"
          size="56"
          :src="botAvatar"
          :name="botName"
        />

        <div class="pl-3 d-flex flex-column">
          <p class="topic-name">{{ $t(title) }}</p>
          <p>{{ $t('Chatbot ' + botName) }}</p>
          <p class="mb-0" style="font-size: 14px; text-transform: lowercase">
            {{ amountMessage }}
            {{ amountMessage > 1 ? $t('messages') : $t('message') }},
            {{ formatNumber(amountWord) }}
            {{ amountWord > 1 ? $t('words') : $t('word') }}
          </p>
        </div>
      </div>

      <!-- Question -->
      <div class="mt-5 px-5 d-flex flex-column">
        <p style="font-size: 18px">
          {{ $t('Questions, your expectations') }}
        </p>
        <v-textarea
          v-model="question"
          class="custom-input mt-2"
          auto-grow
          outlined
          clearable
          spellcheck="false"
          clear-icon="$close-circle"
          row-height="24"
          :rules="rules.question"
        />
        <p style="font-size: 18px; text-align: right">
          {{ wordCount }}/{{ formatNumber(maxWord) }} words
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  watch,
  // useStore,
  // useRouter,
  useContext
} from '@nuxtjs/composition-api'
import { useScreenSafeArea } from '@vueuse/core'
import numeral from 'numeral'

import { useProductOrder } from '../../../repositories'

export default defineComponent({
  props: {
    maxWord: {
      type: Number,
      default: 150
    }
  },
  setup(props, { emit }) {
    const { i18n } = useContext()
    // const router = useRouter()
    const { top } = useScreenSafeArea()
    const { orderItem } = useProductOrder()
    const question = ref('')
    const wordCount = ref(0)

    const title = ref('Default Title')
    const botAvatar = ref('')
    const botName = ref('Name of Bot')
    const amountMessage = ref(0)
    const amountWord = ref(0)
    const contentId = ref('ContentId')
    const conversationId = ref('conversationId')
    const userId = ref('userId')
    const refreshKey = ref<number>(0)

    const rules: any = {
      question: [
        () =>
          question.value.length <= props.maxWord ||
          i18n.t(`Questions should not exceed ${props.maxWord} words`)
      ]
    }

    watch(orderItem, () => {
      title.value = orderItem.value.meta.title
      botAvatar.value = orderItem.value.meta['bot-avatar']
      botName.value = orderItem.value.meta['bot-name']
      amountMessage.value = orderItem.value.meta['amount-message']
      amountWord.value = orderItem.value.meta['amount-word']
      contentId.value = orderItem.value.meta.contentId
      conversationId.value = orderItem.value.meta.conversationId
      userId.value = orderItem.value.meta.userId
      refreshKey.value++
    })

    const formatNumber = (number: any) => {
      return numeral(number).format('0,0')
    }

    // const handleBackConversation = () => {
    //   router.push(`/${conversationId.value}/${userId.value}`)

    //   commit('chat/updateField', {
    //     path: 'isVerified',
    //     value: false
    //   })
    // }

    const touch = () => {
      return question.value.length <= props.maxWord
    }

    const submit = () => {
      if (!touch()) return

      emit('submit', {
        question: question.value,
        contentId
      })
    }

    return {
      top,
      question,
      rules,
      wordCount,
      title,
      botAvatar,
      botName,
      amountMessage,
      amountWord,
      contentId,
      touch,
      submit,
      formatNumber,
      refreshKey
    }
  }
})
</script>

<style scoped>
p {
  margin-bottom: 0;
}
.header-box {
  height: 60px;
  z-index: 10 !important;
  box-shadow: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
.full-size {
  width: 100% !important;
  height: 100% !important;
}
.step-name {
  flex: 1;
  font-size: 22px;
  margin-bottom: 0;
}
.main-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
.topic-name {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
}
.custom-input >>> .v-input__slot {
  margin-bottom: 0;
}
.custom-input >>> .v-text-field__details {
  display: none;
  margin-bottom: 0;
}
.v-btn.v-size--default {
  font-size: 1.175rem;
}
</style>
