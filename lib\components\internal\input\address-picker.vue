<template>
  <v-card>
    <v-card-title
      class="primary white--text"
      :style="{ 'padding-top': padding.top + 15 + 'px' }"
    >
      {{ type === 'add' ? $t('add new address') : $t('edit address') }}
    </v-card-title>
    <v-card-text>
      <v-form ref="form" v-model="isValid">
        <v-autocomplete
          v-model="address.province"
          class="mt-5"
          outlined
          :label="$t('province/city')"
          hide-no-data
          :items="cityProvinceAddress"
          :filter="customSearch"
          :rules="[(v) => !!v || $t('required field')]"
          item-text="value"
        />
        <v-textarea
          v-model="address.line1"
          auto-grow
          outlined
          :label="$t('address')"
          :rules="[(v) => !!v || $t('required field')]"
        />
      </v-form>
    </v-card-text>
    <v-card-actions>
      <v-btn block depressed color="primary" @click="saveAddress">
        {{ $t('save') }}
      </v-btn>
    </v-card-actions>
    <v-card-actions>
      <v-btn block outlined color="primary" @click="$emit('cancelAddress')">
        {{ $t('cancel') }}
      </v-btn>
    </v-card-actions>
  </v-card>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  useStore,
  watch,
  watchEffect
} from '@nuxtjs/composition-api'

// const province: Array<string> = "An Giang, Bà Rịa – Vũng Tàu, Bạc Liêu, Bắc Giang, Bắc Kạn, Bắc Ninh, Bến Tre, Bình Dương, Bình Định, Bình Phước, Bình Thuận, Cà Mau, Cao Bằng, Cần Thơ, Đà Nẵng, Đắk Lắk, Đắk Nông, Điện Biên, Đồng Tháp, Gia Lai, Hà Giang, Hà Nam, Hà Nội, Hà Tĩnh, Hải Dương, Hải Dương, Hậu Giang, Hòa Bình, Thành phố Hồ Chí Minh, Hưng Yên, Khánh Hòa, Kiên Giang, Kon Tum, Lai Châu, Lạng Sơn, Lào Cai, Lâm Đồng, Long An, Nam Định, Nghệ An, Ninh Bình, Ninh Thuận, Phú Thọ, Phú Yên, Quảng Bình, Quảng Nam, Quảng Ngãi, Quảng Ninh, Quảng Trị, Sóc Trăng, Sơn La, Tây Ninh, Thái Bình, Thái Nguyên, Thanh Hóa, Thừa Thiên Huế, Tiền Giang, Trà Vinh, Tuyên Quang, Vĩnh Long, Vĩnh Phúc, Yên Bái".split(
//   ", "
// );
// const searchProvinceLC: Array<string> = "An Giang, Ba Ria – Vung Tau, Bac Lieu, Bac Giang, Bac Kan, Bac Ninh, Ben Tre, Binh Duong, Binh Đinh, Binh Phuoc, Binh Thuan, Ca Mau, Cao Bang, Can Tho, Da Nang, Dak Lak, Dak Nong, Dien Bien, Dong Thap, Gia Lai, Ha Giang, Ha Nam, Ha Noi, Ha Tinh, Hai Duong, Hai Duong, Hau Giang, Hoa Binh, Thanh pho Ho Chi Minh, Hung Yen, Khanh Hoa, Kien Giang, Kon Tum, Lai Chau, Lang Son, Lao Cai, Lam Dong, Long An, Nam Dinh, Nghe An, Ninh Binh, Ninh Thuan, Phu Tho, Phu Yen, Quang Binh, Quang Nam, Quang Ngai, Quang Ninh, Quang Tri, Soc Trang, Son La, Tay Ninh, Thai Binh, Thai Nguyen, Thanh Hoa, Thua Thien Hue, Tien Giang, Tra Vinh, Tuyen Quang, Vinh Long, Vinh Phuc, Yen Bai".split(', ').map((item: string) => item.toLowerCase());
interface IProvince {
  value: string
  LC: string
  UC: string
}
const province: Array<IProvince> = [
  { value: 'An Giang', LC: 'an giang', UC: 'AN GIANG' },
  {
    value: 'Bà Rịa – Vũng Tàu',
    LC: 'ba ria – vung tau',
    UC: 'BA RIA – VUNG TAU'
  },
  { value: 'Bạc Liêu', LC: 'bac lieu', UC: 'BAC LIEU' },
  { value: 'Bắc Giang', LC: 'bac giang', UC: 'BAC GIANG' },
  { value: 'Bắc Kạn', LC: 'bac kan', UC: 'BAC KAN' },
  { value: 'Bắc Ninh', LC: 'bac ninh', UC: 'BAC NINH' },
  { value: 'Bến Tre', LC: 'ben tre', UC: 'BEN TRE' },
  { value: 'Bình Dương', LC: 'binh duong', UC: 'BINH DUONG' },
  { value: 'Bình Định', LC: 'binh đinh', UC: 'BINH ĐINH' },
  { value: 'Bình Phước', LC: 'binh phuoc', UC: 'BINH PHUOC' },
  { value: 'Bình Thuận', LC: 'binh thuan', UC: 'BINH THUAN' },
  { value: 'Cà Mau', LC: 'ca mau', UC: 'CA MAU' },
  { value: 'Cao Bằng', LC: 'cao bang', UC: 'CAO BANG' },
  { value: 'Cần Thơ', LC: 'can tho', UC: 'CAN THO' },
  { value: 'Đà Nẵng', LC: 'da nang', UC: 'DA NANG' },
  { value: 'Đắk Lắk', LC: 'dak lak', UC: 'DAK LAK' },
  { value: 'Đắk Nông', LC: 'dak nong', UC: 'DAK NONG' },
  { value: 'Điện Biên', LC: 'dien bien', UC: 'DIEN BIEN' },
  { value: 'Đồng Nai', LC: 'dong nai', UC: 'DONG NAI' },
  { value: 'Đồng Tháp', LC: 'dong thap', UC: 'DONG THAP' },
  { value: 'Gia Lai', LC: 'gia lai', UC: 'GIA LAI' },
  { value: 'Hà Giang', LC: 'ha giang', UC: 'HA GIANG' },
  { value: 'Hà Nam', LC: 'ha nam', UC: 'HA NAM' },
  { value: 'Hà Nội', LC: 'ha noi', UC: 'HA NOI' },
  { value: 'Hà Tĩnh', LC: 'ha tinh', UC: 'HA TINH' },
  { value: 'Hải Dương', LC: 'hai duong', UC: 'HAI DUONG' },
  { value: 'Hải Phòng', LC: 'hai phong', UC: 'HAI PHONG' },
  { value: 'Hậu Giang', LC: 'hau giang', UC: 'HAU GIANG' },
  { value: 'Hòa Bình', LC: 'hoa binh', UC: 'HOA BINH' },
  {
    value: 'Thành phố Hồ Chí Minh',
    LC: 'thanh pho ho chi minh',
    UC: 'THANH PHO HO CHI MINH'
  },
  { value: 'Hưng Yên', LC: 'hung yen', UC: 'HUNG YEN' },
  { value: 'Khánh Hòa', LC: 'khanh hoa', UC: 'KHANH HOA' },
  { value: 'Kiên Giang', LC: 'kien giang', UC: 'KIEN GIANG' },
  { value: 'Kon Tum', LC: 'kon tum', UC: 'KON TUM' },
  { value: 'Lai Châu', LC: 'lai chau', UC: 'LAI CHAU' },
  { value: 'Lạng Sơn', LC: 'lang son', UC: 'LANG SON' },
  { value: 'Lào Cai', LC: 'lao cai', UC: 'LAO CAI' },
  { value: 'Lâm Đồng', LC: 'lam dong', UC: 'LAM DONG' },
  { value: 'Long An', LC: 'long an', UC: 'LONG AN' },
  { value: 'Nam Định', LC: 'nam dinh', UC: 'NAM DINH' },
  { value: 'Nghệ An', LC: 'nghe an', UC: 'NGHE AN' },
  { value: 'Ninh Bình', LC: 'ninh binh', UC: 'NINH BINH' },
  { value: 'Ninh Thuận', LC: 'ninh thuan', UC: 'NINH THUAN' },
  { value: 'Phú Thọ', LC: 'phu tho', UC: 'PHU THO' },
  { value: 'Phú Yên', LC: 'phu yen', UC: 'PHU YEN' },
  { value: 'Quảng Bình', LC: 'quang binh', UC: 'QUANG BINH' },
  { value: 'Quảng Nam', LC: 'quang nam', UC: 'QUANG NAM' },
  { value: 'Quảng Ngãi', LC: 'quang ngai', UC: 'QUANG NGAI' },
  { value: 'Quảng Ninh', LC: 'quang ninh', UC: 'QUANG NINH' },
  { value: 'Quảng Trị', LC: 'quang tri', UC: 'QUANG TRI' },
  { value: 'Sóc Trăng', LC: 'soc trang', UC: 'SOC TRANG' },
  { value: 'Sơn La', LC: 'son la', UC: 'SON LA' },
  { value: 'Tây Ninh', LC: 'tay ninh', UC: 'TAY NINH' },
  { value: 'Thái Bình', LC: 'thai binh', UC: 'THAI BINH' },
  { value: 'Thái Nguyên', LC: 'thai nguyen', UC: 'THAI NGUYEN' },
  { value: 'Thanh Hóa', LC: 'thanh hoa', UC: 'THANH HOA' },
  {
    value: 'Thừa Thiên Huế',
    LC: 'thua thien hue',
    UC: 'THUA THIEN HUE'
  },
  { value: 'Tiền Giang', LC: 'tien giang', UC: 'TIEN GIANG' },
  { value: 'Trà Vinh', LC: 'tra vinh', UC: 'TRA VINH' },
  { value: 'Tuyên Quang', LC: 'tuyen quang', UC: 'TUYEN QUANG' },
  { value: 'Vĩnh Long', LC: 'vinh long', UC: 'VINH LONG' },
  { value: 'Vĩnh Phúc', LC: 'vinh phuc', UC: 'VINH PHUC' },
  { value: 'Yên Bái', LC: 'yen bai', UC: 'YEN BAI' }
]
export default defineComponent({
  props: {
    data: {
      type: Object,
      default: () => ({
        province: '',
        line1: ''
      })
    },
    type: {
      type: String,
      default: 'add'
    }
  },
  setup(props, { emit }) {
    const { state }: any = useStore()

    const padding = computed(() => state?.app?.safeArea ?? { top: 0 })

    const cityProvinceAddress: Array<IProvince> = province
    const address = reactive({
      province: props.data?.province || '',
      line1: props.data?.line1 || ''
    })
    const dataEdit = ref<any>({})
    const isValid = ref<boolean>(false)
    const form = ref()
    const resetAddress = () => {
      form.value.reset()
    }
    const saveAddress = () => {
      form.value.validate()
      if (!isValid.value) return
      switch (props.type) {
        case 'add':
          emit('saveAddress', address)
          break
        case 'edit':
          dataEdit.value = props.data
          dataEdit.value.province = address.province
          dataEdit.value.line1 = address.line1
          emit('editAddress', dataEdit)
          break
        case 'remove':
          emit('removeAddress', address)
          break
        default:
      }
    }
    const customSearch = (item, queryText, _itemText) => {
      // const i1 = item.value.indexOf(queryText);
      const i2 = item.LC.indexOf(queryText.toLowerCase())
      const i3 = item.UC.indexOf(queryText.toUpperCase())
      return i2 > -1 || i3 > -1
    }
    watchEffect(() => {
      address.province = props.data?.province || ''
      address.line1 = props.data?.line1 || ''
    })
    watch(
      () => props.data,
      () => {
        address.province = props.data?.province || ''
        address.line1 = props.data?.line1 || ''
      },
      { deep: true }
    )
    return {
      cityProvinceAddress,
      saveAddress,
      resetAddress,
      form,
      address,
      isValid,
      customSearch,
      padding
    }
  }
})
</script>
