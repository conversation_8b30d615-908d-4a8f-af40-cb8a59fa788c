<template>
  <w-dynamic-form
    :key="refresh"
    ref="dynamicForm"
    class="dynamic-form"
    :field="fields"
    :btn="[]"
    @form-submit="handleData"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  useContext,
  watch,
  computed
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

export interface IType {
  weight?: number
  height?: number
}

export default defineComponent({
  props: {
    defaultVal: {
      type: Object as PropType<IType>,
      default: () => ({ weight: 0, height: 0 } as IType)
    },
    patientId: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const dynamicForm = ref()
    const refresh = ref<number>(0)
    const isValid = ref<boolean>(false)
    const { i18n } = useContext()
    watch(
      () => props.defaultVal,
      () => {
        if (!Object.keys(props.defaultVal).length) return

        refresh.value += 1
      }
    )

    const rules = {
      general: [(v: any) => !!v || i18n.t('require field')],
      weight: [],
      height: []
    }

    const fields = computed(() => [
      {
        fieldName: 'weight',
        fieldComponent: 'w-number-input',
        fieldAttr: {
          suffix: 'kg',
          increment: 1,
          defaultVal: props.defaultVal.weight,
          range: [0, 150],
          rules: [...rules.general, ...rules.weight],
          label: i18n.t('weight')
          //   textFieldWidth: CONFIG_INPUT_NUMBER.TEXT_FIELD_WIDTH
        }
      },
      {
        fieldName: 'height',
        fieldComponent: 'w-number-input',
        fieldAttr: {
          suffix: 'cm',
          increment: 1,
          defaultVal: props.defaultVal.height,
          range: [0, 200],
          rules: [...rules.general, ...rules.height],
          label: i18n.t('height')
          //   textFieldWidth: CONFIG_INPUT_NUMBER.TEXT_FIELD_WIDTH
        }
      }
    ])

    const handleData = (obs: { weight: number; height: number }) => {
      if (!props.patientId) return // If patientId is not provided, return

      // Filter out entries with value equal to 0
      const updatedObject = Object.fromEntries(
        Object.entries(obs).filter(([_key, value]) => value !== 0)
      )

      if (!Object.keys(updatedObject).length) {
        emit('on:submit', {})
        return
      } // If no entries remaining, return

      emit('on:submit', obs)
    }

    const onSubmit = () => {
      isValid.value = dynamicForm.value?.isValid
      dynamicForm.value?.handleAction('submit')
    }

    return {
      fields,
      dynamicForm,
      refresh,
      isValid,
      onSubmit,
      handleData
    }
  }
})
</script>

<style scoped>
.dynamic-form >>> strong {
  color: rgba(0, 0, 0, 0.6) !important;
  font-weight: 500 !important;
}

.dynamic-form >>> .v-divider {
  border: none;
}
</style>
