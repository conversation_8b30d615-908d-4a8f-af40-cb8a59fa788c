<template>
  <v-dialog
    ref="datePickerMenu"
    v-model="datePickerMenu"
    transition="scale-transition"
    offset-x
    max-width="290px"
    min-width="290px"
  >
    <template #activator="{ attrs }">
      <v-text-field
        v-model="dateFormatted"
        v-mask="mask"
        :dense="dense"
        :readonly="readonly"
        :rules="rules"
        :placeholder="placeholder"
        :label="label"
        aria-autocomplete="false"
        outlined
        persistent-hint
        hint="Có thể nhâp DDMMYYYY hoặc DDMMYY"
        clearable
        autocomplete="off"
        v-bind="attrs"
        @blur="parseDate(dateFormatted)"
      >
        <template v-if="required" #label>
          {{ label }}
          <span class="red--text">*</span>
        </template>
        <template #append>
          <v-icon @click="datePickerMenu = !datePickerMenu">
            {{ appendIcon }}
          </v-icon>
        </template>
      </v-text-field>
    </template>
    <v-date-picker
      ref="datePicker"
      v-model="date"
      :max="maxDate"
      :locale="locale"
      no-title
      @input="parseDate"
    >
      <v-spacer />
      <v-btn text color="primary" @click="datePickerMenu = false">
        Cancel
      </v-btn>
      <v-btn text color="primary" @click="$refs.datePickerMenu.save(date)">
        OK
      </v-btn>
    </v-date-picker>
  </v-dialog>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Vue, Component, Emit, Prop, Watch } from 'vue-property-decorator'
/* eslint-disable @typescript-eslint/no-unused-vars */
import { mask } from 'vue-the-mask'

interface IDatePickerComponent {
  datePickerMenu: boolean
  date: string
  dateFormatted: string
  mask: string
  parseDate: Function
  addDateValue: Function
  getRandomColor: Function
}

@Component({
  directives: {
    mask
  }
})
/* eslint-disable @typescript-eslint/no-unused-vars */
export default class DatePickerComponent
  extends Vue
  implements IDatePickerComponent
{
  getRandomColor: Function
  @Prop({ type: String, default: '' })
  value: string

  @Prop({ type: String, default: 'en-us' })
  locale: string

  @Prop({ type: String, default: 'date' })
  type: string

  @Prop({ type: Array, default: () => [] })
  rules: []

  @Prop({ type: String, default: 'DD/MM/YYYY' })
  placeholder: string

  @Prop({ type: String, default: '' })
  label: string

  @Prop({ type: Boolean, default: false })
  readonly: boolean

  @Prop({ type: Boolean, default: false })
  required: boolean

  @Prop({
    type: String,
    default: ''
  })
  maxDate: string

  @Prop({ type: Boolean, default: true })
  dense

  @Prop({ type: String, default: '$calendar' })
  appendIcon

  @Watch('datePickerMenu')
  datePickerOnChange(val) {
    if (this.type === 'dob') {
      val && setTimeout(() => (this.datePicker.activePicker = 'YEAR'))
    }
  }

  @Emit('input')
  emitInput(value) {
    return value
  }

  @Watch('value', { immediate: true })
  onValueChange(newVal) {
    if (newVal) {
      this.parseDate(newVal)
    }
  }

  get datePicker(): any {
    return this.$refs.datePicker
  }

  datePickerMenu = false
  date: string = new Date().toISOString().substr(0, 10)
  dateFormatted = ''
  mask = '##/##/####'

  parseDate(date) {
    if (date) {
      if (this.$dayjs(date, 'DD/MM/YY', true).isValid()) {
        const dateFormat = this.$dayjs(date, 'DD/MM/YY', true)
        this.addDateValue(dateFormat)
      } else if (this.$dayjs(date, 'DD/MM/YYYY', true).isValid()) {
        const dateFormat = this.$dayjs(date, 'DD/MM/YYYY', true)
        this.addDateValue(dateFormat)
      } else if (this.$dayjs(date, 'YYYY-MM-DD', true).isValid()) {
        const dateFormat = this.$dayjs(date, 'YYYY-MM-DD', true)
        this.addDateValue(dateFormat)
      } else {
        const dateFormat = this.$dayjs(date)
        this.addDateValue(dateFormat)
      }
    }
  }

  addDateValue(date: any) {
    this.dateFormatted = date.format('DD/MM/YYYY')
    this.date = date.format().substring(0, 10)
    this.emitInput(date.format())
  }
}
</script>
