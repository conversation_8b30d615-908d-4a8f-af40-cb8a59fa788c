<template>
  <!-- annual service fee -->
  <div class="mx-2">
    <v-card flat rounded="lg" class="d-flex text-18 pa-3">
      <div>
        <div class="d-block font-weight-bold">
          <v-icon size="20" class="mb-1">$crown</v-icon>
          {{ $t('membership') }}
        </div>
        <div class="text-14">
          <span
            v-dompurify-html="
              $t(
                'Membership (all-year waiver of the service fee and other benefits)'
              )
            "
            class="text-14 mr-1"
          ></span>
          <v-dialog v-model="dialogBenefit" width="500">
            <template #activator="{ on, attrs }">
              <v-btn fab width="22" height="22" text v-bind="attrs" v-on="on">
                <v-icon size="16"> $informationOutline</v-icon>
              </v-btn>
            </template>
            <span>
              {{ $t('') }}
            </span>
            <v-card>
              <v-toolbar color="primary" dark style="text-transform: uppercase">
                {{ $t('membership') }}
              </v-toolbar>

              <ul
                v-dompurify-html="
                  $t(
                    'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care'
                  )
                "
                style="color: black; line-height: 32px; font-size: 15px"
                class="pt-4 pl-8"
              ></ul>
              <v-card-actions class="justify-end">
                <v-btn color="black" text @click="dialogBenefit = false">
                  {{ $t('close') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </div>
      </div>
      <v-spacer></v-spacer>
      <div class="d-flex flex-column align-end">
        <div class="font-weight-bold">
          {{ $n(250000, 'currency') }}
        </div>
        <v-btn
          fab
          small
          class="pa-0"
          depressed
          style="letter-spacing: normal"
          @click="$router.push('/checkout/all-year-member')"
        >
          <v-icon size="20">$chevronRight</v-icon>
        </v-btn>
      </div>
    </v-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'
export default defineComponent({
  setup() {
    const dialogBenefit = ref(false)
    return {
      dialogBenefit
    }
  }
})
</script>
