<script lang="ts">
import { defineComponent, useContext } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import { BenefitOption } from '../../../../../models'
import StatusTag from '../../../forms/choose-membership/status-tag.vue'

export default defineComponent({
  name: 'AddonsCheckout',
  components: { StatusTag },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    benefitOption: {
      type: Array as PropType<BenefitOption[]>,
      required: true
    },
    updateSelectedCheckboxes: {
      type: Function as PropType<(slug: string) => void>,
      required: true
    }
  },
  setup() {
    const { i18n }: any = useContext()
    const getState = (state: string): boolean => {
      return state !== 'cancelled' || false
    }
    return { i18n, getState }
  }
})
</script>

<template>
  <v-col cols="12" class="mt-0 px-3">
    <div class="px-1">
      <h3 class="headline text--secondary title-dark font-weight-bold">
        {{ i18n.t('additional benefits') }}
      </h3>
      <!-- Loop through benefit options -->
      <v-skeleton-loader
        v-if="loading"
        type="list-item-two-line, image"
        class="mx-auto"
      ></v-skeleton-loader>
      <div
        v-for="(option, index) in benefitOption"
        v-else
        :key="`${option._id}${index}`"
        :style="{ marginTop: option.isDisabled ? '15px' : '0' }"
      >
        <StatusTag v-if="option.isDisabled" content="coming soon" />
        <div
          class="py-1 benefit-options d-flex justify-space-between align-center"
        >
          <v-checkbox
            ref="checkboxAddons"
            :input-value="getState(option.state)"
            :disabled="option.isDisabled"
            :style="{
              width: '100%',
              opacity: option.isDisabled ? '0.6' : '1'
            }"
            :messages="[i18n.t(option.description)]"
            @change="updateSelectedCheckboxes(option.slug)"
          >
            >
            <!-- Customize the label of the checkbox -->
            <template #label>
              <div
                style="width: 100%"
                class="d-flex justify-space-between grey--text text--darken-4 w-100 font-weight-bold"
              >
                <div class="d-flex flex-column justify-center">
                  {{ i18n.t(option.title) }}
                </div>
                <div class="primary--text">
                  {{
                    `${i18n.n(option.price, {
                      style: 'currency',
                      currency: 'VND'
                    })}/${i18n.t('year')}`
                  }}
                </div>
              </div>
            </template>

            <!-- Use the #message slot -->
            <template #message="{ key, message }">
              <span :key="key" v-dompurify-html="message" class="text-14">
              </span>
            </template>
          </v-checkbox>
        </div>
      </div>
    </div>
  </v-col>
</template>

<style scoped>
.benefit-options >>> .v-messages__message {
  font-size: 15px !important;
  line-height: 150% !important;
  color: #4b5563;
}

.benefit-options >>> .v-input--selection-controls {
  margin-top: 5px !important;
}

.benefit-options >>> .v-input__slot {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0px !important;
}

.benefit-options >>> .v-messages {
  margin-left: 26px;
}
</style>
