<template>
  <v-card
    v-if="
      checkoutConfig.vat.show ||
      (voucherCondition && !paymentConfig.voucher.autoApply)
    "
    elevation="0"
    rounded="lg"
    width="97%"
    class="mt-2 pb-2 mx-auto"
    style="box-shadow: 0px 2px 6px -2px rgba(79, 79, 79, 0.2); border: none"
  >
    <v-card-title class="text-20 font-weight-bold">{{
      $t('Voucher')
    }}</v-card-title>
    <e-invoice v-if="checkoutConfig.vat.show" />
    <voucher v-if="voucherCondition" :order="order" />
  </v-card>
</template>
<script>
import { defineComponent, useContext, computed } from '@nuxtjs/composition-api'
import EInvoice from '../e-invoice.vue'
import Voucher from '../voucher.vue'
export default defineComponent({
  components: {
    EInvoice,
    Voucher
  },
  props: {
    order: {
      type: Object,
      default: () => {},
      required: true
    },
    orderItems: {
      type: Array,
      default: () => {},
      required: true
    }
  },
  setup(props) {
    const { $config } = useContext()
    const paymentConfig = computed(() => $config.checkout.payment)
    const checkoutConfig = computed(() => $config.checkout)
    const voucherCondition = computed(
      () =>
        paymentConfig?.value?.voucher?.allow &&
        !props.order.voucher &&
        // !(props.order.promotions && props.order.promotions.length > 0) &&
        props.orderItems[0]?.meta?.applyVoucher !== false
    )
    return { paymentConfig, checkoutConfig, voucherCondition }
  }
})
</script>
