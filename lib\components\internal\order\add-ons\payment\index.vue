<script lang="ts">
import {
  defineComponent,
  watch,
  ref,
  useContext
} from '@nuxtjs/composition-api'
import getProductInfo from '../../../../../composables/get-product-info'
import { useSearchRelatedProducts } from '../../../../../composables/use-search-related-products'
export default defineComponent({
  name: 'AddonsPayment',
  setup(_, { emit }) {
    const { i18n }: any = useContext()
    const { isMember, isMembershipProduct, typeProduct } = getProductInfo()
    const { execute: searchRelatedProducts, data } = useSearchRelatedProducts([
      `payment-${isMember.value ? 'member' : 'non-member'}-${
        typeProduct.value
      }`,
      `payment-${isMember.value ? 'member' : 'non-member'}`,
      `payment-${typeProduct.value}`
    ])
    searchRelatedProducts()
    const indexValue = ref<number>()

    watch([data, indexValue], (newValue) => {
      emit('on:indexValue', { newValue })
    })

    return {
      i18n,
      data,
      indexValue,
      isMembershipProduct
    }
  }
})
</script>

<template>
  <v-card
    v-show="!isMembershipProduct && data.length > 0"
    class="px-3 py-3 my-3 mx-auto rounded-lg"
    width="97%"
    style="box-shadow: 0px 2px 6px -2px rgba(79, 79, 79, 0.2); border: none"
  >
    <p class="my-2 mx-auto text-17 text-center font-weight-bold">
      {{ i18n.t('Bundle & Save') }} 🔥
    </p>
    <v-list class="choose-option px-0 py-2">
      <v-list-item-group v-model="indexValue" color="primary">
        <v-list-item
          v-for="(item, i) in data"
          :key="i"
          class="rounded-lg pa-3 px-2 my-2"
          @click="$emit('on-call-product')"
        >
          <template #default>
            <div class="d-flex justify-space-between" style="width: 100%">
              <div class="d-flex">
                <div>
                  <div
                    class="font-weight-bold d-flex algin-center"
                    style="line-height: 20px"
                  >
                    <v-icon size="20">
                      {{ indexValue === i ? '$checkCircle' : '$circleOutline' }}
                    </v-icon>
                    <span class="ml-2">{{ i18n.t(item.title) }}</span>
                  </div>
                  <div
                    v-dompurify-html="i18n.t(item.description)"
                    class="text-14 mt-1"
                  ></div>
                </div>
              </div>
              <div class="text-right font-weight-bold">
                {{ i18n.n(item.price, 'currency') }}
              </div>
            </div>
          </template>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </v-card>
</template>
<style scoped>
.choose-option >>> .v-list-item--active:hover::before,
.choose-option >>> .v-list-item--active::before {
  opacity: 0;
}

.choose-option >>> .v-item-group.v-list-item-group {
  gap: 15px !important;
  display: flex;
  flex-direction: column;
}
.choose-option >>> .v-item--active.v-list-item--active {
  border: solid 1px #009688;
  background: #0096881a !important;
  box-shadow: #08a09123 0px 7px 29px 0px;
}
.choose-option
  >>> .v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
  border: solid 1px #e0e0e079;
}
</style>
