<template>
  <v-card class="mt-3 pa-3 mx-2 elevation-0 d-flex align-start" rounded="lg">
    <div class="d-flex align-center">
      <div class="ml-1">
        <div class="d-block font-weight-bold">
          <v-icon size="20">$lightningBolt</v-icon>
          {{ $t('asynchronous telehealth package') }}
        </div>
        <span
          v-dompurify-html="$t('Membership + 10 question combo')"
          style="font-size: 14px"
        ></span>
        <v-dialog v-model="dialogBenefit" width="500">
          <template #activator="{ on, attrs }">
            <v-btn fab width="22" height="22" text v-bind="attrs" v-on="on">
              <v-icon size="16"> $informationOutline</v-icon>
            </v-btn>
          </template>

          <v-card>
            <v-toolbar color="primary" dark style="text-transform: uppercase">
              {{ $t('membership') }}
            </v-toolbar>

            <ul
              v-dompurify-html="
                $t(
                  'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care'
                )
              "
              style="color: black; line-height: 32px; font-size: 15px"
              class="pt-4 pl-8"
            ></ul>
            <v-card-actions class="justify-end">
              <v-btn color="black" text @click="dialogBenefit = false">
                {{ $t('close') }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </div>
    </div>
    <v-spacer></v-spacer>
    <v-spacer></v-spacer>
    <div class="d-flex flex-column align-end">
      <div class="font-weight-bold">
        {{ $n(700000, 'currency') }}
      </div>
      <v-btn
        class="pa-0"
        fab
        small
        depressed
        style="letter-spacing: normal"
        @click="handleUpdateCombo"
      >
        <v-icon size="20">$chevronRight</v-icon>
      </v-btn>
    </div>
  </v-card>
</template>
<script lang="ts">
import { defineComponent, useRouter, ref } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    isMember: {
      type: Boolean,
      default: false
    },
    isMembershipProduct: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const router = useRouter()
    const dialogBenefit = ref(false)
    const handleUpdateCombo = () => {
      router.push('/checkout/all-year-combo-10-questions')
    }
    return {
      handleUpdateCombo,
      dialogBenefit
    }
  }
})
</script>
