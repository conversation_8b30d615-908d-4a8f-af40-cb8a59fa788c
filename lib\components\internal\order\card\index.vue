<template>
  <v-card
    class="member-card rounded-lg mx-auto white--text"
    width="305"
    height="170"
    :class="memberService.title || 'wellcare-prepaid'"
    :style="backgroundStyle"
    @click="$emit('chooseCard')"
  >
    <v-card-title
      :class="{
        'red--text text--darken-1':
          memberService.title === 'conceirge' ||
          memberService.title === 'premiere'
      }"
      class="font-weight-bold text-18 py-1"
      ><div
        class="text-left text-uppercase d-flex align-center justify-space-between"
        style="width: 100%"
      >
        <span>{{ $t(memberService.title) || $t('Wellcare Card') }}</span>
        <v-img
          v-show="memberService.wellcareLogo"
          max-width="50"
          max-height="50"
          :src="require('../../../../static/icons/logo.png')"
        ></v-img></div
    ></v-card-title>
    <div
      style="width: 100%; height: 100%; position: absolute; top: 0; left: 0"
      class="d-flex align-center justify-end px-5"
    >
      <div v-if="activate" class="text-26 font-weight-bold">
        <span>{{ $n(memberService.usable, 'currency') }}</span>
        <!--
        <span v-else>
          {{ $n(12 * memberService.pricePerMonth, 'currency') }}
        </span>
        -->
      </div>
      <div v-else class="text-26 font-weight-bold">
        <span>{{ $t('unactivated') }}</span>
      </div>
    </div>
    <v-footer
      absolute
      class="transparent py-0"
      :class="{ unactivate: !activate }"
    >
      <div
        class="d-flex align-center justify-space-between white--text text-uppercase"
        style="width: 100%"
      >
        <div class="text-20">
          {{ $store.state.authen.user.name }}
        </div>
        <div
          v-if="type !== 'cash' && activate"
          class="d-flex flex-column justify-center"
        >
          <span class="text-14"><small>EXP</small></span>
          <span v-if="memberService.exp" class="text-20">{{
            memberService.exp
          }}</span>
          <span v-else class="text-20"
            >{{
              new Date().getMonth() + 1 > 10
                ? new Date().getMonth() + 1
                : '0' + (new Date().getMonth() + 1)
            }}
            / {{ `${new Date().getFullYear() + 1}`.substring(2) }}</span
          >
        </div>
        <v-img
          v-if="memberService.image"
          max-width="50"
          max-height="50"
          :src="memberService.image"
        ></v-img>
        <v-icon v-else color="white" size="50">{{ memberService.icon }}</v-icon>
      </div>
    </v-footer>
  </v-card>
</template>
<script lang="ts">
import { computed, defineComponent } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    memberService: {
      type: Object,
      default: () => {}
    },
    activate: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const type = computed(() => props.memberService.type)
    const backgroundStyle = computed(
      () =>
        `background: ${props.memberService.color[0]} !important;
        background: -webkit-linear-gradient(to right, ${props.memberService.color[1]}, ${props.memberService.color[0]}) !important;
        background: linear-gradient(to right, ${props.memberService.color[1]}, ${props.memberService.color[0]}) !important;`
    )
    return { type, backgroundStyle }
  }
})
</script>
<style scoped>
.total {
  position: absolute;
  bottom: 10px;
  width: 100%;
}
.unactivate {
  opacity: 0.5 !important;
}
</style>
