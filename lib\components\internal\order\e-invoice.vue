<template>
  <div>
    <v-checkbox
      v-model="eInvoice"
      color="primary"
      class="mt-1 px-5"
      hide-details="auto"
    >
      <template #label
        ><span
          :class="{
            'primary--text': eInvoice,
            'grey--text text--darken-3': !eInvoice
          }"
          class="font-weight-bold"
          >{{ $t('export e-invoice') }}</span
        ></template
      ></v-checkbox
    >
    <v-expand-transition>
      <v-col v-show="eInvoice" cols="12">
        <v-card-text class="pa-0">
          <v-form>
            <v-text-field
              v-model="state.email"
              :error-messages="v$.email.$errors.map((i) => $t(i.$message))"
              dense
              :label="$t('email')"
              outlined
              @blur="v$.email.$touch()"
            ></v-text-field>
            <v-text-field
              v-model="state.code"
              :error-messages="v$.code.$errors.map((i) => $t(i.$message))"
              dense
              :label="$t('tax code')"
              outlined
              @blur="v$.code.$touch()"
            ></v-text-field>
            <v-text-field
              v-model="state.company"
              :error-messages="v$.company.$errors.map((i) => $t(i.$message))"
              dense
              :label="$t('company name')"
              outlined
              @blur="v$.company.$touch()"
            ></v-text-field>
            <v-textarea
              v-model="state.address"
              dense
              :error-messages="v$.address.$errors.map((i) => $t(i.$message))"
              :label="$t('address')"
              outlined
              auto-grow
              rows="3"
              @blur="v$.address.$touch()"
            ></v-textarea>
          </v-form>
          <span class="text--secondary text-16"
            >* {{ $t('not unless you input the proper information') }}</span
          >
        </v-card-text>
      </v-col>
    </v-expand-transition>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'
import { useVuelidate } from '@vuelidate/core'
import { required, email, minLength } from '@vuelidate/validators'
export default defineComponent({
  setup() {
    const validated = ref(false)
    const state = ref({
      email: '',
      code: '',
      company: '',
      address: ''
    })
    const rules = {
      email: { email, required },
      code: { minLength: minLength(10), required },
      company: { required },
      address: { required }
    }
    const v$ = useVuelidate(rules, state)
    const eInvoice = ref(false)
    const submit = () => {
      v$.value.$validate().then((result: boolean) => {
        validated.value = result
      })
    }
    return { eInvoice, state, rules, v$, submit, validated }
  }
})
</script>
