<template>
  <div>
    <h3 class="px-4 pb-0 pt-4 text-center text-20">{{ $t('download app') }}</h3>
    <div class="pa-4">
      <div
        class="d-flex flex-wrap align-center justify-center"
        style="gap: 15px"
      >
        <v-img
          src="/checkout/black.svg"
          contain
          height="50px"
          max-width="150px"
          class="logo"
          @click="appStoreHref.click()"
        ></v-img>
        <v-img
          src="/checkout/google-play-badge.png"
          cover
          height="49px"
          max-width="150px"
          class="logo rounded-lg"
          @click="googlePlayHref.click()"
        ></v-img>
      </div>
    </div>
    <a
      ref="googlePlayHref"
      href="https://play.google.com/store/apps/details?id=vn.wellcare"
      target="_blank"
      class="hidden"
    ></a>
    <a
      ref="appStoreHref"
      href="https://apps.apple.com/us/app/wellcare/id1039423586?ls=1"
      class="hidden"
      target="_blank"
    ></a>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'

export default defineComponent({
  setup() {
    const googlePlayHref = ref(null)
    const appStoreHref = ref(null)
    return { appStoreHref, googlePlayHref }
  }
})
</script>

<style scoped>
.logo {
  cursor: pointer;
  opacity: 0.9;
  transition: 0.2s linear;
}
.logo:hover {
  opacity: 1;
}
.hidden {
  position: absolute;
  top: 99999px;
  opacity: 0;
}
</style>
