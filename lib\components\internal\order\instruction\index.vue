<template>
  <v-card outlined width="100%" color="transparent">
    <v-card-title
      v-if="!isMembershipProduct"
      style="font-size: 18px"
      class="py-0"
    >
      <div class="font-weight-bold mx-auto">
        {{ $t('download app') }}
      </div>
      <div class="mx-auto text-center" style="word-break: break-word">
        {{
          isShortQuestion
            ? $t(`to receive the physician's response`)
            : $t('to make a voice or video call to the physician')
        }}
      </div>
    </v-card-title>

    <client-only>
      <v-card-actions class="mb-10 py-0 mt-4">
        <v-btn
          class="rounded ml-auto"
          large
          width="45%"
          style="border-color: silver"
          depressed
          outlined
          @click="openConsultationUrl"
          ><strong
            ><small>{{ $t('skip') }}</small></strong
          >
        </v-btn>
        <template v-if="isMembershipProduct">
          <v-btn
            class="rounded mr-auto"
            large
            color="primary"
            width="45%"
            depressed
            @click="directToMembership"
            ><strong
              ><small>{{ $t('continue') }}</small></strong
            >
          </v-btn>
        </template>
        <template v-else>
          <v-btn
            class="rounded mr-auto"
            width="45%"
            large
            color="primary"
            depressed
            @click="onInstallButtonClick"
            ><strong
              ><small>{{ $t('install') }}</small></strong
            >
          </v-btn>
        </template>
      </v-card-actions>
    </client-only>

    <v-card-text
      v-if="!isShortQuestion"
      v-dompurify-html="$t('video call payment instruction')"
      class="black--text transparent instruction px-9"
    />

    <template v-if="!$vuetify.breakpoint.xsOnly">
      <v-dialog v-model="downloadModal" max-width="500">
        <download />
      </v-dialog>
    </template>
    <template v-else>
      <v-bottom-sheet v-model="downloadModal">
        <download />
      </v-bottom-sheet>
    </template>
  </v-card>
</template>
<script lang="ts">
import {
  defineComponent,
  useContext,
  useRouter,
  ref,
  computed,
  useRoute
} from '@nuxtjs/composition-api'
import Download from './download.vue'
export default defineComponent({
  components: { Download },
  props: {
    consultationId: {
      type: [String, Number],
      default: ''
    },
    isShortQuestion: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    // https://play.google.com/store/apps/details?id=vn.wellcare
    // https://apps.apple.com/us/app/wellcare/id1039423586?ls=1
    const DOWNLOAD_LINK = {
      ios: 'https://apps.apple.com/us/app/wellcare/id1039423586?ls=1',
      android: 'https://play.google.com/store/apps/details?id=vn.wellcare'
    }
    const router = useRouter()
    const route = useRoute()
    const { $config, app } = useContext()
    const isIOS = computed(() => app?.$ua?.isFromIos())
    const isFromMobile = computed(
      () => app?.$ua?.isFromMobilephone() || app?.$ua?.isFromSmartphone()
    )

    const isMembershipProduct = computed(
      () => route.value.query.product === 'membership'
    )

    const directToMembership = () => {
      console.log('You have successfully navigated !')
    }

    const consultationLink = computed(
      () =>
        `${$config.checkout.payment.consultationLink}/${props.consultationId}`
    )
    const downloadModal = ref(false)
    const openConsultationUrl = () => {
      if (props.consultationId && process.client)
        window.open(consultationLink.value, '_blank')
      else router.push('/')
    }
    const onInstallButtonClick = () => {
      if (isFromMobile.value && process.client) {
        const downloadLink = isIOS.value
          ? DOWNLOAD_LINK.ios
          : DOWNLOAD_LINK.android
        window.open(downloadLink, '_blank')
      } else downloadModal.value = true
    }
    return {
      consultationLink,
      openConsultationUrl,
      downloadModal,
      isIOS,
      isFromMobile,
      onInstallButtonClick,
      isMembershipProduct,
      directToMembership
    }
  }
})
</script>
<style scoped>
.instruction >>> li {
  padding-bottom: 8px;
}
</style>
