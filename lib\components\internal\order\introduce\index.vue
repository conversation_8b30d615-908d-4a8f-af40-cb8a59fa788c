<template>
  <v-card
    class="pa-2 my-3 rounded-lg mx-auto"
    :class="memberService.background"
    width="97%"
    outlined
  >
    <div
      class="text-20 d-flex font-weight-bold align-center px-2"
      style="gap: 10px"
    >
      <v-img :src="memberService.icon" max-width="20" max-height="20"></v-img
      ><span class="text-capitalize">{{ $t(memberService.title) }}</span>
    </div>
    <div class="benefits d-flex flex-column px-4 text-16">
      <div v-for="benefit in memberService.benefits" :key="benefit.text">
        <v-icon large :color="memberService.mainColor">$circle-small</v-icon
        ><span>{{ $t(benefit.text) }}</span>
      </div>
    </div>

    <!--
    <v-btn
      width="200"
      class="text-capitalize rounded-pill white--text ma-4"
      :class="memberService.buttonClass"
      @click="memberService.action"
      ><span>{{
        $t('just') + ' ' + $n(memberService.pricePerMonth) + '/'
      }}</span
      ><span class="text-lowercase">{{ $t('month') }}</span></v-btn
    >
    -->
  </v-card>
</template>
<script>
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    memberService: {
      type: Object,
      default: () => {}
    }
  },
  setup() {
    return {}
  }
})
</script>
