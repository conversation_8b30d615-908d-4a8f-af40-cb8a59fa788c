<template>
  <v-card
    elevation="0"
    class="px-3 py-2 pt-3 my-3 rounded-lg"
    style="background: #f9f9f9db; position: relative"
  >
    <v-btn
      v-if="removeable"
      icon
      small
      fab
      class="rounded-lg"
      style="top: -4px; right: -4px; position: absolute"
      @click="dialogConfirmDelete = true"
      ><v-icon size="18">$close</v-icon>
    </v-btn>
    <v-expand-x-transition>
      <div class="order-item">
        <div
          class="order-item d-flex align-center justify-space-between"
          :style="{
            marginTop: removeable ? '12px' : '',
            gap: '4px'
          }"
        >
          <div class="d-flex align-center" style="gap: 10px; flex: 8">
            <w-avatar
              v-if="orderItemAvatar !== ''"
              size="50"
              :name="orderItem.title"
              :user-id="orderItem._id"
              :src="orderItemAvatar"
              :style="{
                'background-color': '#fff !important',
                border: '1px solid rgb(76 230 221)'
              }"
            />
            <div class="font-weight-bold" style="font-size: 15px">
              <div class="d-flex align-start">
                <span v-show="isAddons" class="primary--text mr-1">✓</span>
                <span class="pr-1">{{ i18n.t(getTitle(orderItem)) }}</span>
              </div>
            </div>
          </div>
          <div v-if="showFee" class="text-right">
            <div v-if="orderItem.note && orderItem.customPrice">
              <StatusTag :content="orderItem.note"></StatusTag>
            </div>
            <div
              v-else-if="getSubTotal(orderItem) !== 0"
              class="primary--text font-weight-bold text-center"
              style="font-size: 15px; flex: 3"
            >
              {{ i18n.n(getSubTotal(orderItem), 'currency') }}
            </div>
          </div>
        </div>
        <div :class="['mt-2', { 'ml-5': isAddons }]">
          <span
            v-dompurify-html="
              i18n.t(getDescription(orderItem)) + ' ' + getCreatedAt(orderItem)
            "
            class="text-14"
          />
          <v-dialog
            v-if="typeName === 'consultation-v2'"
            transition="dialog-bottom-transition"
            max-width="600"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-if="guide"
                color="primary"
                class="mb-2"
                fab
                width="22"
                height="22"
                text
                v-bind="attrs"
                v-on="on"
              >
                <v-icon color="primary" size="16"> $informationOutline</v-icon>
              </v-btn>
            </template>
            <template #default="dialog">
              <v-card>
                <v-toolbar color="primary" dark>
                  {{ i18n.t('detailed instructions') }}
                </v-toolbar>
                <v-card-text>
                  <div
                    v-dompurify-html="i18n.t(detail)"
                    style="color: black"
                    class="pt-5"
                  ></div>
                </v-card-text>
                <v-card-actions class="justify-end">
                  <v-btn text color="black" @click="dialog.value = false">
                    {{ i18n.t('close') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </template>
          </v-dialog>
          <v-dialog v-model="dialogBenefit" width="500">
            <template #activator="{ on, attrs }">
              <v-btn
                v-show="
                  checkBenefitExist(orderItem) && getMoreDetail(orderItem)
                "
                class="mb-2"
                fab
                width="22"
                height="22"
                text
                v-bind="attrs"
                v-on="on"
              >
                <v-icon color="primary" size="16"> $informationOutline</v-icon>
              </v-btn>
            </template>
            <v-card>
              <v-toolbar color="primary" dark style="text-transform: uppercase">
                {{ i18n.t('membership') }}
              </v-toolbar>
              <div
                v-dompurify-html="i18n.t(getMoreDetail(orderItem))"
                style="color: black; line-height: 32px; font-size: 15px"
                class="pt-4 pa-3"
              ></div>
              <v-card-actions class="justify-end">
                <v-btn color="black" text @click="dialogBenefit = false">
                  {{ i18n.t('close') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </div>
        <v-card-text v-if="guide" class="px-0 d-flex flex-column">
          <span v-dompurify-html="i18n.t(guide)" style="color: black" />
        </v-card-text>
      </div>
    </v-expand-x-transition>
    <confirm-delete
      v-model="dialogConfirmDelete"
      :loading="loadingRemove"
      @cancel="dialogConfirmDelete = false"
      @executeDelete="onRemoveOrderItem"
    />
  </v-card>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  useContext,
  onMounted
} from '@nuxtjs/composition-api'
import { useMappingProduct } from '../../../../composables/index'
import ConfirmDelete from '../../forms/choose-patient/modal/comfirm-delete.vue'
import useRemoveOrderItem from '../../../../repositories/order-item/remove-order-item'
import { useRemoveRules } from '../../../../repositories/order-item/use-remove-rules'
import StatusTag from '../../forms/choose-membership/status-tag.vue'
// import { usePaymentOrder } from '../../../../repositories'
import { useProductGuides } from './use-product-guides'

export default defineComponent({
  components: {
    ConfirmDelete,
    StatusTag
  },
  props: {
    orders: {
      type: Array,
      default: () => []
    },
    orderItem: {
      type: Object,
      default: () => {}
    },
    order: {
      type: Object,
      default: () => {}
    },
    singleOrder: {
      type: Boolean,
      default: false
    },
    typeName: {
      type: String,
      default: ''
    }
  },
  setup(props: any) {
    const { $config, i18n } = useContext()
    const { guide, detail } = useProductGuides(
      props.orderItem?.type?.name || '',
      props.orderItem?.type?.name?.replace(/-v2$/, '-detail') || ''
    )

    const { removeable } = useRemoveRules(
      computed(() => props.orderItem),
      computed(() => props.orders)
    )

    const { getBenefitName, checkBenefitExist } = useMappingProduct()

    const {
      execute: executeRemove,
      loading: loadingRemove,
      // onSuccess,
      onError
    } = useRemoveOrderItem(
      computed<string>(() => props.orderItem._id),
      computed<string>(() => props.order._id)
    )

    const isAddons = computed<boolean>(() =>
      [
        'membership',
        'health-program-baby-development',
        'membership-ask-doctor',
        'membership-personal-doctor'
        // 'personal-doctor-addon'
      ].includes(props.orderItem?.product?.sku)
    )

    const dialogBenefit = ref(false)
    const dialogConfirmDelete = ref(false)

    const showFee = computed(() => $config?.checkout?.payment?.showFee)

    onError(() => {
      dialogConfirmDelete.value = false
    })

    const orderItemAvatar = computed<string>(
      () => props.orderItem?.image?.url ?? ''
    )

    const getCreatedAt = (orderItem: any) => {
      return orderItem?.meta?.createdAt || orderItem?.createdAt || ''
    }

    const getTitle = (orderItem: any) => {
      if (checkBenefitExist(orderItem)) {
        return getBenefitName(orderItem?.product?.slug)?.title
      } else {
        return orderItem.title || ''
      }
    }

    const getDescription = (orderItem: any) => {
      if (checkBenefitExist(orderItem)) {
        return getBenefitName(orderItem?.product?.slug)?.description || ''
      } else {
        return orderItem.description
      }
    }

    const getMoreDetail = (orderItem: any) => {
      if (checkBenefitExist(orderItem)) {
        return getBenefitName(orderItem?.product?.slug)?.moreDetail || ''
      }
    }

    const getSubTotal = (orderItem: any) => {
      if (checkBenefitExist(orderItem)) {
        return getBenefitName(orderItem?.product?.slug)?.price || ''
      } else {
        return orderItem.subTotal
      }
    }

    const onRemoveOrderItem = () => {
      executeRemove()
      setTimeout(() => {
        dialogConfirmDelete.value = false
      }, 1500)
    }

    onMounted(() => {
      const button = document.getElementById('btn-info')

      if (button) {
        button.addEventListener('click', function () {
          window.open('https://khamtuxa.vn/dich-vu/chinh-sach-phi', '_blank')
        })
      }
    })

    return {
      i18n,
      guide,
      detail,
      // getAvatar,
      orderItemAvatar,
      getCreatedAt,
      getBenefitName,
      getTitle,
      getSubTotal,
      getDescription,
      getMoreDetail,
      checkBenefitExist,
      dialogBenefit,
      dialogConfirmDelete,
      executeRemove,
      loadingRemove,
      removeable,
      showFee,
      onRemoveOrderItem,
      isAddons
    }
  }
})
</script>

<style scoped>
.v-card__text {
  padding-bottom: 0 !important;
}
.v-btn span {
  text-transform: lowercase;
}
.v-btn span:first-letter {
  text-transform: uppercase;
}
.order-item >>> .v-avatar {
  background-color: transparent !important;
}

.order-item >>> .personal-doctor-charge {
  display: none !important;
}
</style>
