const productGuides = {
  'consultation-v2':
    'after payment done: add images & videos (if any) from our mobile app call ontime after consultation done: note and suggested prescription (if any) will be shown upon the completion notification one free follow-up question within 24 hours note: lacking of necessary medical information may lead to a cancellation no cash refund re-schedule once, within 30 days',
  'consultation-detail':
    'after payment is done, please: check your for our confirmation sent through sms or on the wellcare mobile app, there will be your e-medical record upload images and videos from the link in that confirmation notification or on our wellcare mobile app asap, our doctor will check it out touch the call button on your e-medical record on time. see the note and suggested prescription after receiving the completion confirmation send a follow-up question to the doctor as needed, within 24 hours. please note: we may cancel the appointment unless you provide all the necessary medical information as requested.',
  consultation:
    'after payment is done we will send you a confirmation through sms and the wellcare mobile app please open the e medical record link in that sms or the wellcare mobile app to as soon as possible provide more detailed health information connect with the doctor on time there is a call button see the note and suggested prescription after receiving the completion confirmation send a follow up question to doctor as needed within 24 hours',
  'consultation-question-provider':
    'check your sms or the mobile app for the comfirmation you will receive the response within 24 hours please again check your sms or the mobile app',
  'consultation-question':
    'check your sms or the mobile app for the comfirmation you will receive the response within 24 hours please again check your sms or the mobile app'
}

export function useProductGuides(type1: string, type2: string = '') {
  const guide = productGuides[type1]
  const detail = productGuides[type2]
  return { guide, detail }
}
