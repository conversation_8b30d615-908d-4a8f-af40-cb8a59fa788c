<script lang="ts">
import { computed, defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'Bundles',
  props: {
    data: {
      type: Array,
      required: true
    },
    model: {
      type: Number,
      default: null
    }
  },
  emits: ['update:model', 'select-bundle'],
  setup(props, { emit }) {
    const localModel = computed({
      get: () => props.model,
      set: (value) => emit('update:model', value)
    })

    const selectBundle = (index: number) => {
      localModel.value = index
      emit('select-bundle', index)
    }

    return {
      localModel,
      selectBundle
    }
  }
})
</script>

<template>
  <v-card class="px-3 py-3 mx-auto rounded-lg" width="97%" elevation="0">
    <p v-once class="my-2 mx-auto text-18 text-center font-weight-bold">
      {{ $t('Bundle & Save') }} 🔥
    </p>
    <v-list class="choose-option px-0 py-2">
      <v-list-item-group v-model="localModel" color="primary">
        <v-list-item
          v-for="(item, i) in data"
          :key="i"
          class="rounded-lg pa-3 px-2 my-2"
          :style="{
            border: '1px solid #ebebeb'
          }"
          @click="selectBundle(i)"
        >
          <template #default>
            <div class="d-flex justify-space-between" style="width: 100%">
              <div class="d-flex">
                <div>
                  <div class="font-weight-bold" style="line-height: 20px">
                    {{ $t(item.title) }}
                  </div>
                  <div
                    v-dompurify-html="$t(item.description)"
                    class="text-14 mt-1"
                  />
                </div>
              </div>
              <div class="text-right font-weight-bold">
                {{ $n(item.price, 'currency') }}
              </div>
            </div>
          </template>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </v-card>
</template>

<style scoped>
.choose-option :deep(.v-list-item--active:hover::before),
.choose-option :deep(.v-list-item--active::before) {
  opacity: 0;
}

.choose-option :deep(.v-item-group.v-list-item-group) {
  gap: 15px !important;
  display: flex;
  flex-direction: column;
}

.choose-option :deep(.v-item--active.v-list-item--active) {
  border: solid 1px #009688;
  background: #0096881a !important;
  box-shadow: #08a09123 0px 7px 29px 0px;
}

.choose-option
  :deep(.v-list-item:not(.v-list-item--active):not(.v-list-item--disabled)) {
  border: solid 1px #e0e0e0c7;
}
</style>
