<script lang="ts">
import {
  defineComponent,
  computed,
  useContext,
  ref,
  useStore
} from '@nuxtjs/composition-api'
import WalletSlider from '../../wallet/wallet-slider.vue'
import { getProductInfo } from '../../../../composables'
// import AddOns from '../add-ons/index.vue'
export default defineComponent({
  components: {
    WalletSlider
    //  AddOns
  },
  props: {
    wallets: {
      type: Array,
      default: () => []
    },
    order: {
      type: Object,
      default: () => {}
    },
    orderItems: {
      type: Array,
      default: () => []
    },
    isMembershipProduct: {
      type: Boolean,
      default: false
    },
    statusCode: {
      type: Number,
      default: 0
    },
    allowPayment: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { $config } = useContext()
    const { state }: any = useStore()
    const isShowTooltip = ref(false)
    const { skuProduct } = getProductInfo()
    const isLoading = ref(false)
    const isMember = computed(() => state.authen.user.isMember)
    const acceptWallets = computed(
      () => $config.checkout.payment.acceptWalletTypes
    )
    const blockedProductsForPayment = computed(
      () =>
        $config?.checkout?.payment?.blockedProductsForPayment || [
          'membership',
          'expert-verification',
          'membership-personal-doctor'
        ]
    )

    const isPaymentEnabled = computed(() => {
      const isProductBlocked = blockedProductsForPayment.value?.includes(
        skuProduct.value
      )
      return !isProductBlocked
    })

    const pointConversion = computed(
      () => $config.checkout?.payment?.pointConversion
    )

    const wellcareDialog = ref(false)

    const isEnoughBalance = computed(
      () => ![422, 426].includes(props.statusCode)
    )

    let walletDefault
    const walletConfig = computed(() => $config.checkout.payment.wallet)

    const walletsService = computed(() => {
      const res: any[] = []
      for (const wallet of props.wallets as Array<{ type: string }>) {
        if (acceptWallets.value.includes(wallet?.type)) {
          res.push({
            ...wallet,
            ...walletConfig.value[wallet.type],
            activate: true
          })
          if (wallet?.type === 'cash')
            walletDefault = {
              ...wallet,
              ...walletConfig.value[wallet.type],
              activate: true
            }
        }
      }
      return res.sort((a, b) =>
        a.type === 'cash' ? -1 : b.type === 'cash' ? 1 : 0
      ) // Sort on the frontend
    })

    const currentWallet = ref(walletsService.value[0])
    const onCardChange = (index: any) => {
      emit('on-card-change', walletsService.value[index])
      currentWallet.value = walletsService.value[index]
    }

    // const validateWallet = (wallet: any, total: number) => {
    //   return wallet.type === 'membership'
    //     ? (wallet?.child ?? []).some((item: any) => item.useable < 1)
    //     : pointConversion.value?.autoConvert
    //     ? wallet.usable < total / pointConversion.value.rate && wallet.activate
    //     : wallet.usable < total && wallet.activate
    // }

    const handleClick = () => {
      wellcareDialog.value = false
      isShowTooltip.value = true
      setTimeout(() => {
        isShowTooltip.value = false
      }, 4000)
      emit('start-payment-process')
    }

    return {
      isMember,
      isLoading,
      skuProduct,
      acceptWallets,
      blockedProductsForPayment,
      handleClick,
      walletsService,
      currentWallet,
      onCardChange,
      pointConversion,
      walletDefault,
      isPaymentEnabled,
      isShowTooltip,
      wellcareDialog,
      isEnoughBalance
      // validateWallet,
    }
  }
})
</script>

<template>
  <div>
    <v-row>
      <v-col cols="12">
        <v-card class="transparent" tile outlined>
          <v-card-title
            class="text-20 font-weight-bold mx-auto"
            style="width: 97%"
            >{{ $t('choose card') }}
          </v-card-title>
          <template
            v-if="acceptWallets.length <= 1 || isMembershipProduct || !isMember"
          >
            <w-wallet-card
              v-if="wallets"
              :member-service="walletDefault"
              :activate="true"
            />
          </template>
          <template v-else>
            <WalletSlider
              :wallets="walletsService"
              :total="order.total"
              @onCardChange="onCardChange"
            />
          </template>
        </v-card>
      </v-col>
      <v-col v-if="pointConversion && pointConversion.show" cols="12">
        <w-point-conversion
          :current-point="currentWallet.usable"
          :payment-fee="-order.discount"
        />
      </v-col>
      <v-col v-if="statusCode === 422 || statusCode === 426" cols="12">
        <div class="py-2 px-3">
          <p
            class="text-16 text-center mb-0"
            style="
              color: rgb(253 162 11);
              padding: 8px 4px;
              background: rgb(255 218 157 / 20%);
              border-radius: 8px;
            "
          >
            ⚠️
            {{
              statusCode === 422
                ? $t(
                    'your card balance is not enough for service booking, please purchase additional points'
                  )
                : $t(
                    'Your member ID is not eligible for this service. Please choose another payment method'
                  )
            }}
          </p>
        </div>
      </v-col>
      <!-- <v-col cols="12">
        <add-ons :order="order" :order-items="orderItems" />
      </v-col> -->

      <v-col cols="12">
        <w-choose-topup
          :title="'Topup method'"
          :order="order"
          :current-wallet="currentWallet"
        />
      </v-col>
      <!-- <v-col
        cols="12"
        :style="{
          pointerEvents: isPaymentEnabled === false ? 'none' : '',
          opacity: isPaymentEnabled === false ? '0.4' : '1'
        }"
      >
        <w-choose-topup
          :title="'Topup method'"
          :order="order"
          :current-wallet="currentWallet"
        />
      </v-col> -->
    </v-row>

    <p
      v-if="isShowTooltip && (statusCode === 422 || statusCode === 426)"
      style="
        background: rgba(97, 97, 97, 0.9);
        color: #fff;
        border-radius: 4px;
        font-size: 14px;
        line-height: 22px;
        padding: 5px 16px;
        position: fixed;
        left: 50%;
        width: 85%;
        bottom: 100px;
        transform: translateX(-50%);
      "
    >
      {{
        statusCode === 422
          ? $t(
              'your card balance is not enough for service booking, please purchase additional points'
            )
          : $t(
              'Your member ID is not eligible for this service. Please choose another payment method'
            )
      }}
    </p>

    <v-footer elevation="1" app inset style="background-color: #fff">
      <div class="my-2 d-flex align-start justify-space-between w-full">
        <div class="d-flex flex-column align-start">
          <p class="font-weight-bold black--text text-18 mb-0">
            {{ $t('total') }}
          </p>
          <!-- <p class="text-16" style="color: #6a7282">{{ $t('total') }}</p> -->
        </div>
        <div class="d-flex flex-column align-end">
          <p
            class="mb-0 pa-0 total-price primary--text font-weight-bold text-20"
          >
            {{ $n(order.total, 'currency') }}
          </p>
        </div>
      </div>
      <v-btn
        :block="$vuetify.breakpoint.xsOnly"
        class="white--text text-uppercase mx-auto py-6"
        width="300"
        color="primary"
        :style="{
          pointerEvents: isPaymentEnabled === false ? 'none' : '',
          opacity: isPaymentEnabled === false ? '0.4' : '1'
        }"
        rounded
        @click="wellcareDialog = true"
      >
        <template v-if="isLoading">
          <v-progress-circular :size="20" color="#fff" indeterminate />
        </template>
        <template v-else>
          <span
            ><strong>{{ $t('Pay now') }}</strong></span
          >
        </template>
      </v-btn>
    </v-footer>
    <v-dialog v-model="wellcareDialog" max-width="450" scrollable>
      <v-card>
        <v-card-text>
          <div class="font-weight-bold text-h6 text-center mt-4">
            📢 Thông báo quan trọng từ Wellcare
          </div>
          <div class="mt-4">
            <p>Wellcare sẽ ngừng hoạt động khám từ xa kể từ 01/03/2025.</p>
            <p class="mt-2">
              Tiền thanh toán dư hoặc được hoàn về đang lưu trong sổ khám chỉ có
              thể dùng để khám từ xa và không thể rút ra (Thông tư
              39/2014/TT-NHNN và Thông tư 23/2019/TT-NHNN của Ngân hàng Nhà nước
              Việt Nam).
            </p>

            <ul class="mt-2 list-inside list-disc">
              <li class="mt-2">
                Hệ thống đặt hẹn vẫn tiếp tục duy trì đến 31/03/2025 và chỉ hoạt
                động để những bệnh nhân đã chuyển tiền dư hoặc được hoàn về sổ
                khám (tính đến 28/02/2025) tiếp tục sử dụng dịch vụ đến
                31/03/2025.
              </li>
              <li class="mt-2">
                Từ 01/03/2025, Wellcare không hỗ trợ chuyển khoản hoặc thanh
                toán mới.
              </li>
              <li class="mt-2">
                Người dùng đã là thành viên vẫn có thể dùng các quyền lợi Health
                GPT, EduHub, Health Programs trên ứng dụng cho đến ngày hết hạn.
              </li>
            </ul>
          </div>
        </v-card-text>
        <v-card-actions v-if="isEnoughBalance">
          <v-btn
            block
            depressed
            color="primary"
            class="py-2"
            rounded
            @click="handleClick"
            >{{ $t('continue') }}</v-btn
          >
        </v-card-actions>
        <v-card-actions>
          <v-btn
            block
            depressed
            text
            rounded
            class="py-2"
            @click="wellcareDialog = false"
          >
            {{ $t('close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped>
.limit-width {
  max-width: 250px !important;
}
</style>
