<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'ConfirmFooter',
  emits: ['confirm'],
  setup(_, { emit }) {
    const onConfirm = () => {
      emit('confirm')
    }

    return {
      onConfirm
    }
  }
})
</script>

<template>
  <v-footer elevation="0" app inset>
    <v-btn
      width="250"
      :block="$vuetify.breakpoint.xsOnly"
      depressed
      class="text-capitalize mx-auto py-6"
      color="primary"
      @click="onConfirm"
    >
      <span>
        <strong>{{ $t('confirm') }}</strong>
      </span>
    </v-btn>
  </v-footer>
</template>

<style scoped>
.v-footer {
  background-color: transparent !important;
}
</style>
