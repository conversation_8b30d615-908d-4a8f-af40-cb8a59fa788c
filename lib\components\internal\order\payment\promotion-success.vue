<script lang="ts">
import { computed, defineComponent, useContext } from '@nuxtjs/composition-api'
import AiaPolicy from '../policy/aia.vue'
import ManulifePolicy from '../policy/manulife.vue'

export default defineComponent({
  components: { AiaPolicy, ManulifePolicy },
  setup() {
    const { $config } = useContext()
    const accentColor = '#f05a28'

    const voucherPolicy = computed(
      () => $config.checkout.payment.voucher?.policy
    )

    const policyComponent = computed(() => {
      switch (voucherPolicy.value) {
        case 'aia-flexa':
          return 'AiaPolicy'
        case 'manulife':
          return 'ManulifePolicy'
        default:
          return null
      }
    })

    const cardStyle = computed(() => ({
      border: 'none !important',
      backgroundColor: `${accentColor}1A` // 10% opacity
    }))

    return {
      voucherPolicy,
      policyComponent,
      cardStyle,
      accentColor
    }
  }
})
</script>

<template>
  <div>
    <template v-if="policyComponent">
      <component :is="policyComponent"
    /></template>
    <template v-else>
      <v-card
        class="d-flex justify-space-between align-center mx-auto px-4 py-1"
        :style="cardStyle"
        width="97%"
        height="56"
        outlined
      >
        <div class="text-16 d-flex align-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            style="fill: #f05a28"
          >
            <path
              d="M21 5H3a1 1 0 0 0-1 1v4h.893c.996 0 1.92.681 2.08 1.664A2.001 2.001 0 0 1 3 14H2v4a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1v-4h-1a2.001 2.001 0 0 1-1.973-2.336c.16-.983 1.084-1.664 2.08-1.664H22V6a1 1 0 0 0-1-1zM9 9a1 1 0 1 1 0 2 1 1 0 1 1 0-2zm-.8 6.4 6-8 1.6 1.2-6 8-1.6-1.2zM15 15a1 1 0 1 1 0-2 1 1 0 1 1 0 2z"
            ></path>
          </svg>
          <span class="ml-3" :style="{ color: accentColor }">
            {{ $t('Code applied') }}
          </span>
        </div>
      </v-card>
    </template>
  </div>
</template>
