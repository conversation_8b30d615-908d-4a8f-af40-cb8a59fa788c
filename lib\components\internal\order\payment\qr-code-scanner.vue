<template>
  <v-container class="pa-4 pt-2 pb-0" max-width="md">
    <!-- QR Code Section -->
    <v-card
      outlined
      class="pa-6 pt-2 pb-0 d-flex flex-column align-center grey lighten-4"
    >
      <v-card-title class="text-20 font-weight-bold pb-2 px-2">
        Scan QR Code to Pay
      </v-card-title>
      <p class="grey--text text-14 text-center px-2">
        Sử dụng ứng dụng ngân hàng của bạn để quét (scan) hoặc sử dụng ứng dụng
        Camera hỗ trợ mã QR để quét mã.
      </p>
      <VietQrCode
        :bank-id="bankId"
        :account-no="accountNo"
        :amount="paymentAmount"
        :description="paymentDescription"
        :account-name="accountName"
      />
      <!-- <div class="mt-3 d-flex align-center justify-center">
        <v-progress-circular :size="18" indeterminate :width="2" color="primary"></v-progress-circular>
        <p class="text-16 font-weight-bold grey--text ml-2 mb-0"><PERSON>ang chờ bạn quét</p>
      </div> -->
    </v-card>
  </v-container>
</template>

<script>
import { ref, computed, defineComponent } from '@nuxtjs/composition-api'
import VietQrCode from '../../topup/vietqr-code.vue'
export default defineComponent({
  components: { VietQrCode },
  props: {
    order: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const bankId = ref('vietcombank')
    const accountNo = ref('*************')
    const paymentAmount = computed(() => props?.order?.total)
    const paymentDescription = ref('***********')
    const accountName = ref('CTCP CONG NGHE MHEALTH')

    return {
      bankId,
      accountNo,
      accountName,
      paymentAmount,
      paymentDescription
    }
  }
})
</script>
