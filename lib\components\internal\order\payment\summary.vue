<script lang="ts">
import {
  computed,
  defineComponent,
  useContext,
  useStore
} from '@nuxtjs/composition-api'
import { getProductInfo } from '../../../../composables'
import OrderItem from '../order-item/index.vue'

export default defineComponent({
  components: { OrderItem },
  props: {
    order: {
      type: Object,
      default: () => {}
    },
    orderItems: {
      type: Array,
      default: () => []
    },
    waiting: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const { $config } = useContext()
    const { state }: any = useStore()
    const { isMember, isMembershipProduct } = getProductInfo()

    // Computed properties for system amount, total, and showing fees
    const order = computed(() => state?.checkout?.order)

    const systemAmount = computed(() => order.value?.systemAmount)

    const systemTotal = computed(() => order.value?.systemTotal)

    const showFee = computed(() => $config?.checkout?.payment?.showFee)

    const sortedOrderItems = computed(() => {
      const orderItemsCopy = [...props.orderItems]

      const mainProductName = state?.checkout?.mainProductName

      const getPriority = (sku: string, slug: string) => {
        if (slug === mainProductName) {
          return 0
        } else if (sku === 'membership') {
          return 1
        } else if (sku.startsWith('membership-')) {
          return 2
        } else if (sku.includes('consultation-')) {
          return 4
        } else {
          return 3
        }
      }

      return orderItemsCopy.sort((a, b) => {
        const priorityA = getPriority(
          a?.product?.sku || a?.product?.title || '',
          a?.product?.slug || ''
        )
        const priorityB = getPriority(
          b?.product?.sku || b?.product?.title || '',
          b?.product?.slug || ''
        )

        if (priorityA !== priorityB) {
          return priorityA - priorityB
        }

        return (
          new Date(a.occurredAt).getTime() - new Date(b.occurredAt).getTime()
        )
      })
    })

    return {
      showFee,
      isMember,
      systemTotal,
      systemAmount,
      sortedOrderItems,
      isMembershipProduct
    }
  }
})
</script>

<template>
  <v-card
    rounded="lg"
    class="px-2 py-1 pb-4 mx-auto"
    width="97%"
    elevation="0"
    style="box-shadow: 0px 2px 6px -2px rgba(79, 79, 79, 0.2); border: none"
  >
    <!-- Display each order item using 'order-item' component -->
    <div style="border-bottom: dashed 1px #ccc" class="mb-2">
      <v-slide-y-transition group>
        <OrderItem
          v-for="(item, i) in sortedOrderItems"
          :key="'order-' + i"
          :orders="sortedOrderItems"
          :order-item="item"
          :order="order"
          :single-order="sortedOrderItems.length === 1"
          :type-name="item.type.name"
        />
      </v-slide-y-transition>
    </div>

    <!-- START SERVICE SESSION -->
    <!-- Display service fee information -->
    <div v-if="!isMembershipProduct && showFee" class="system-fee mt-2 pa-2">
      <div v-if="!isMember">
        <!-- service fee -->
        <v-card flat rounded="lg" class="d-flex text-18 pa-0">
          <div>
            <div class="font-weight-bold">{{ $t('service fee') }}</div>
            <div class="text-14">
              {{ $t('one-time') }}
            </div>
          </div>
          <v-spacer></v-spacer>
          <div class="font-weight-bold">{{ $n(50000, 'currency') }}</div>
        </v-card>
      </div>

      <div v-else class="d-flex align-center justify-space-between">
        <div>
          <div class="text-18 font-weight-bold">
            {{ $t('service fee') }}
          </div>
          <div class="text-14" style="color: var(--v-primary-base)">
            {{ $t('waiver of the service fee for member') }}
          </div>
        </div>

        <!-- Display system amount and total -->
        <div style="text-align: right">
          <div
            :class="[
              'text-18',
              'text-right',
              {
                'text-decoration-line-through': systemTotal === 0
              }
            ]"
            :style="{
              color: systemTotal === 0 ? '#7f7f82c5' : '',
              fontWeight: systemTotal === 0 ? 'normal' : 'bold'
            }"
          >
            <span v-if="systemAmount > 0">
              {{ $n(systemAmount, 'currency') }}</span
            >
          </div>
          <div v-if="systemTotal === 0" class="font-weight-bold text-18">
            {{ $n(systemTotal, 'currency') }}
          </div>
        </div>
      </div>
    </div>
    <!-- END SERVICE FEE SESSION -->
    <!-- Display total and subtotal -->
    <v-expand-transition>
      <div v-if="showFee" class="mt-3">
        <div v-if="order.discount || order.voucherAmount" class="px-1">
          <div
            class="d-flex align-center justify-space-between text-18 py-1 font-weight-bold"
          >
            <span>{{ $t('subtotal') }}</span>
            <span>{{ $n(order.subTotal, 'currency') }} </span>
          </div>
          <div
            class="d-flex align-center justify-space-between text-18 py-1 font-weight-bold"
          >
            <span>{{ $t('discount') }}</span>
            <span class="text--darken-1"
              >-{{ $n(order.discount, 'currency') }}
            </span>
          </div>
        </div>

        <!-- Display final total -->
        <div
          class="d-flex align-center justify-space-between pt-2 font-weight-bold px-1 black--text"
        >
          <span class="text-18">{{ $t('total') }}</span>
          <div>
            <v-progress-circular
              v-if="waiting"
              :size="22"
              indeterminate
              :width="2"
              color="primary"
            ></v-progress-circular>

            <span v-else class="text-23 font-weight-black primary--text">
              {{ $n(order.total, 'currency') }}
            </span>
          </div>
        </div>
      </div>
    </v-expand-transition>
  </v-card>
</template>
