<script lang="ts">
import { computed, defineComponent, useContext } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const { $config } = useContext()

    const showCancel = computed(() => {
      if ($config.checkout?.payment?.voucher?.cancel) return true
      return false
    })
    return { showCancel }
  }
})
</script>

<template>
  <div>
    <v-card
      class="d-flex justify-space-between align-center mx-auto px-4 py-1"
      style="border: none !important; background-color: #86ebdf30"
      width="97%"
      height="56"
      outlined
    >
      <span class="text-16"
        ><v-icon color="primary">$check-circle</v-icon
        ><span class="ml-3" style="color: var(--v-primary-base)">{{
          $t('voucher applied successfully')
        }}</span></span
      >
      <v-btn
        v-if="showCancel"
        height="40"
        small
        depressed
        color="error lighten-1"
        class="text-capitalize font-weight-bold"
        :loading="loading"
        @click="$emit('cancelVoucher')"
        >{{ $t('cancel') }}</v-btn
      >
    </v-card>
  </div>
</template>
