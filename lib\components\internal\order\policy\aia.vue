<template>
  <v-card
    class="mx-auto px-8 py-4"
    width="97%"
    :style="bg"
    height="145"
    style="border-radius: 20px"
  >
    <div class="d-flex align-center justify-space-between" style="height: 100%">
      <div class="white--text">
        <div class="font-weight-bold" style="font-size: 24px">
          {{ $t('Sponsored by AIA') }}
        </div>
        <div v-if="aiaPromotion" style="font-size: 20px">
          {{ $t('usage') }}:
          <span class="font-weight-bold"
            ><span>{{
              Math.max(
                0,
                aiaPromotion.policy.benefit.repeat.frequencyMax -
                  aiaPromotion.redemption.count
              )
            }}</span
            >/<span>{{
              aiaPromotion.policy.benefit.repeat.frequencyMax
            }}</span></span
          >
        </div>
      </div>
      <v-img contain src="/aia.png" max-width="55" max-height="55"></v-img>
    </div>
  </v-card>
</template>
<script lang="ts">
import { defineComponent, useStore, ref, watch } from '@nuxtjs/composition-api'
export default defineComponent({
  setup() {
    const bg = `background: #f3bfcd !important;
        background: radial-gradient(100% 436.96% at 100% 32.31%, #D31145 23.43%, rgba(211, 17, 69, 0.6) 51.14%, rgba(211, 17, 69, 0.8) 90.1%)`
    const { state }: any = useStore()
    const aiaPromotion = ref(
      state.checkout?.promotions?.find(
        (promotion) => promotion.policy.key === 'aia-flexa'
      )
    )
    watch(
      state.checkout,
      () => {
        if (state.checkout.promotions)
          aiaPromotion.value = state.checkout.promotions.find(
            (promotion) => promotion.policy.key === 'aia-flexa'
          )
      },
      { deep: true }
    )
    return { bg, aiaPromotion }
  }
})
</script>
