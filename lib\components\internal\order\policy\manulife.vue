<template>
  <v-card
    class="mx-auto pa-8"
    width="97%"
    :style="bg"
    height="165"
    style="border-radius: 8px"
  >
    <div class="font-weight-bold white--text header" style="font-size: 24px">
      {{ $t('Sponsored by Manulife') }}
    </div>
    <div class="d-flex align-center justify-space-between" style="height: 100%">
      <div v-if="manulifePromotion" class="white--text body">
        <div>{{ $t('you have') }}</div>
        <div style="font-size: 20px">
          <span class="font-weight-bold"
            ><span>{{
              Math.max(
                0,
                manulifePromotion.policy.benefit.repeat.frequencyMax -
                  manulifePromotion.redemption.count
              )
            }}</span
            >/<span>{{
              manulifePromotion.policy.benefit.repeat.frequencyMax
            }}</span></span
          >
          <span class="text-lowercase">{{ $t('usage') }}</span>
        </div>
      </div>
      <v-img contain src="/move.png" max-width="55" max-height="55"></v-img>
    </div>
    <v-bottom-sheet
      v-model="bottomSheet"
      hide-overlay
      class="rounded-xl rounded-br-0 rounded-bl-0"
    >
      <v-card
        elevation="0"
        style="border-top: 8px solid var(--v-error-base)"
        class="py-4"
      >
        <v-btn
          icon
          small
          style="position: absolute; top: 5px; right: 5px"
          @click="bottomSheet = false"
          ><v-icon>$close</v-icon></v-btn
        >
        <div
          class="d-flex flex-column justify-center align-center"
          style="gap: 10px"
        >
          <v-icon color="error" size="70">$close-circle-outline</v-icon>
          <div>{{ $t('you are out of usage') }}</div>
        </div>
      </v-card>
    </v-bottom-sheet>
  </v-card>
</template>
<script lang="ts">
import {
  defineComponent,
  useStore,
  ref,
  watch,
  onMounted
} from '@nuxtjs/composition-api'
export default defineComponent({
  setup() {
    const bottomSheet = ref(false)
    const bg = `background: #2F3144 !important;`
    const { state }: any = useStore()
    const manulifePromotion = ref(
      state.checkout?.promotions?.find(
        (promotion) => promotion.policy.key === 'manulife-move'
      )
    )
    watch(
      state.checkout,
      () => {
        if (state.checkout.promotions)
          manulifePromotion.value = state.checkout.promotions.find(
            (promotion) => promotion.policy.key === 'manulife-move'
          )
        if (
          manulifePromotion.value?.redemption?.count >=
            manulifePromotion.value?.policy?.benefit?.repeat?.frequencyMax ||
          manulifePromotion.value?.redemption?.sum >=
            manulifePromotion.value?.policy?.benefit?.maxAmount
        ) {
          bottomSheet.value = true
        }
      },
      { deep: true }
    )
    onMounted(() => {
      if (
        manulifePromotion.value &&
        manulifePromotion.value?.redemption?.count >=
          manulifePromotion.value?.policy?.benefit?.repeat?.frequencyMax
      ) {
        bottomSheet.value = true
      }
    })
    return { bg, manulifePromotion, bottomSheet }
  }
})
</script>
