<template>
  <v-row>
    <v-col cols="12">
      <div class="text-center" style="width: 100%">
        <v-row>
          <v-col cols="12" class="d-flex justify-center">
            <div class="d-flex justify-center" style="max-width: 350px">
              <v-icon size="100" color="error">$alert-circle</v-icon>
            </div>
          </v-col>
        </v-row>
        <h1>
          {{ $t('this order has been abandoned') }}
        </h1>
        <p>{{ $t('Go back and try again or call us +84 366 905 905') }}</p>
        <v-btn
          depressed
          color="primary"
          to="/"
          class="align-center white--text text-capitalize"
        >
          {{ $t('back to home') }}
        </v-btn>
      </div>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'
export default defineComponent({
  setup() {}
})
</script>
