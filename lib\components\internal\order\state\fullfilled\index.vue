<template>
  <v-overlay>
    <div class="text-center">
      <v-progress-circular
        :size="70"
        color="primary"
        indeterminate
      ></v-progress-circular>
    </div>
  </v-overlay>
</template>
<script lang="ts">
import {
  defineComponent,
  useRouter,
  useRoute,
  onMounted
} from '@nuxtjs/composition-api'
import getProductInfo from '../../../../../composables/get-product-info'
export default defineComponent({
  setup() {
    const router = useRouter()
    const route = useRoute()
    const { isMembershipProduct, isPregnancyProduct } = getProductInfo()
    onMounted(() => {
      router.push({
        path: `/payment/order/${route.value.params.id}/success`,
        query: {
          membershipProduct: isMembershipProduct.value,
          pregnancyProduct: isPregnancyProduct.value
        }
      })
    })
  }
})
</script>
