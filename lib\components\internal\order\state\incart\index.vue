<script lang="ts">
import {
  ref,
  watch,
  computed,
  useStore,
  // useRoute,
  onMounted,
  useContext,
  onUnmounted,
  defineComponent,
  defineAsyncComponent
} from '@nuxtjs/composition-api'
import { useDebounceFn } from '@vueuse/core'
import {
  getProductInfo,
  useOrderStatusEvent
  // useSearchRelatedProducts
} from '../../../../../composables'
import {
  updateOrder,
  // usePaymentOrder,
  useProductOrder,
  useValidateOrderPayment
} from '../../../../../repositories'

// components
const Summary = defineAsyncComponent(() => import('../../payment/summary.vue'))
const ChooseCard = defineAsyncComponent(
  () => import('../../payment/choose-card.vue')
)
// const Bundles = defineAsyncComponent(() => import('../../payment/bundles.vue'))
const ConfirmFooter = defineAsyncComponent(
  () => import('../../payment/confirm-footer.vue')
)
const VoucherSuccess = defineAsyncComponent(
  () => import('../../payment/voucher-success.vue')
)
const PromotionSuccess = defineAsyncComponent(
  () => import('../../payment/promotion-success.vue')
)
// const QRCodeScanner = defineAsyncComponent(() => import('../../payment/qr-code-scanner.vue'))

export default defineComponent({
  components: {
    Summary,
    ChooseCard,
    // Bundles,
    ConfirmFooter,
    // QRCodeScanner,
    VoucherSuccess,
    PromotionSuccess
  },
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItems: {
      type: Array,
      default: () => []
    }
  },
  setup(props, { root }) {
    const { state } = useStore()
    const { $axios, $config } = useContext()
    // const route = useRoute()

    // State
    const maxRetries = 2
    const retryDelay = 1500 // 1.5 second

    const statusCode = ref<number>()
    const payload = ref<any>({})
    const updatedOrder = ref<any>({})
    const allowPayment = ref<boolean>(false)
    const walletIdUpdateOrder = ref<any>()
    const model = ref(null)
    const waiting = ref<boolean>(false)
    // const policies = ref<string[]>(['aia-flexa', 'manulife'])

    const lastValidatedPayload = ref(null)
    const lastValidationResult = ref(null)

    // Get
    const checkoutConfig = computed(() => $config.checkout)
    const wallets = computed(() => (state as any)?.checkout?.wallets)
    const paymentConfig = computed(() => $config.checkout.payment)
    const walletConfig = computed(
      () => $config.checkout.payment.acceptWalletTypes
    )
    // const policy = computed<string>(
    //   () => paymentConfig.value.voucher?.policy || ''
    // )

    // Handle API
    const {
      // reloadOrder: executeProductOrder,
      // updateOrder: updateNewOrder,
      order,
      orderItems
    } = useProductOrder()

    // Get Product info
    const { isMember, typeProduct, isMembershipProduct } = getProductInfo()

    // Wallet validate
    const {
      execute: validateOrderPayment
      // onSucess: onValidateOrderPaymentSuccess
      // onError: onValidateOrderPaymentError
    } = useValidateOrderPayment(order)

    const {
      execute: _updateOrder,
      onSucess: onUpdateOrderSuccess,
      loading: updateLoading
    } = updateOrder(updatedOrder)

    // const { execute: searchRelatedProducts, data } = useSearchRelatedProducts([
    //   `payment-${isMember.value ? 'payment-member' : 'non-member'}-${
    //     typeProduct.value
    //   }`,
    //   `payment-${isMember.value ? 'payment-member' : 'non-member'}`,
    //   `payment-${typeProduct.value}`
    // ])

    // const { executeGetOrder } = usePaymentOrder(
    //   computed(() => ({ _id: route.value.params.id })),
    //   computed(() => ({ polling: computed(() => 1000) })),
    //   computed(() => 'pending')
    // )

    $axios.put(
      `${$config.endpoint}/ecommerce/order/${props.order?._id}/apply-change`
    )
    const { executeEvtSource } = useOrderStatusEvent(
      computed(() => order.value?._id)
    )

    const currentWallet = ref(
      wallets.value.find((wallet) => wallet.type === walletConfig.value[0])
    )

    // Logic UI
    const isShowCard = computed<boolean>(() => {
      const alwaysShowCard = paymentConfig.value?.alwaysShowCard
      const orderTotal = order.value?.total
      const nonOnlyVoucher = !paymentConfig.value.voucher.onlyVoucher

      return alwaysShowCard || (orderTotal && nonOnlyVoucher)
    })
    const isVoucherSuccess = computed<boolean>(
      () => props.order?.voucher || paymentConfig.value?.voucher?.autoApply
    )
    const isPromotionSuccess = computed<boolean>(() => {
      const isPromotions =
        order.value?.promotions && order.value?.promotions.length
      const isNotMembershipProduct = !isMembershipProduct.value
      return isPromotions && isNotMembershipProduct
    })
    // const isShowBundles = computed<boolean>(() => {
    //   const nonMemberProduct = !isMembershipProduct.value
    //   const hasBundles = data.value.length
    //   const nonPolicy = !policies.value.includes(policy.value)
    //   return nonMemberProduct && hasBundles && nonPolicy
    // })
    const isShowConfirmFooter = computed<boolean>(
      () => props.order.total === 0 && !isShowCard.value
    )

    // Optimize validation functions
    const shouldValidate = (newPayload, oldPayload) => {
      if (!oldPayload) return true
      return (
        newPayload.total !== oldPayload.total ||
        newPayload.payment.wallet !== oldPayload.payment.wallet ||
        JSON.stringify(newPayload.orderItems) !==
          JSON.stringify(oldPayload.orderItems)
      )
    }

    // Function validateWithRetry: Validates payment with a retry mechanism up to maxRetries times
    const validateWithRetry = async (payload, retries = 0) => {
      try {
        const res = await validateOrderPayment(payload) // Call the validation API
        statusCode.value = res?.code
        if (statusCode.value && [200, 201].includes(statusCode.value)) {
          allowPayment.value = true
        }
        walletIdUpdateOrder.value = res.results[0]?._id
        lastValidationResult.value = res
      } catch (error) {
        if (retries < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay))
          await validateWithRetry(payload, retries + 1) // Retry validation
        } else {
          // onValidateOrderPaymentError(error) // Handle error after all retries
          statusCode.value = error.response.status
          if (statusCode.value && ![200, 201].includes(statusCode.value)) {
            allowPayment.value = false
          }
          lastValidationResult.value = null // Clear old result on error
        }
      }
    }

    const debouncedValidateOrderPayment = useDebounceFn((payload) => {
      if (shouldValidate(payload, lastValidatedPayload.value)) {
        validateWithRetry(payload)
        lastValidatedPayload.value = JSON.parse(JSON.stringify(payload))
      }
      // else {
      //   // Use the latest validation result if there's no significant change
      //   onValidateOrderPaymentSuccess(lastValidationResult.value)
      // }
    }, 300)

    // Func
    const cancelVoucher = () => {
      updatedOrder.value = {
        _id: props.order._id,
        voucher: null,
        promotions: []
      }
      _updateOrder()
    }

    const confirmPayment = () => {
      updatedOrder.value = {
        _id: props.order._id,
        source: process.client ? window.location?.hostname : undefined,
        payment: {
          ...props.order.payment,
          state: 'paid',
          wallet: walletIdUpdateOrder.value
        }
      }
      _updateOrder()
    }

    const startPaymentProcess = () => {
      if (allowPayment.value && walletIdUpdateOrder.value) {
        // Perform a final validation before confirming payment
        confirmPayment()
        // validateWithRetry(payload.value).then(() => {
        // })
      } else {
        scrollToBottom() // Scroll to bottom if conditions are not met
      }
    }

    const onCardChange = (wallet: any) => {
      currentWallet.value = wallet
      payload.value = {
        ...order.value,
        orderItems: orderItems.value,
        payment: {
          ...order.value.payment,
          wallet: wallet._id
        }
      }
      debouncedValidateOrderPayment(payload.value)
    }

    // const debouncedHandleCallProduct = useDebounceFn(async (index: number) => {
    //   try {
    //     waiting.value = true
    //     model.value = index
    //     const selectedProduct = data.value[index]
    //     if (selectedProduct) {
    //       await executeProductOrder({ product: { slug: selectedProduct.slug } })
    //       await updateNewOrder({ ...order.value })
    //       await _updateOrder({
    //         _id: order.value._id,
    //         promotions: (state as any).checkout.promotions
    //       })
    //     } else {
    //       console.error('Selected product not found')
    //     }
    //   } catch (error) {
    //     console.error('Error occurred:', error)
    //   } finally {
    //     await executeGetOrder()
    //     waiting.value = false
    //   }
    // }, 300)

    watch(
      [() => order.value, () => orderItems.value],
      ([newOrder, newOrderItems]) => {
        payload.value = {
          ...newOrder,
          orderItems: newOrderItems,
          payment: {
            ...newOrder.payment,
            wallet: currentWallet.value?._id
          }
        }
        debouncedValidateOrderPayment(payload.value)
      },
      { deep: true }
    )

    onUpdateOrderSuccess(() => {
      ;(root as any).$loading?.finish()
    })

    onMounted(() => {
      executeEvtSource()
      // searchRelatedProducts()
    })

    onUnmounted(() => {
      model.value = null
      waiting.value = false
    })

    const scrollToBottom = () => {
      setTimeout(() => {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        })
      }, 500) // Small delay to ensure DOM is updated
    }

    return {
      // data,
      model,
      wallets,
      waiting,
      isMember,
      isShowCard,
      statusCode,
      typeProduct,
      walletConfig,
      onCardChange,
      cancelVoucher,
      paymentConfig,
      updateLoading,
      // isShowBundles,
      scrollToBottom,
      checkoutConfig,
      isVoucherSuccess,
      isPromotionSuccess,
      startPaymentProcess,
      isMembershipProduct,
      isShowConfirmFooter,
      // handleCallProduct: debouncedHandleCallProduct,
      allowPayment
    }
  }
})
</script>

<template>
  <v-container
    class="pa-0"
    :style="{ width: $vuetify.breakpoint.smAndUp ? '80%' : '100%' }"
  >
    <v-row>
      <v-slide-x-transition>
        <v-col cols="12">
          <Summary
            :order-items="orderItems"
            :order="order"
            :waiting="waiting"
          />
        </v-col>
      </v-slide-x-transition>

      <!-- <v-slide-y-transition>
        <v-col v-if="isShowBundles" cols="12">
          <Bundles
            :data="data"
            :model.sync="model"
            @select-bundle="handleCallProduct"
          />
        </v-col>
      </v-slide-y-transition> -->

      <v-slide-y-transition>
        <v-col v-if="isVoucherSuccess" cols="12">
          <VoucherSuccess
            :loading="updateLoading"
            @cancel-voucher="cancelVoucher"
          />
        </v-col>
      </v-slide-y-transition>

      <v-slide-y-transition>
        <v-col v-if="isPromotionSuccess" cols="12">
          <PromotionSuccess />
        </v-col>
      </v-slide-y-transition>

      <!-- <v-slide-y-transition>
        <QRCodeScanner :order="order" />
      </v-slide-y-transition> -->

      <v-slide-y-transition>
        <v-col v-if="isShowCard" cols="12">
          <ChooseCard
            :order="order"
            :wallets="wallets"
            :order-items="orderItems"
            :is-membership-product="isMembershipProduct"
            :status-code="statusCode"
            :allow-payment="allowPayment"
            @start-payment-process="startPaymentProcess"
            @on-card-change="onCardChange"
          />
        </v-col>
      </v-slide-y-transition>

      <v-slide-y-transition>
        <v-col v-if="isShowConfirmFooter" cols="12">
          <ConfirmFooter @confirm="startPaymentProcess" />
        </v-col>
      </v-slide-y-transition>
    </v-row>
  </v-container>
</template>
