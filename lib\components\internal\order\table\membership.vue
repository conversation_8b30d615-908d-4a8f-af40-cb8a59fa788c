<template>
  <div>
    <div
      class="switch d-flex align-center justify-center pb-6"
      :class="{ premiere: choosen === 0, conceirge: choosen === 1 }"
    >
      <v-img
        max-width="50"
        max-height="50"
        :src="require('../../../../static/icons/choosen.png')"
      ></v-img>
    </div>
    <v-row class="membership-table">
      <v-col cols="3" class="benefit-col">
        <v-divider></v-divider>
        <div v-for="benefit in memberServices[0].benefits" :key="benefit.text">
          <div class="text-16 font-weight-bold pa-5 benefit">
            {{ $t(benefit.text) }}
          </div>
        </div>
      </v-col>
      <v-divider vertical></v-divider>
      <v-col
        v-for="(service, index) in memberServices"
        :key="index"
        cols="4.5"
        class="main-col"
        :class="{
          premiere: index === 0,
          conceirge: index === 1,
          choosen: choosen === index
        }"
        @click="chooseMembership(index)"
      >
        <div
          style="height: 120px; position: relative"
          class="d-flex flex-column align-center justify-space-between"
        >
          <div
            class="tag white--text"
            :class="{ error: index === 1, 'grey darken-3': index === 0 }"
          >
            {{ index === 0 ? 'Basic' : 'Premium' }}
          </div>
          <v-card class="mx-auto" width="120"
            ><div
              class="text-center font-weight-bold px-2 py-1 text-uppercase white--text"
              :class="{
                'grey darken-3': index === 0,
                'red darken-2 ': index === 1
              }"
            >
              {{ service.title }}
            </div></v-card
          >
          <div
            class="text-20 font-weight-bold text-center"
            :class="{
              'grey--text text--darken-3': index === 0,
              'red--text text--darken-2': index === 1
            }"
          >
            {{ $t(service.description) }}
          </div>
          <div class="text-center">
            <span class="d-inline-block" style="position: relative">
              <span class="currency font-weight-bold">VND</span>
              <span class="text-36 font-weight-bold">{{
                $n(service.pricePerMonth)
              }}</span>
              <small class="text-lowercase">/{{ $t('month') }}</small>
            </span>
          </div>
        </div>

        <v-divider></v-divider>
        <div
          v-for="benefit in service.benefits"
          :key="benefit.text"
          class="benefit text-center"
        >
          <v-icon size="26" :color="benefit.enabled ? 'success' : 'error'">{{
            benefit.enabled ? '$check' : '$close'
          }}</v-icon>
        </div>
      </v-col>
    </v-row>
    <div class="text-center pt-10">
      <v-btn
        class="white--text text-capitalize"
        rounded
        color="primary"
        width="50%"
        :disabled="choosen !== 0 && choosen !== 1"
        @click="$emit('openMembershipOrder', choosen)"
        ><span>{{ $t('confirm') }}</span></v-btn
      >
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    memberServices: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    const choosen = ref(3)
    const chooseMembership = (type) => {
      choosen.value = type
    }
    return { choosen, chooseMembership }
  }
})
</script>
<style scoped>
.tag {
  position: absolute;
  top: 10px;
  left: -30px;
  width: 100px;
  transform: rotate(-45deg);
  border-radius: 0 !important;
  font-size: 12px;
  text-align: center;
  font-weight: bolder;
}
.benefit-col {
  padding-top: 132px;
}
.currency {
  position: absolute;
  top: 0;
  right: 100%;
}
.benefit {
  min-height: 115px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-col {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  cursor: pointer;
  transition: all 0.3s linear;
  opacity: 0.9;
  overflow: hidden;
}

.main-col.premiere:hover,
.main-col.premiere.choosen {
  background-color: rgb(233, 212, 173, 0.3);
  opacity: 1;
}
.main-col.conceirge:hover,
.main-col.conceirge.choosen {
  background-color: rgb(233, 212, 173, 0.6);
  opacity: 1;
}
.benefit-col,
.main-col {
  padding-bottom: 0 !important;
}
.switch {
  margin-left: 234px;
  width: 369.5px;
  transition: 0.3s linear;
  opacity: 0;
}
.switch.premiere {
  transform: translateX(0);
  opacity: 1;
}
.switch.conceirge {
  transform: translateX(369.5px);
  opacity: 1;
}
</style>
