<template>
  <div>
    <v-checkbox
      v-model="voucher"
      color="primary"
      hide-details="auto"
      class="mt-1 px-4 voucher-checkbox"
      ><template #label
        ><span
          :class="{
            'primary--text': voucher,
            'grey--text text--darken-3': !voucher
          }"
          class="font-weight-bold"
          >{{ $t('do you have a voucher or access code?') }}</span
        ></template
      ></v-checkbox
    >
    <div
      v-if="!order.voucher"
      class="d-flex align-center voucher my-6 mx-auto"
      :class="{ appear: voucher, hidden: !voucher }"
    >
      <v-text-field
        v-model="voucherInput"
        outlined
        persistent-hint
        :error-messages="voucherError"
        hide-details="auto"
        :label="$t('enter gift code')"
        @blur="voucherError = ''"
        @keypress.enter="applyVoucher(voucherInput)"
      >
        <template #append>
          <v-btn
            :disabled="!voucherInput || !voucherInput.trim()"
            :loading="loading || updateLoading"
            height="40px"
            small
            depressed
            color="primary"
            @click="applyVoucher(voucherInput)"
            >{{ $t('apply') }}</v-btn
          >
        </template>
      </v-text-field>
    </div>
  </div>
</template>
<script lang="ts">
import {
  // computed,
  defineComponent,
  // onMounted,
  ref,
  // useContext,
  useStore
} from '@nuxtjs/composition-api'
import DOMPurify from 'dompurify'
import { useVoucher } from '../../../repositories/voucher/use-voucher'
import { updateOrder } from '../../../repositories/order/update-order'
export default defineComponent({
  props: {
    order: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const { state }: any = useStore()
    // const { $config } = useContext()
    // const voucherConfig = computed(() => $config.checkout.payment.voucher)
    const voucher = ref(false)
    const voucherInput = ref('')
    const voucherOption = ref({})
    const updateOrderOption = ref({
      _id: null,
      voucher: null,
      promotions: null
    })
    const { checkVoucher, onCheckVoucherSuccess, loading, voucherError } =
      useVoucher(voucherOption)
    const { execute: _updateOrder, loading: updateLoading } =
      updateOrder(updateOrderOption)

    onCheckVoucherSuccess((data: any) => {
      updateOrderOption.value._id = props.order._id
      updateOrderOption.value.voucher = data.results.voucher.code
        ? data.results.voucher.code
        : ''
      _updateOrder()
    })

    const applyVoucher = (code: string) => {
      voucherOption.value = {
        voucher: {
          code: DOMPurify.sanitize(code.replace(/\s+/g, ''))
        },
        user: state.authen.user,
        order: {
          ...props.order,
          source: process.client ? window.location?.host : undefined
        },
        orderItems: state.checkout?.orderItems
      }
      checkVoucher()
    }

    return {
      voucher,
      voucherInput,
      applyVoucher,
      loading,
      voucherError,
      updateLoading
    }
  }
})
</script>
<style scoped>
.voucher-text:hover {
  text-decoration: underline !important;
}
.voucher {
  transition: 0.2s linear;
}
.voucher.hidden {
  width: 0;
  opacity: 0;
  height: 0;
  visibility: hidden;
}
.voucher.appear {
  width: 50%;
  opacity: 1;
  height: 55px;
  visibility: visible;
}
@media only screen and (max-width: 720px) {
  .voucher.appear {
    width: 90%;
    opacity: 1;
    height: 55px;
    visibility: visible;
  }
}
div >>> .v-input__append-inner {
  margin-top: 8px !important;
}
.voucher-checkbox >>> .v-input__slot {
  display: flex;
  align-items: flex-start;
}
</style>
