<template>
  <div class="mt-4">
    <template v-if="membershipWallet">
      <v-card
        width="97%"
        class="rounded-lg mx-auto py-2 px-3"
        style="
          background: white;
          background: -webkit-linear-gradient(
            to right,
            white,
            rgba(0, 255, 200, 0.05)
          );
          background: linear-gradient(to right, white, rgba(0, 255, 200, 0.05));
          box-shadow: rgba(99, 99, 99, 0.05) 0px 2px 8px 0px;
        "
      >
        <div class="pa-2">
          <h4 class="text-center mx-auto">
            {{ $t('Gói bác sĩ riêng') }}
          </h4>
          <v-divider
            style="border-color: var(--v-primary-base)"
            class="mt-2"
          ></v-divider>
        </div>
        <div class="pa-4 black--text">
          <v-row class="px-3 pt-1">
            <span
              ><v-icon>$cardBulletedOutline</v-icon>
              <span>{{ $t('<PERSON>ạ<PERSON> mức còn') }}</span></span
            >
            <v-spacer></v-spacer>
            <span>50/150 phút</span>
          </v-row>
          <div class="pt-5 pb-7">
            <v-progress-linear value="15"></v-progress-linear>
          </div>
          <v-row class="px-3 pt-1">
            <span
              ><v-icon>$calendarClock</v-icon>
              <span>{{ $t('Hết hạn') }}</span></span
            >
            <v-spacer></v-spacer>
            <span>23/12</span>
          </v-row>
          <div class="pt-5">
            <v-progress-linear value="15"></v-progress-linear>
          </div>
        </div>
      </v-card>
    </template>
    <template v-else>
      <div class="text-center">you are not membership</div>
    </template>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  computed,
  useStore,
  onMounted
} from '@nuxtjs/composition-api'

export default defineComponent({
  setup(_props, { emit }) {
    const { state }: any = useStore()
    const membershipWallet = computed(() =>
      state.checkout.wallets.find((wallet: any) => wallet.type === 'membership')
    )
    onMounted(() => emit('change-wallet', 'membership'))
    return { membershipWallet }
  }
})
</script>
