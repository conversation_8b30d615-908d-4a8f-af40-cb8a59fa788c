<template>
  <div>
    <w-avatar
      size="35"
      :src="getAvatar"
      :user-id="getPatientId"
      :name="getPatientName"
      :style="{
        cursor: 'pointer'
      }"
      @click="dialogConfirm = true"
    />

    <w-dialog-confirm
      v-model="dialogConfirm"
      max-width="400"
      persistent
      title="change patient"
      description="do you want to change the patient?"
      @on:cancel="dialogConfirm = false"
      @on:submit="changePatient"
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from '@nuxtjs/composition-api'
import { useEventBus } from '@wellcare/nuxt-module-data-layer/compositions'

export default defineComponent({
  props: {
    patient: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    // const { state } = useStore()
    const { emitEvent } = useEventBus()

    const dialogConfirm = ref<boolean>(false)

    const getAvatar = computed<string>(() => props.patient?.avatar?.url || '')
    const getPatientId = computed<string>(() => props.patient?._id)
    const getPatientName = computed<string>(() => props.patient?.name)

    const changePatient = () => {
      emitEvent('preStep', { step: 3 })
    }

    return {
      getAvatar,
      getPatientId,
      getPatientName,
      // patient,
      dialogConfirm,
      changePatient
    }
  }
})
</script>
