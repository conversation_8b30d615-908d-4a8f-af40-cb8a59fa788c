<template>
  <v-container class="pa-0">
    <w-gallery
      id="gallery-medical-record"
      :files-list="filesList"
      removeable
      :downloadable="false"
      :updateable="false"
      :load-more="total > LIMIT_DEFAULT"
      @remove-item="removeItem"
      @load-more-item="loadFile(total)"
      @load-less-item="loadFile(LIMIT_DEFAULT)"
    />
    <v-row>
      <v-col
        v-for="(item, index) in uploadComponent"
        :key="index"
        cols="4"
        sm="3"
      >
        <w-file-input
          :custom-style="styleComponent"
          :option-upload="internalUploadOption"
          :type="item.type"
          :file-rules="fileRules"
          mode="v2"
          @response-data="recieveData"
        >
          <template #w-upload-icon>
            <v-icon color="primary" x-large> {{ item.icon }} </v-icon>
          </template>
          <template #w-upload-text>
            {{ $t(item.text) }}
          </template>
          <template v-if="item.note" #w-upload-note>
            <strong class="text-caption">{{ item.note }}</strong>
          </template>
        </w-file-input>
      </v-col>
    </v-row>
  </v-container>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  onMounted,
  useContext,
  watch
} from '@nuxtjs/composition-api'
import type { ComputedRef } from '@nuxtjs/composition-api'

import { ISearchOption, searchFiles } from '../../../../repositories'

const LIMIT_DEFAULT = 10

export default defineComponent({
  name: 'FileComponent',
  props: {
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    },
    userId: {
      type: String || undefined,
      required: true
    },
    uploadOption: {
      type: Object,
      default: () => {}
    },
    searchFilesOption: {
      type: Object,
      default: () => {}
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { i18n } = useContext()
    const fileRules = {
      video: {
        duration: 70
      }
    }
    // const userId: ComputedRef<string> = computed(() => props.userId)
    const internalSearchFilesOption: ComputedRef<ISearchOption> = computed(
      () => {
        const data = props.searchFilesOption
        if (!data?.filter?.user) data.filter.user = props.userId
        return data
      }
    )
    const {
      files: filesList,
      execute: executeSearch,
      total
    } = searchFiles(internalSearchFilesOption)
    // const filesList: Ref<any[]> = ref([])
    const internalUploadOption: ComputedRef<any> = computed(() => {
      const data = props.uploadOption
      if (!data.user) data.user = props.userId
      return data
    })
    const styleComponent: any = {
      border: '2px dashed var(--v-primary-base)'
    }
    const uploadComponent: any[] = [
      {
        type: 'image',
        icon: '$cameraEnhance',
        text: 'image'
      },
      {
        // hot fix for manulife
        type: 'video-audio',
        icon: '$videoPlus',
        text: 'Video, Audio',
        note: i18n.t('max -seconds/video, items(s)', {
          time: 60,
          item: 3
        })
      },
      {
        type: 'file',
        icon: '$file',
        text: 'document'
      }
    ]
    // disable download btn on webview
    const downloadable: ComputedRef<boolean> = computed(() => {
      const userAgent = window.navigator.userAgent
      const isWebview =
        /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)|.*WebView.*|Android.*(wv|WebView)/i.test(
          userAgent
        )
      return !isWebview
    })
    // FIRST LOAD
    onMounted(() => {
      executeSearch()
    })
    // LOGIC
    const recieveData = (_data) => {
      // filesList.value.push(data)
      executeSearch()
    }
    const loadFile = (limit: number = LIMIT_DEFAULT) => {
      internalSearchFilesOption.value.limit = limit
      executeSearch()
    }
    const removeItem = (_index) => {
      // filesList.value.splice(index, 1)
      executeSearch()
    }
    watch(total, () => {
      emit('onTotal', total.value)
    })
    return {
      filesList,
      styleComponent,
      uploadComponent,
      internalUploadOption,
      recieveData,
      removeItem,
      fileRules,
      LIMIT_DEFAULT,
      total,
      loadFile,
      downloadable
    }
  }
})
</script>

<style lang="css">
.w-abs-right {
  position: absolute !important;
  top: 8px;
  right: 8px;
}
</style>
