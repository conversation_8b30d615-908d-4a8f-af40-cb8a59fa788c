<template>
  <div>
    <input
      v-show="false"
      ref="fileInput"
      :accept="acceptString"
      :multiple="multiple"
      type="file"
      @change="onInputChange"
    />
    <slot name="default" :upload="uploadBtn">
      <v-img aspect-ratio="1" class="grey lighten-2 rounded-lg">
        <v-card class="btn-upload fill-height" rounded="lg" @click="uploadBtn">
          <v-card-text
            class="d-flex flex-column align-center justify-center fill-height pa-0"
          >
            <slot name="w-upload-icon">
              <v-icon color="primary" x-large> $cameraEnhance </v-icon>
            </slot>
            <div class="d-flex align-center text-center">
              <strong>
                <slot name="w-upload-text">
                  {{ $t('image') }}
                </slot>
              </strong>
            </div>
          </v-card-text>
        </v-card>
      </v-img>
    </slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@nuxtjs/composition-api'

export default defineComponent({
  setup() {}
})
</script>

<style scoped>
.btn-upload {
  position: relative;
  border: 2px dashed #0096883d;
}
.btn-upload:hover {
  background-color: #0096882d;
}
.btn-upload:not(:last-child) {
  margin-right: 10px;
}
.w-uploading {
  cursor: not-allowed;
}
</style>

function uuid() { throw new Error("Function not implemented."); }
