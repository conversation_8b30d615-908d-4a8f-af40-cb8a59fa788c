<template>
  <v-row :dense="$vuetify.breakpoint.smAndDown">
    <template v-if="!isEmpty">
      <v-col
        v-for="(file, index) in files"
        :key="`file-${index}`"
        class="d-flex child-flex"
        cols="4"
        sm="3"
      >
        <file-item
          :ref="`file-${index}`"
          :index="index"
          :file="file"
          @remove-file="onRemoveFile"
          @update-file="updatedFile"
        ></file-item>
      </v-col>
    </template>
    <template v-if="isUploadingFile">
      <v-col
        v-for="(uploadingFile, iFile) in uploadingFiles"
        :key="`uploadingFile-${iFile}`"
        cols="4"
        sm="3"
      >
        <file-uploading
          :file="uploadingFile"
          :progress="progress"
        ></file-uploading>
      </v-col>
    </template>
    <v-col v-if="isLoading" cols="12" class="d-flex justify-center">
      <loading-dot v-model="isLoading"></loading-dot>
    </v-col>
    <v-col v-if="haveMore" cols="12" class="d-flex justify-center">
      <v-btn
        depressed
        block
        color="white"
        class="rounded-lg none-uppercase"
        @click="onLoadMore"
      >
        <v-icon left v-text="isExpanded ? '$down' : '$up'"></v-icon>
        {{ isExpanded ? $t('see more') : $t('collapse') }}
      </v-btn>
    </v-col>
  </v-row>
</template>
<script lang="ts"></script>
