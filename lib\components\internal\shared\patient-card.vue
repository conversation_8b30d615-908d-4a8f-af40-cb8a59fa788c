<template>
  <v-row :key="'patient-card'">
    <v-col cols="12" sm="6" md="5">
      <v-card v-if="loading" rounded="lg" flat>
        <v-card-text class="py-1">
          <v-skeleton-loader
            type="list-item-avatar-two-line"
          ></v-skeleton-loader>
        </v-card-text>
      </v-card>
      <v-card
        v-else
        flat
        tile
        rounded="lg"
        class="w-card py-2"
        style="
          position: relative;
          box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px -2px !important;
        "
      >
        <v-list-item>
          <v-list-item-avatar class="mr-3" size="46">
            <w-avatar
              size="50"
              :src="getAvatar"
              :name="getName"
              :user-id="getId"
            ></w-avatar>
          </v-list-item-avatar>
          <v-list-item-content class="py-0">
            <v-list-item-title class="text-secondary font-weight-medium">
              <!-- {{ getPatientName(patient) }} -->
              {{ getName }}
            </v-list-item-title>
            <v-list-item-subtitle>
              <!-- {{ $t(getPatientGender(patient)) }}, -->
              {{ $t(getGender) }},
              <!-- {{ $t(patientDob) }} -->
              {{ $t(getDob) }}
            </v-list-item-subtitle>
            <!-- <v-list-item-subtitle v-else class="caption grey--text">
              Thông tin chi tiết
            </v-list-item-subtitle> -->
          </v-list-item-content>
        </v-list-item>
      </v-card>
    </v-col>
    <!-- <v-col cols="12">
      <v-textarea
        :placeholder="$t('medical history')"
        :label="$t('medical history')"
        name="reason"
        class="rounded-lg"
        outlined
      ></v-textarea>
    </v-col> -->
    <!-- <w-dialog v-model="showDialog" :title="'choose patient'">
      <v-card color="#f9f9f9">
        <v-card-text>
          <v-alert
            v-if="error"
            dense
            text
            type="error"
            icon="$information"
            dismissible
            transition="scale-transition"
          >
            <span v-dompurify-html="error"></span>
          </v-alert>
          <patient-item
            v-for="(patient, iPatient) in availablePatientsPrivateDoctor"
            :key="`patient-${iPatient}`"
            :patient="patient"
            :is-active="isActive(patient)"
            :class="{
              'mb-3': iPatient < availablePatientsPrivateDoctor.length - 1
            }"
            @select="onSelectPatient"
          ></patient-item>
        </v-card-text>
      </v-card>
    </w-dialog> -->
    <!-- <w-dialog-alert v-model="showAlert" persistent>
      <template #w-container>
        <v-card flat class="rounded-lg">
          <v-card-text class="pa-4 d-flex align-center justify-space-between">
            <div>
              <p class="title mb-1">Lỗi hệ thống</p>
              {{ errorMsg || $t("system error") }}
            </div>
            <div>
              <v-icon size="56" color="red">$emoticonSad</v-icon>
            </div>
          </v-card-text>
          <v-card-actions class="px-4 pb-3">
            <v-btn
              color="#0096883d"
              depressed
              href="tel:+842836226822"
              large
              block
              class="primary--text rounded-lg none-uppercase"
            >
              Liên hệ hỗ trợ
            </v-btn>
          </v-card-actions>
        </v-card>
      </template>
    </w-dialog-alert> -->
  </v-row>
</template>

<script lang="ts">
import { computed, defineComponent } from '@nuxtjs/composition-api'
import type { ComputedRef } from '@nuxtjs/composition-api'

export default defineComponent({
  name: 'PatientCard',
  props: {
    patientInfo: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const getName: ComputedRef<string> = computed(
      () => props.patientInfo?.name || ''
    )
    const getAvatar: ComputedRef<string> = computed(
      () => props.patientInfo?.avatar || ''
    )
    const getId: ComputedRef<string> = computed(
      () => props.patientInfo?.id || ''
    )
    const getGender: ComputedRef<string> = computed(
      () => props.patientInfo?.gender
    )
    const getDob: ComputedRef<string> = computed(() => props.patientInfo?.dob)
    return {
      getName,
      getAvatar,
      getId,
      getGender,
      getDob
    }
  }
})
</script>

<style scoped></style>
