<template>
  <client-only>
    <component
      :is="currentStep.component"
      ref="pgroup"
      :order="order"
      :order-item="orderItem"
      :order-items="orderItems"
      :current-step="currentStep"
      v-bind="{ ...$props }"
      @emit-data-record="receiveDataFromStep"
      @submit="(metaPayload, canPay) => $emit('submit', metaPayload, canPay)"
      @set-order-item="$emit('set-order-item', $event)"
      @set-step-meta="$emit('set-step-meta', $event)"
    />
  </client-only>
</template>
<script lang="ts">
import { defineComponent, ref, useContext } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'
import {
  IOrder,
  IOrderItem,
  IStep,
  useProductOrder
} from '../../../repositories/index'
// import { useVuelidate } from '@vuelidate/core'
import ChooseAddress from '../forms/address/index.vue'
import ChooseClinic from '../forms/choose-clinic.vue'
import ChooseExpertVerify from '../forms/choose-expert-verify.vue'
import ChooseKnowledgeProvider from '../forms/knowledge-question/choose-knowledge-provider.vue'
import ChooseLanguage from '../forms/choose-language.vue'
import ChooseMedium from '../forms/choose-medium.vue'
import ChooseMembership from '../forms/choose-membership/index.vue'
import ChooseOption from '../forms/knowledge-question/choose-option.vue'
import ChoosePatient from '../forms/choose-patient/index.vue'
import ChooseProvider from '../forms/choose-provider/index.vue'
import ChooseProviderTimeslot from '../forms/choose-provider-timeslot/index.vue'
import ChooseRequestTime from '../forms/choose-request-time.vue'
import ConfirmQuestion from '../forms/confirm-question.vue'
import KnowledgeQuestion from '../forms/knowledge-question/index.vue'
import MedicineFullfillmentType from '../forms/medicine-fullfillment-type.vue'
import MedicinePendingDelivery from '../forms/medicine-pending-delivery/index.vue'
import MedicalHistory from '../forms/medical-history/index.vue'
import MembershipAddon from '../forms/membership-addon.vue'
import MembershipChoosePatientPersonalDoctor from '../forms/membership-choose-patient-personal-doctor/index.vue'
import PregnancyDiary from '../forms/pregnancy-diary/index.vue'
import PrepareFirstMeeting from '../forms/prepare-first-meeting.vue'
import PrepareMedicalHistory from '../forms/prepare-medical-history.vue'
import PrepareMedicalRecord from '../forms/prepare-medical-record.vue'
import Question from '../forms/question.vue'
import RegisterMember from '../forms/register-member/index.vue'
import RegisterPrivateProvider from '../forms/register-private-provider.vue'
import RequestVerify from '../forms/request-verify.vue'
import Skeleton from '../forms/skeleton.vue'
import BodyIndex from '../forms/body-index/index.vue'
import updateOrderItemV2 from '../../../repositories/order-item/update-oder-item-v2'
import ChooseTarget from '../forms/choose-target.vue'
import ChooseReason from '../forms/choose-reason.vue'
import ChooseExpectations from '../forms/choose-expectations.vue'
import ChoosePackages from '../forms/choose-packages.vue'
import PersonalDoctorPackageConfirm from '../forms/personal-doctor-package-confirm.vue'
import ProviderProfileBySlug from '../forms/provider-profile-by-slug.vue'
import AddonsPersonalDoctor from '../forms/addons-personal-doctor/index.vue'
import ChoosePatientPersonalDoctor from '../forms/choose-patient-personal-doctor/index.vue'
import ChoosePersonalDoctor from '../forms/choose-personal-doctor/index.vue'
import ChooseBaby from '../forms/choose-baby/index.vue'
import ConfirmVerification from '../forms/confirm-verification/index.vue'

export default defineComponent({
  components: {
    ChooseReason,
    ChooseTarget,
    ChooseExpectations,
    ChoosePackages,
    AddonsPersonalDoctor,
    ChoosePatientPersonalDoctor,
    ChoosePersonalDoctor,
    ChooseAddress,
    ChooseClinic,
    ChooseExpertVerify,
    ChooseKnowledgeProvider,
    ChooseLanguage,
    ChooseMedium,
    ChooseMembership,
    ChooseOption,
    ChoosePatient,
    ChooseProvider,
    ChooseProviderTimeslot,
    ChooseRequestTime,
    ConfirmQuestion,
    KnowledgeQuestion,
    MedicineFullfillmentType,
    MedicinePendingDelivery,
    MedicalHistory,
    MembershipAddon,
    MembershipChoosePatientPersonalDoctor,
    PregnancyDiary,
    PrepareFirstMeeting,
    PrepareMedicalHistory,
    PrepareMedicalRecord,
    // TODO: rewrite questions
    Question,
    RegisterMember,
    RegisterPrivateProvider,
    RequestVerify,
    Skeleton,
    BodyIndex,
    ChooseBaby,
    PersonalDoctorPackageConfirm,
    ProviderProfileBySlug,
    ConfirmVerification
  },
  props: {
    order: {
      type: Object as PropType<IOrder>,
      required: true
    },
    orderItem: {
      type: Object as PropType<IOrderItem<any>>,
      required: true
    },
    orderItems: {
      type: Array as PropType<Array<IOrderItem<any>>>,
      default: []
    },
    currentStep: {
      type: Object as PropType<IStep>,
      required: true
    }
  },
  setup(_props, { emit }) {
    const pgroup = ref()
    const submit = () => {
      pgroup.value.submit()
    }
    const dataUpdate = ref()
    const { orderItem } = useProductOrder()
    const { execute } = updateOrderItemV2(dataUpdate)
    const { $toast } = useContext()
    const validate = () => {
      // if product step is form
      if (pgroup.value.v$) {
        pgroup.value.v$.$touch()
        return new Promise((resolve, reject) => {
          pgroup.value.v$
            .$validate()
            .then((check) => {
              if (!check) $toast.error('An error has occur')
              resolve(check)
            })
            .catch(() => reject(new Error('error')))
        })
      }
      // if not, create function touch in component and validate it
      else
        return new Promise((resolve, _reject) => {
          return resolve(pgroup.value?.touch())
        })
    }
    const goToPayment = () => {
      return pgroup.value?.goToPayment()
    }
    const receiveDataFromStep = (value) => {
      const meta = Object.assign({}, orderItem.value.meta)
      const orderItemClone = Object.assign({}, orderItem.value)
      Object.keys(value).forEach((key) => {
        meta[key] = value[key]
      })

      orderItemClone.meta = meta
      dataUpdate.value = {
        orderItemId: orderItem.value?._id,
        data: orderItemClone
      }
      updateOrderItem()
    }
    const debounce = (fn, delay = 0, immediate = false) => {
      let timeout
      return (...args) => {
        if (immediate && !timeout) fn(...args)
        clearTimeout(timeout)

        timeout = setTimeout(() => {
          fn(...args)
        }, delay)
      }
    }
    const updateOrderItem = debounce(() => {
      execute()
    }, 500)

    const onSubmit = (payloadMeta, canpay) => {
      emit('submit', payloadMeta, canpay)
    }
    return {
      pgroup,
      validate,
      submit,
      goToPayment,
      receiveDataFromStep,
      debounce,
      updateOrderItem,
      onSubmit
    }
  }
})
</script>
