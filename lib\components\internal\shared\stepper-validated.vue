<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  useStore,
  watch,
  nextTick
} from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

import BodyIndex from '../forms/medical-history/body-index.vue'
import PrepareMedicalRecord from '../forms/medical-history/prepare-medical-record.vue'
import Vaccination from '../forms/medical-history/vaccination.vue'
import StepBodyIndex from '../forms/body-index/form.vue'
import { IStepperItems } from '../../../models'

export default defineComponent({
  name: 'StepperValidated',
  components: { BodyIndex, PrepareMedicalRecord, Vaccination, StepBodyIndex },
  props: {
    items: {
      type: Array as PropType<IStepperItems[]>,
      default: () => [] as IStepperItems[]
    },
    order: {
      type: Object,
      required: true
    },
    orderItem: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const { state } = useStore()

    const componentRef = ref<any>(null) // Adjust type if possible
    const stepper = ref<number>(
      props.items.findIndex((item) => item?.isFirstOpen === true) + 1
    )
    const stepperItems = ref<IStepperItems[]>(props.items)

    const stepperValidDatedState = computed(
      () => (state as any)?.checkout?.stepperValidDated
    )

    const isDone = computed<boolean>(() =>
      stepperItems.value.every((item) => item.isDone === true || false)
    )

    const setState = (
      val: boolean = true,
      position: number = stepper.value - 1
    ) => {
      stepperItems.value = stepperItems.value.map((currentValue, index) =>
        index === position ? { ...currentValue, isDone: val } : currentValue
      )
    }

    const valid = async () => {
      // Check if stepper is invalid and validate all steps
      if (stepper.value === -1) {
        for (const i in stepperItems.value) {
          const isCheck = await componentRef.value[i]?.submit()

          // If any step is not valid, set the stepper to that step and return false
          if (!isCheck) {
            stepper.value = Number(i) + 1
            setState(false)
            return false
          }
        }
      }

      // Find the current step index
      const step = componentRef.value[stepper.value - 1]
        ? componentRef.value.findIndex(
            (item: any) =>
              stepperItems.value[stepper.value - 1]?.label === item?.name
          )
        : componentRef.value.length

      // console.log(step)

      // Call submit method of the current step and return the result
      const valid = await componentRef.value[step]?.submit()

      // Return the result of the submit method
      return Boolean(valid)
    }

    const next = async () => {
      const itemsLength = Array.isArray(props.items) ? props.items.length : 1
      const validForm = await valid()

      if (validForm) {
        // If the form is valid, mark the current step as done and move to the next step
        setState()
      } else if (stepper.value === -1) {
        // If we are at the end of the stepper (all steps are done), return true to indicate it's done
        return isDone.value
      } else {
        // If the form is not valid, return nothing to indicate the user should stay in the current step
        return
      }

      stepper.value =
        stepper.value < itemsLength
          ? stepper.value + 1
          : (stepper.value % itemsLength) + 1

      const stepperItem = stepperItems.value[stepper.value - 1]

      if (stepperItem?.isDone) {
        // If the next step is already done, find the first incomplete step and move to it
        stepper.value =
          stepperItems.value.findIndex(
            (item) => !('isDone' in item) || item.isDone === false
          ) + 1
      }

      if (isDone.value) {
        // If we have reached the end of the stepper, mark it as done
        stepper.value = -1
      }
    }

    watch(stepper, async (_newVal, oldVal) => {
      const step = oldVal - 1

      const isValid = await componentRef.value[step]?.submit()

      if (isValid) {
        setState(true, step)
      }
    })

    nextTick(() => {
      const isSaveStep = props.items.every((item) => {
        return stepperValidDatedState.value[item.key].summarize
      })

      if (isSaveStep && !stepper.value) {
        stepperItems.value = stepperItems.value.map((item) => ({
          ...item,
          isDone: true
        }))
        stepper.value = -1
        next()
      }
    })

    return {
      stepper,
      stepperItems,
      componentRef,
      isDone,
      next,
      stepperValidDatedState
    }
  }
})
</script>

<template>
  <v-stepper
    v-model="stepper"
    vertical
    flat
    rounded="xl"
    :style="{
      backgroundColor: 'transparent'
    }"
  >
    <template v-for="(item, i) in stepperItems">
      <v-stepper-step
        :key="i"
        :complete="item.isDone"
        :step="i + 1"
        editable
        class="px-1"
        edit-icon="$check"
      >
        <div
          class="d-flex justify-space-between align-center font-weight-black"
        >
          <div>
            <div class="d-flex align-center">
              <p class="mb-0">
                <span class="text-capitalize" v-html="$t(item.label)"></span
                ><span v-if="item.required" class="red--text">*</span>
              </p>
              <v-tooltip
                v-if="item.info"
                top
                max-width="400"
                color="primary"
                close-delay="500"
              >
                <template #activator="{ on, attrs }">
                  <v-icon class="ml-1" size="20" v-bind="attrs" v-on="on"
                    >$informationOutline</v-icon
                  >
                </template>
                <span>{{ $t(item.info) }}</span>
              </v-tooltip>
            </div>
            <small
              v-if="stepperValidDatedState[item.key].summarize"
              class="mt-1"
              >{{ $t(stepperValidDatedState[item.key].summarize) }}</small
            >
          </div>
        </div>
      </v-stepper-step>

      <v-stepper-content :key="'content' + i" :step="i + 1" class="ml-4 pr-10">
        <component
          :is="item.component"
          ref="componentRef"
          :label="item.label"
          :order="order"
          :order-item="orderItem"
          v-bind="{ ...item.props, type: item.key }"
        />
      </v-stepper-content>
    </template>
  </v-stepper>
</template>
