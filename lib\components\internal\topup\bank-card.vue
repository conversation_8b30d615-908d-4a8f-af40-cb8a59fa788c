<template>
  <v-card-text>
    <v-row class="noselect" dense>
      <v-col cols="12" class="mb-2">
        <p class="font-weight-bold mb-2 text--secondary text-18">
          1. {{ $t('enter a valid amount of money') }}
        </p>
        <v-text-field
          outlined
          :value="format"
          :hint="hint"
          :readonly="model !== suggestions.length - 1"
          :style="{
            'pointer-events: none !important': model !== suggestions.length - 1
          }"
          type="text"
          inputmode="numeric"
          hide-spin-buttons
          :placeholder="$t('choose an amount')"
          hide-details="auto"
          suffix="đ"
          @keydown="onKeyDown"
          @input="onTopupTotalChange($event)"
        >
        </v-text-field>

        <v-chip-group
          v-model="model"
          :mandatory="mandatory"
          @change="onSuggestionChange($event)"
        >
          <v-chip
            v-for="(suggest, index) in suggestions"
            :key="suggest.index + suggest.text"
            class="rounded-lg"
            :class="
              index === model
                ? 'suggestion-active'
                : 'suggestion-normal ' + suggest.class
            "
            >{{ suggest.text }}</v-chip
          >
        </v-chip-group>
      </v-col>

      <v-col cols="12" class="mb-2">
        <p class="font-weight-bold mb-2 text--secondary text-18">
          2. {{ $t('select card type') }}
        </p>
        <v-alert
          v-show="order && order._id"
          border="left"
          color="primary"
          colored-border
          class="font-weight-medium text-justify py-0 mb-0"
        >
          <span v-dompurify-html="$t('debit card description')"></span>
        </v-alert>
      </v-col>
      <v-col v-for="item of bankTypes" :key="item.value" cols="12">
        <v-card
          class="cursor"
          rounded="lg"
          outlined
          style="cursor: pointer"
          :class="item.value === bankType ? 'w-shadow active' : 'elevation-0'"
          @click.passive="
            bankType = item.value
            ATMBankCode = null
          "
        >
          <v-card-text
            :class="{ 'primary--text': item.value === bankType }"
            class="d-flex align-center justify-space-between pa-3"
          >
            <div class="d-flex align-center">
              <v-icon v-if="item.value === bankType" color="primary"
                >$check</v-icon
              >
              <v-icon v-else>$checkboxOff</v-icon>
              <div class="d-flex flex-column ml-2">
                <span class="text-16 font-weight-bold">{{ item.text }}</span>
                <span class="text-16">{{ item.description }}</span>
              </div>
            </div>
            <div v-if="item.imgs && item.imgs.length" class="d-flex">
              <div v-for="itemImg in item.imgs" :key="itemImg" class="mr-2">
                <v-img
                  contain
                  max-width="min(15vw, 60px)"
                  :src="itemImg"
                  class="grey lighten-4"
                >
                  <template #placeholder>
                    <v-row
                      class="fill-height ma-0"
                      align="center"
                      justify="center"
                    >
                      <v-progress-circular
                        indeterminate
                        color="grey lighten-5"
                      ></v-progress-circular>
                    </v-row>
                  </template>
                </v-img>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col v-if="isValid && bankType === 'debit'" cols="12">
        <p class="font-weight-bold mb-2 text--secondary text-18">
          3. {{ $t('choose a bank') }}
        </p>
        <v-row>
          <template v-for="item of ATMBanks">
            <v-col :key="'col-' + item.value" cols="4" md="3">
              <v-hover v-slot="{ hover }" :key="item.value">
                <v-card
                  class="pointer py-1 d-flex justify-center animated zoomIn fast"
                  :class="
                    ATMBankCode === item.value || hover ? 'w-shadow active' : ''
                  "
                  outlined
                  style="cursor: pointer"
                  @click.passive="ATMBankCode = item.value"
                >
                  <!-- <img
                      src="/checkout/payment/123PACB.png"
                      class="img animated zoomIn fast"
                    /> -->
                  <img :src="item.img" class="img animated zoomIn fast" />
                  <v-icon
                    v-if="ATMBankCode === item.value"
                    class="check-icon"
                    color="primary"
                    small
                    >$check</v-icon
                  >
                </v-card>
              </v-hover>
            </v-col>
          </template>
        </v-row>
        <div id="debit-cards"></div>
      </v-col>
    </v-row>
    <v-footer
      class="d-flex justify-center"
      color="white"
      :fixed="$vuetify.breakpoint.xsOnly"
    >
      <v-btn
        color="#FF4500"
        class="text-capitalize white--text rounded-pill py-6"
        depressed
        block
        tile
        :disabled="
          (bankType === 'credit' ? !bankType : !ATMBankCode) || !isValid
        "
        @click="paymentRequest"
        ><span
          ><strong>{{ $t('continue') }}</strong></span
        ></v-btn
      >
    </v-footer>
  </v-card-text>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useStore,
  computed,
  watch,
  useContext,
  reactive
} from '@nuxtjs/composition-api'
import DOMPurify from 'dompurify'
import { usePayment } from '../../../repositories/payment/use-payment'
import { useBanks } from './use-banks'

export default defineComponent({
  props: {
    order: {
      type: Object,
      default: () => {}
    },
    currentWallet: {
      type: Object,
      default: () => {}
    },
    returnUrl: {
      type: String,
      default: '/'
    }
  },
  setup(props: any, ctx: any) {
    const MINIMUM_TOPUP = 20000
    const { app, i18n, $config } = useContext()
    const isEnabledDeeplink = $config?.checkout?.deeplink?.enabled
    const deeplinkScheme = $config?.checkout?.deeplink?.scheme
    // eslint-disable-next-line camelcase
    const return_url = props.order?._id
      ? new URL(
          isEnabledDeeplink
            ? `${deeplinkScheme}payment/order/${props.order._id}/processing`
            : `/payment/order/${props.order._id}/processing`,
          process.client ? window.location?.origin : ''
        )
      : new URL(
          isEnabledDeeplink
            ? `${deeplinkScheme}${props.returnUrl}`
            : `/${props.returnUrl}`,
          process.client ? window.location?.origin : ''
        )
    const format = ref('')
    const hint = ref('')
    const mandatory = ref(false)
    const model = ref()
    const isValid = ref(false)
    watch(format, () => {
      const tempMoney = Number(
        DOMPurify.sanitize(format.value.replace(/\D/g, ''))
      )

      if (tempMoney >= MINIMUM_TOPUP) {
        isValid.value = true
      } else {
        isValid.value = false
      }
    })
    const minimumTopup = computed(() => {
      if (!props.order?._id) return MINIMUM_TOPUP
      return props.order.total - props.currentWallet.usable < MINIMUM_TOPUP
        ? MINIMUM_TOPUP
        : props.order.total - props.currentWallet.usable
    })
    const onTopupTotalChange = (value: any) => {
      if (!value) return
      const noLetter = DOMPurify.sanitize(value.replace(/\D/g, ''))
      format.value = i18n.n(parseInt(noLetter.replace(/\./g, '')))
      topupTotal.value = parseInt(noLetter.replace(/\./g, ''))
    }
    const topupTotal = ref(minimumTopup.value)
    const suggestions = reactive([
      {
        text: `${i18n.n(minimumTopup.value)}đ`,
        value: minimumTopup.value,
        class: ''
      },
      {
        text: `${i18n.n(minimumTopup.value + 1000000)}đ`,
        value: minimumTopup.value + 1000000,
        class: ''
      },
      { text: i18n.t('enter a number'), value: null, class: 'other' }
    ])
    const onSuggestionChange = (index: number) => {
      if (index === suggestions.length - 1) {
        format.value = ''
        return
      }
      mandatory.value = true
      format.value = i18n.n(suggestions[index].value)
      topupTotal.value = suggestions[index].value
    }
    const { state }: any = useStore()
    const bankType = ref(null)
    const bankTypes = ref([
      {
        value: 'credit',
        text: 'Visa/Master',
        description: 'Thẻ quốc tế',
        imgs: [
          'https://apis.wellcare.vn/file-proxy/cms-gallery/6209db65003d0a753d2ed5ec/aca5bbb2d606aa8a444ff5a072c29fd5.png',
          'https://apis.wellcare.vn/file-proxy/cms-gallery/6209dc33e0ab5435eec39481/8b766daa2cf886a3c4513376088f9404.png'
        ]
      },
      {
        value: 'debit',
        text: 'ATM',
        description: 'Thẻ ngân hàng nội địa',
        imgs: [
          'https://apis.wellcare.vn/file-proxy/cms-gallery/6209ebd3003d0a329f2ed5ee/napas-card.png'
        ]
      }
    ])
    const { BANKS } = useBanks()
    const ATMBankCode = ref('')
    const ATMBanks = ref(BANKS)
    const topupRequest = ref({
      app_id: null,
      amount: null,
      user: null,
      phone: null,
      name: null,
      return_url: null,
      metadata: null,
      bankCode: null
    })
    const { createZalopayPayment, onZalopaySuccess, onZalopayError } =
      usePayment(topupRequest)
    const paymentRequest = () => {
      topupRequest.value = {
        app_id: app.$config.payment?.gatewayId || '60bf536592b7cf4c8de14755',
        amount:
          topupTotal.value < MINIMUM_TOPUP ? MINIMUM_TOPUP : topupTotal.value,
        phone: state.authen?.user?.phone,
        user: state.authen?.user?._id,
        name: state.authen?.user?.name,
        return_url: return_url.href,
        bankCode: ATMBankCode.value || 'CC',
        metadata: {
          user: state.authen?.user?._id,
          order: props.order?._id || null,
          description: 'Nap tien so kham online cho ' + state.authen?.user?.name
        }
      }
      ctx.root.$loading.start()
      createZalopayPayment()
    }
    onZalopaySuccess(() => ctx.root.$loading?.finish())
    onZalopayError(() => ctx.root.$loading?.finish())
    watch(topupTotal, () => {
      topupRequest.value.amount = topupTotal.value
      if (topupTotal.value < minimumTopup.value) {
        hint.value =
          i18n.t('minimum top up') +
          ` ${
            minimumTopup.value < MINIMUM_TOPUP
              ? i18n.n(MINIMUM_TOPUP)
              : i18n.n(minimumTopup.value)
          }đ`
      } else hint.value = ''
    })

    const onKeyDown = (event: any) => {
      const keyCode = event.keyCode || event.which
      if (
        !(
          keyCode === 8 ||
          (keyCode >= 37 && keyCode <= 40) ||
          (keyCode >= 48 && keyCode <= 57) ||
          (keyCode >= 96 && keyCode <= 105)
        )
      ) {
        event.preventDefault()
      }
    }

    return {
      bankType,
      bankTypes,
      ATMBankCode,
      ATMBanks,
      paymentRequest,
      topupTotal,
      minimumTopup,
      suggestions,
      model,
      onSuggestionChange,
      mandatory,
      onTopupTotalChange,
      format,
      hint,
      isValid,
      onKeyDown
    }
  }
})
</script>

<style scoped>
.img-bank {
  padding: 10px;
  display: inherit;
  transition: 100ms;
  cursor: pointer;
}

.check-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: white;
}
.w-shadow {
  box-shadow: 0px 5px 10px rgb(0 0 0 / 5%) !important;
}
.w-shadow.active {
  border: 1px solid teal;
  box-shadow: 0px 5px 12px rgb(0 0 0 / 25%) !important;
}
.w-shadow::before {
  border-radius: 8px;
}
.fixed-btn {
  position: fixed;
  bottom: 10px;
  left: 10px;
  width: calc(100% - 20px);
}
.suggestion-active {
  background-color: var(--v-primary-base) !important;
  color: white !important;
}
.suggestion-normal {
  background-color: transparent !important;
  border: 1px solid var(--v-primary-base);
}
</style>
