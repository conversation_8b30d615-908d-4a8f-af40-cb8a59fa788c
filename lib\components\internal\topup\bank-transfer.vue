<template>
  <v-card-text>
    <v-row no-gutters>
      <v-col cols="12">
        <p class="font-weight-bold text--secondary text-18">
          1. {{ $t('bank transfer description') }}:
        </p>
        <div class="d-flex justify-center">
          <v-card
            elevation="0"
            class="animated infinite pulse border-card"
            rounded="lg"
            @click="copyContent(transferContent)"
          >
            <v-card-text class="headline py-2 primary--text">
              {{ transferContent }}
            </v-card-text>
          </v-card>
        </div>
        <v-subheader class="font-weight-bold pt-3 px-0 text-18"
          >2. {{ $t('transfer to') }}:
        </v-subheader>
        <div class="pl-3">
          <div v-for="(bank, index) of banks" :key="'bank-item-' + index">
            <div class="py-2 text-16 font-weight-bold">
              {{ bank.beneficiary }}
              <a @click="copyContent(bank.number)">
                {{ bank.number }}
                <v-icon size="18px" color="primary">$content-copy</v-icon>
              </a>
              {{ bank.bankName }}
            </div>
          </div>
        </div>
      </v-col>

      <v-col v-show="order && order._id" cols="12">
        <v-subheader class="text-18 font-weight-bold pt-3 px-0"
          >3. {{ $t('instruction step') }}:</v-subheader
        >
        <v-alert
          border="left"
          color="primary"
          colored-border
          class="body-2 font-weight-medium text-justify py-0 mb-0"
        >
          <span
            v-dompurify-html="
              $t(
                'make the payment transfer from your bank. Input your mobile phone number as the note of bank transfer content'
              )
            "
          ></span>
        </v-alert>
      </v-col>
    </v-row>
  </v-card-text>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  useContext,
  useRouter
} from '@nuxtjs/composition-api'
import { useClipboard } from '@vueuse/core'
export default defineComponent({
  props: {
    order: {
      type: Object,
      default: () => {}
    },
    user: {
      type: Object,
      default: () => {}
    },
    countdown: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const { $toast, i18n, $config } = useContext()
    const router = useRouter()
    const transferContent = computed(() => props.user.phone)
    const banks = computed(() => $config.checkout?.topup?.bank?.accounts)
    const { copy } = useClipboard()
    const copyContent = (content: string) => {
      copy(content)
        .then(() => {
          $toast.success(i18n.t('copied') + ' ' + content)
        })
        .catch((err) => {
          $toast.error(
            `${i18n.t("can't copy to clipboard").toString()}: ${err.message}`
          )
        })
    }
    const confirmPaid = () => {
      router.push(`/payment/order/${props.order._id}/processing`)
    }

    return {
      copyContent,
      transferContent,
      banks,
      confirmPaid
    }
  }
})
</script>

<style scoped>
.border-card {
  color: #009688 !important;
  border: 2px dashed !important;
  animation: scaleAnimation 2s infinite;
}
@keyframes scaleAnimation {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(0.8);
  }
}
</style>
