<template>
  <v-card-text>
    <v-row>
      <v-list-item>
        <v-list-item-avatar tile>
          <v-img
            class="my-2"
            max-height="70"
            max-width="70"
            src="https://khamtuxa.vn/checkout/payment/cash/icon-momo-shop.svg"
            alt
          ></v-img>
        </v-list-item-avatar>
        <v-list-item-content class="text-18 font-weight-bold">
          <span>{{ $t('store and supermarkets accept momo') }}</span>
          <v-list-item-subtitle class="text-wrap">
            Ministop, Th<PERSON> giới di động, <PERSON><PERSON>ệ<PERSON> má<PERSON> xanh, Circle K...
          </v-list-item-subtitle>
        </v-list-item-content>
      </v-list-item>

      <v-col id="main-momo" class="mt-4 px-0 py-0" cols="12">
        <v-subheader class="text-uppercase font-weight-bold text-18"
          >{{ $t('instruction step') }}:</v-subheader
        >
        <p class="text-18 font-weight-bold mx-4 text-justify">
          <strong>{{ $t('step') }} 1:</strong>
          {{ $t('find momo topup locations (see Guide below)') }}
        </p>
        <p class="text-18 font-weight-bold mx-4 text-justify">
          <strong>{{ $t('step') }} 2:</strong>
          {{ $t('topup to Momo phone number') }}.
          <a href="tel:+84366905905">+84 366 905 905</a>
          {{ $t('then text or call') }}
          <a href="tel:+84366905905">+84 366 905 905</a>
        </p>
        <p class="text-18 font-weight-bold mx-4 text-justify">
          <strong>{{ $t('step') }} 3:</strong>
          {{ $t('momo success') }}
          <v-alert
            border="left"
            color="primary"
            colored-border
            class="text-16 font-weight-medium text-justify py-0 mb-0 mt-2"
          >
            <span v-dompurify-html="$t('momo success description')"></span>
          </v-alert>
        </p>
        <p class="text-16 mx-4 text-justify font-weight-bold">
          {{ $t('find momo topup locations') }}
          <a href="https://momo.vn/timdiemgiaodich" target="_blank"
            >{{ $t('here') }}.</a
          >
        </p>
      </v-col>
    </v-row>
  </v-card-text>
</template>
<script lang="ts">
import { defineComponent, ref } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    order: {
      type: Object,
      default: () => {}
    },
    returnUrl: {
      type: String,
      default: '/'
    }
  },
  setup(_props) {
    const cashType = ref('')
    const chooseCashType = (type) => {
      cashType.value = type
    }
    return { cashType, chooseCashType }
  }
})
</script>

<style scoped>
.border-card {
  color: #009688 !important;
  border: 2px dashed !important;
}
</style>
