<template>
  <v-container>
    <v-row class="text-xs-center" align="center" justify="center">
      <v-col cols="12">
        <v-card flat>
          <div class="my-1 font-weight-bold">
            {{ $t('enter a valid amount of money') }}
          </div>
          <v-text-field
            outlined
            :value="format"
            :hint="hint"
            :readonly="model !== suggestions.length - 1"
            :style="{
              'pointer-events: none !important':
                model !== suggestions.length - 1
            }"
            type="text"
            inputmode="numeric"
            hide-spin-buttons
            :placeholder="$t('choose an amount')"
            hide-details="auto"
            suffix="đ"
            @keydown="onKeyDown"
            @input="onTopupTotalChange($event)"
          >
          </v-text-field>
          <v-chip-group
            v-model="model"
            :mandatory="mandatory"
            @change="onSuggestionChange($event)"
          >
            <v-chip
              v-for="(suggest, index) in suggestions"
              :key="suggest.index + suggest.text"
              class="rounded-lg"
              :class="index === model ? 'active' : 'normal ' + suggest.class"
              >{{ suggest.text }}</v-chip
            >
          </v-chip-group>
        </v-card>

        <v-alert
          v-show="order && order._id"
          border="left"
          color="primary"
          colored-border
          class="font-weight-medium text-justify py-0 my-4"
        >
          <span v-dompurify-html="$t('debit card description')"></span>
        </v-alert>
      </v-col>
    </v-row>

    <v-footer
      class="d-flex justify-center"
      color="transparent"
      :fixed="$vuetify.breakpoint.xsOnly"
    >
      <v-btn
        block
        color="#FF4500"
        class="text-capitalize white--text rounded-pill py-6"
        depressed
        tile
        :disabled="!isValid"
        @click="startPaymentProcess"
        ><span
          ><strong>{{ $t('continue') }}</strong></span
        ></v-btn
      >
    </v-footer>
  </v-container>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  useStore,
  computed,
  watch,
  useContext,
  reactive
} from '@nuxtjs/composition-api'
import DOMPurify from 'dompurify'
import { usePayment } from '../../../repositories/payment/use-payment'

export default defineComponent({
  props: {
    order: {
      type: Object,
      default: () => {}
    },
    currentWallet: {
      type: Object,
      default: () => {}
    },
    returnUrl: {
      type: String,
      default: '/'
    }
  },
  setup(props, ctx: any) {
    const MINIMUM_TOPUP = 20000
    const { state }: any = useStore()
    const { i18n, $config } = useContext()
    const isEnabledDeeplink = $config?.checkout?.deeplink?.enabled
    const deeplinkScheme = $config?.checkout?.deeplink?.scheme
    const hint = ref()
    const mandatory = ref(false)
    const model = ref()
    const format = ref()
    // eslint-disable-next-line camelcase
    const return_url = props.order?._id
      ? new URL(
          isEnabledDeeplink
            ? `${deeplinkScheme}payment/order/${props.order._id}/processing`
            : `/payment/order/${props.order._id}/processing`,
          process.client ? window.location?.origin : ''
        )
      : new URL(
          isEnabledDeeplink
            ? `${deeplinkScheme}${props.returnUrl}`
            : `/${props.returnUrl}`,
          process.client ? window.location?.origin : ''
        )

    const minimumTopup = computed(() => {
      if (!props.order?._id) return MINIMUM_TOPUP
      return props.order.total - props.currentWallet.usable < MINIMUM_TOPUP
        ? MINIMUM_TOPUP
        : props.order.total - props.currentWallet.usable
    })
    const topupTotal = ref()
    const topupRequest = ref({
      amount:
        minimumTopup.value < MINIMUM_TOPUP ? MINIMUM_TOPUP : minimumTopup.value,
      phone: state.authen?.user?.phone,
      return_url,
      user: state.authen?.user?._id,
      name: state.authen?.user?.name,
      metadata: {
        user: state.authen?.user?._id,
        order: props.order?._id || null,
        description: 'Nap tien so kham online cho ' + state.authen?.user?.name
      }
    })
    const suggestions = reactive([
      {
        text: `${i18n.n(minimumTopup.value)}đ`,
        value: minimumTopup.value,
        class: ''
      },
      {
        text: `${i18n.n(minimumTopup.value + 1000000)}đ`,
        value: minimumTopup.value + 1000000,
        class: ''
      },
      { text: i18n.t('enter a number'), value: null, class: 'other' }
    ])
    const onSuggestionChange = (index: number) => {
      if (index === suggestions.length - 1) {
        format.value = ''
        return
      }
      mandatory.value = true
      format.value = i18n.n(suggestions[index].value)
      topupTotal.value = suggestions[index].value
    }

    const { createMomoPayment, onMomoSuccess, onMomoError } =
      usePayment(topupRequest)

    const startPaymentProcess = () => {
      if (topupRequest.value.amount < minimumTopup.value)
        topupRequest.value.amount = minimumTopup.value
      ctx.root.$loading?.start()
      createMomoPayment()
    }
    onMomoSuccess(() => ctx.root.$loading?.finish())
    onMomoError(() => ctx.root.$loading?.finish())
    const onTopupTotalChange = (value: any) => {
      if (!value) return
      const noLetter = DOMPurify.sanitize(value.replace(/\D/g, ''))
      format.value = i18n.n(parseInt(noLetter.replace(/\./g, '')))
      topupTotal.value = parseInt(noLetter.replace(/\./g, ''))
    }
    watch(topupTotal, () => {
      topupRequest.value.amount = topupTotal.value
      if (topupTotal.value < minimumTopup.value)
        hint.value =
          i18n.t('minimum top up') +
          ` ${
            minimumTopup.value < MINIMUM_TOPUP
              ? i18n.n(MINIMUM_TOPUP)
              : i18n.n(minimumTopup.value)
          }đ`
      else hint.value = ''
    })

    const isValid = ref(false)
    watch(format, () => {
      const tempMoney = Number(
        DOMPurify.sanitize(format.value.replace(/\D/g, ''))
      )

      if (tempMoney >= MINIMUM_TOPUP) {
        isValid.value = true
      } else {
        isValid.value = false
      }
    })

    const onKeyDown = (event: any) => {
      const keyCode = event.keyCode || event.which
      if (
        !(
          keyCode === 8 ||
          (keyCode >= 37 && keyCode <= 40) ||
          (keyCode >= 48 && keyCode <= 57) ||
          (keyCode >= 96 && keyCode <= 105)
        )
      ) {
        event.preventDefault()
      }
    }
    return {
      topupTotal,
      minimumTopup,
      hint,
      startPaymentProcess,
      suggestions,
      model,
      onSuggestionChange,
      mandatory,
      onTopupTotalChange,
      format,
      isValid,
      onKeyDown
    }
  }
})
</script>
<style scoped>
.active {
  background-color: var(--v-primary-base) !important;
  color: white !important;
}
.normal {
  background-color: transparent !important;
  border: 1px solid var(--v-primary-base);
}
</style>
