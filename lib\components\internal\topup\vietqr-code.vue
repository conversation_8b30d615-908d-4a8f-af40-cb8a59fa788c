<template>
  <div v-if="qrCodeUrl">
    <img
      :src="qrCodeUrl"
      alt="Mã QR thanh toán"
      :style="{ maxWidth: '300px', objectFit: 'cover', borderRadius: '12px' }"
    />
  </div>
</template>
<script>
import { computed, defineComponent } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    bankId: {
      type: String,
      required: true
    },
    accountNo: {
      type: String,
      required: true
    },
    template: {
      type: String,
      default: 'compact2'
    },
    amount: {
      type: Number,
      default: 0
    },
    description: {
      type: String,
      default: ''
    },
    accountName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const qrCodeUrl = computed(() => {
      let url = `https://img.vietqr.io/image/${props.bankId}-${props.accountNo}-${props.template}.png`
      const params = new URLSearchParams()

      if (props.amount > 0) params.append('amount', props.amount)
      if (props.description)
        params.append('addInfo', encodeURIComponent(props.description))
      if (props.accountName) params.append('accountName', props.accountName)

      if (params.toString()) url += `?${params.toString()}`
      return url
    })

    return {
      qrCodeUrl
    }
  }
})
</script>
