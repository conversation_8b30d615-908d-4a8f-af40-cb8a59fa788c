<template>
  <div
    @mousedown="isMouseDown = true"
    @mouseup="isMouseDown = false"
    @mouseleave="isMouseDown = false"
    @mousemove="onWindowSwiping"
  >
    <v-sheet
      class="mx-auto"
      elevation="0"
      style="background-color: transparent !important"
      max-width="650"
    >
      <v-slide-group
        ref="walletSlider"
        v-model="walletIndex"
        mandatory
        center-active
        active-class="success"
        class="d-flex align-center"
        style="overflow: visible !important; height: 175px"
        @change="onCardChange"
      >
        <v-slide-item
          v-for="(wallet, index) in wallets"
          :key="wallet.title + index"
          v-slot="{ active, toggle }"
        >
          <w-wallet-card
            :member-service="wallet"
            :activate="walletIndex === index"
            :class="{
              activate: active,
              'mx-auto': wallets.length === 1
            }"
            @chooseCard="toggle"
          />
        </v-slide-item>
      </v-slide-group>
      <div
        v-if="wallets.length > 1"
        class="d-flex align-center justify-center mt-3"
      >
        <v-icon
          v-for="n in wallets.length"
          :key="'select' + n"
          size="12"
          class="mx-2"
          :color="n === walletIndex + 1 ? '#9ca3af' : 'grey lighten-2'"
        >
          $circle
        </v-icon>
      </div>
    </v-sheet>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, computed, watch } from '@nuxtjs/composition-api'
export default defineComponent({
  props: {
    wallets: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    const isMouseDown = ref(false)
    const walletSlider = ref(null)
    const walletIndex = ref(0)
    const wallet = computed(() => props.wallets[walletIndex.value])
    const spacing = 215
    const sliderPosition = computed(() => walletSlider.value?.scrollOffset || 0)
    const isMobileSwiping = computed(
      () => walletSlider.value?.isSwiping || false
    )
    const walletSliderItems = computed(() => walletSlider.value?.items || null)
    watch(isMobileSwiping, () => {
      if (isMobileSwiping.value) return
      const jumbStep =
        Math.floor(sliderPosition.value / spacing) > props.wallets.length - 1
          ? props.wallets.length - 1
          : Math.floor(sliderPosition.value / spacing)
      walletSliderItems.value[jumbStep].toggle()
    })
    const onCardChange = (value) => {
      emit('onCardChange', value)
    }
    const onWindowSwiping = (e) => {
      if (isMouseDown.value)
        // walletSlider.value.scrollOffset = e.clientX
        console.log('swiping on browser window mode', e)
    }
    return {
      walletIndex,
      wallet,
      walletSlider,
      isMouseDown,
      onWindowSwiping,
      onCardChange,
      walletSliderItems
    }
  }
})
</script>
