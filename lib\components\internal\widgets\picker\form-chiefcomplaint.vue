<template>
  <v-card flat class="mt-5" min-height="75vh" max-height="75vh">
    <div class="d-flex">
      <v-text-field
        v-model="chiefComplaint"
        outlined
        :placeholder="$t('please enter main reason for consultation')"
        class="rounded-lg"
        maxlength="50"
        height="35"
        dense
      ></v-text-field>
      <v-btn
        color="primary"
        height="40"
        depressed
        class="ms-2 rounded-lg"
        @click="saveChiefcomplaint"
      >
        {{ $t('save') }}
      </v-btn>
    </div>
    <v-spacer />
    <div class="text-caption font-italic">
      {{ $t('or choose common reason') }}
    </div>
    <v-spacer />
    <v-tabs-items v-model="tabs" touchless>
      <v-tab-item>
        <!-- <v-card flat>
          <v-card-text>
            <v-list dense>
              <div class="text-title">
                {{ $t('psychotherapy') }}
              </div>
              <v-list-item
                v-for="(item, index) of options"
                :key="'opt-' + index"
                class="font-weight-bold"
                @click="tabs = index + 1"
              >
                {{ item[locale] }}
                <v-list-item-icon>
                  <v-icon>$chevronRight</v-icon>
                </v-list-item-icon>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card> -->
        <div class="mt-5">
          <div
            v-for="(item, index) of options"
            :key="'opt-' + index"
            class="font-weight-bold d-flex my-2"
            style="cursor: pointer"
            @click="tabs = index + 1"
          >
            <div class="flex-grow-1">
              {{ item[locale] }}
            </div>
            <div>
              <v-icon>$chevronRight</v-icon>
            </div>
          </div>
        </div>
      </v-tab-item>
      <v-tab-item
        v-for="(choice, index) of choiceLists"
        :key="'choice-' + index"
      >
        <FormMultichoice
          :key="'f-' + key"
          :choice-list="choice.slice(2)"
          :title="choice[1].title"
          :locale="locale"
          :name="choice[0].name"
          @go-tab="tabs = $event"
          @update-choice="updateChiefcomplaint"
        />
      </v-tab-item>
    </v-tabs-items>
  </v-card>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  watchEffect
} from '@nuxtjs/composition-api'
import DOMPurify from 'dompurify'
import {
  choiceListForMe,
  choiceListForCouple,
  choiceListForTeenagers
} from '../../../../repositories/choice/chiefcomplaint'
import FormMultichoice from './form-multichoice.vue'

export default defineComponent({
  components: {
    FormMultichoice
  },
  props: {
    reason: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    // DECLARE VARIABLE
    const { $cookies } = useContext()
    const chiefComplaint = ref('')
    const locale = computed(() => $cookies.get('locale') ?? 'vi')
    const tabs = ref(0)
    const options = [
      { vi: 'Cho tôi', en: 'For me' },
      { vi: 'Cho trẻ', en: 'For child' },
      { vi: 'Cặp đôi', en: 'Couple' }
    ]
    const choiceLists = [
      choiceListForMe,
      choiceListForTeenagers,
      choiceListForCouple
    ]
    const name = ref('')
    const key = ref(0)
    // LOGIC
    const updateChiefcomplaint = (data: any) => {
      chiefComplaint.value = DOMPurify.sanitize(data.text)
      name.value = data.text ? data.name : null
      if (!data.text) name.value = ''
      emit('update-cheifcomplaint', {
        text: data.text,
        name: data.text ? data.name : null,
        isFinished: false
      })
      /*
        tabs.value = 0
        key.value++
      */
    }
    const saveChiefcomplaint = () => {
      emit('update-cheifcomplaint', {
        text: DOMPurify.sanitize(chiefComplaint.value),
        name: name.value,
        isFinished: true
      })
      tabs.value = 0
    }
    watchEffect(() => {
      chiefComplaint.value = props.reason
    })
    return {
      tabs,
      options,
      locale,
      choiceLists,
      updateChiefcomplaint,
      chiefComplaint,
      key,
      saveChiefcomplaint
    }
  }
})
</script>
