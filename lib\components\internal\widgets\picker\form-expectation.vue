<template>
  <v-col
    v-show="choices.length > 0 || choiceList.length > 0"
    cols="12"
    class="px-0 mb-10"
  >
    <div class="d-flex">
      <div class="flex-grow-1">
        <h4 class="body-1 font-weight-medium text--secondary">
          {{ $t('what are your expectations from your therapist') }}
        </h4>
      </div>
      <div v-show="choiceList.length > 0 && choices.length > 0">
        <v-icon @click="openDialog">$edit</v-icon>
      </div>
    </div>
    <ul>
      <li v-for="(item, index) of choices" :key="index" class="pt-2">
        {{ item.key }}
      </li>
    </ul>

    <w-dialog
      :value="expectationDialog"
      :mobile-attr="{
        scrollable: true
      }"
      :desktop-attr="{
        width: '600px',
        scrollable: true
      }"
      :title="$t('what are your expectations from your therapist')"
      class-title=""
      @onClose="expectationDialog = false"
    >
      <div class="mt-5">
        <FormMultichoice
          :key="'form-' + key"
          :choice-list="choiceList.slice(1)"
          :locale="locale"
          :name="choiceListName"
          type="checkbox"
          @update-choice="emitExpectations"
        />
      </div>
    </w-dialog>
  </v-col>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  ref,
  useContext,
  watch,
  watchEffect
} from '@nuxtjs/composition-api'
import {
  expectationForCouple,
  expectationForMe
} from '../../../../repositories/choice/expectation-expert'
import { useProductOrder } from '../../../../repositories'
import FormMultichoice from './form-multichoice.vue'

const expectationChoices = [expectationForMe, expectationForCouple]

export default defineComponent({
  components: {
    FormMultichoice
  },
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const { $cookies } = useContext()
    const { orderItem } = useProductOrder()
    const locale = computed(() => $cookies.get('locale') ?? 'vi')
    const choiceList = computed(() => {
      if (!props.name) return []
      return (
        expectationChoices.find((i) => i[0].name?.includes(props.name)) ?? []
      )
    })
    const choiceListName = computed(() => {
      if (choiceList.value.length > 0) return choiceList.value[0].name
      return ''
    })
    const choices = ref([])
    const expectationDialog = ref(false)
    const openDialog = () => {
      if (choiceList.value.length > 0) {
        expectationDialog.value = true
      }
    }
    const key = ref(0)
    const emitExpectations = (data) => {
      choices.value = data.data.slice(-3)
      emit('update-expectations', data.data.slice(-3))
      expectationDialog.value = false
    }
    watchEffect(() => {
      const expectations = orderItem.value.meta?.expectations
      const formData = JSON.parse(localStorage.getItem('formData') ?? '{}')
      if (formData?.expectations) {
        choices.value = formData.expectations
        delete formData.expectations
        localStorage.setItem('formData', JSON.stringify(formData))
      } else if (expectations) {
        choices.value = expectations
      }
    })
    watch(
      () => props.name,
      (next, prev) => {
        key.value++
        if (prev.trim() !== '' && next.trim() !== '') choices.value = []
      }
    )
    return {
      choiceList,
      locale,
      choices,
      expectationDialog,
      openDialog,
      emitExpectations,
      key,
      choiceListName
    }
  }
})
</script>
