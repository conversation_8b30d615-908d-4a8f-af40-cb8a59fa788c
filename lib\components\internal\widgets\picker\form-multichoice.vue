<template>
  <div class="mt-5">
    <div v-if="title" class="text-title">
      <v-icon @click="$emit('go-tab', 0)">$chervonLeft</v-icon>
      {{ title[locale] }}
    </div>
    <v-list dense style="overflow-y: scroll; max-height: 60vh">
      <template v-if="type === 'checkbox'">
        <v-list-item v-for="(item, index) of choiceList" :key="index">
          <v-checkbox
            v-model="choiceCheckbox"
            :label="item[locale]"
            :value="item[locale]"
            dense
          ></v-checkbox>
        </v-list-item>
      </template>
      <template v-else>
        <v-radio-group v-model="choiceRadio">
          <v-radio
            v-for="(item, index) of choiceList"
            :key="index"
            :label="item[locale]"
            :value="item[locale]"
          ></v-radio>
        </v-radio-group>
      </template>
    </v-list>
    <v-fade-transition v-show="errorText !== ''">
      <div class="text-caption red--text text-center">
        {{ $t(errorText) }}
      </div>
    </v-fade-transition>
    <v-btn
      v-show="type === 'checkbox'"
      color="primary"
      block
      class="mt-5 rounded-lg"
      @click="updateChoice"
      >{{ $t('save') }}</v-btn
    >
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from '@nuxtjs/composition-api'
import type { PropType } from '@nuxtjs/composition-api'

export default defineComponent({
  props: {
    choiceList: {
      type: Array as PropType<any[]>,
      required: true
    },
    locale: {
      type: String as PropType<'vi' | 'en'>,
      default: 'vi'
    },
    title: {
      type: Object,
      default: null
    },
    name: {
      type: String,
      default: ''
    },
    type: {
      type: String as PropType<'radio' | 'checkbox'>,
      default: 'radio'
    }
  },
  setup(props, { emit }) {
    const choiceRadio = ref(null)
    const choiceCheckbox = ref([])
    const errorText = ref('')
    const updateChoice = () => {
      if (
        props.name.includes('expectation') &&
        choiceCheckbox.value.length > 3
      ) {
        errorText.value = 'maximum is 3 option'
        return
      }
      errorText.value = ''
      emit('update-choice', {
        name: props.name,
        data: choiceCheckbox.value
      })
    }
    watch(choiceRadio, () => {
      if (choiceRadio.value) {
        emit('update-choice', {
          name: props.name,
          text: choiceRadio.value
        })
      }
    })
    return {
      errorText,
      updateChoice,
      choiceRadio,
      choiceCheckbox
    }
  }
})
</script>
