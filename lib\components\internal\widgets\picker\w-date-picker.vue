<template>
  <v-dialog
    ref="datePickerMenu"
    v-model="datePickerMenu"
    transition="scale-transition"
    offset-x
    max-width="290px"
    min-width="290px"
  >
    <template #activator="{ on, attrs }">
      <v-text-field
        v-model="dateFormatted"
        v-mask="mask"
        :dense="dense"
        :readonly="readonly"
        :rules="rules"
        :placeholder="placeholder"
        :label="label"
        :persistent-hint="persistentHint"
        :hint="hint"
        :hide-details="hideDetails"
        aria-autocomplete="false"
        outlined
        clearable
        required
        autocomplete="off"
        class="rounded-lg"
        v-bind="attrs"
        v-on="openPickerOnClick ? on : null"
        @click:clear="onClear"
        @keydown="onInputChange"
        @blur="parseDate(dateFormatted)"
      >
        <template v-if="label" #label>
          {{ label }}
          <span v-if="required" class="red--text">*</span>
        </template>
        <template #append>
          <v-icon @click="datePickerMenu = !datePickerMenu">{{
            appendIcon
          }}</v-icon>
        </template>
        <template #message="{ message }">
          <span v-text="$t(message)"></span>
        </template>
      </v-text-field>
    </template>
    <v-date-picker
      ref="datePicker"
      v-model="date"
      :max="maxDate"
      :min="minDate"
      :locale="locale"
      show-current
    >
      <v-spacer></v-spacer>
      <v-btn text color="grey" @click="onClosePicker">{{ $t('cancel') }}</v-btn>
      <v-btn text color="primary" @click="onSavePicker">{{ $t('ok') }}</v-btn>
    </v-date-picker>
  </v-dialog>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Vue, Component, Emit, Prop, Watch } from 'vue-property-decorator'
/* eslint-disable @typescript-eslint/no-unused-vars */
import { mask } from 'vue-the-mask'
import { Debounce } from 'vue-debounce-decorator'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'

dayjs.extend(customParseFormat)

@Component({
  directives: {
    mask
  }
})
/* eslint-disable @typescript-eslint/no-unused-vars */
export default class DatePickerComponent extends Vue {
  @Prop({ type: String, default: '' })
  value!: string

  @Prop({ type: String, default: 'vi-vn' }) // "vi-VN" | "en-us"
  locale: string

  @Prop({ type: String, default: 'date' })
  type: string

  @Prop({ type: Array, default: () => [] })
  rules: []

  @Prop({ type: String, default: 'DD/MM/YYYY' })
  placeholder: string

  @Prop({ type: String, default: '' })
  label: string

  @Prop({ type: String, default: 'Required field' })
  errorMessage: string

  @Prop({ type: String, default: 'Có thể nhâp DDMMYYYY hoặc DDMMYY' })
  hint: string

  @Prop({ type: Boolean, default: false })
  readonly: boolean

  @Prop({ type: Boolean, default: true })
  persistentHint: boolean

  @Prop({ type: Boolean, default: false })
  hideDetails: boolean

  @Prop({ type: Boolean, default: false })
  required: boolean

  @Prop({ type: String, default: dayjs().toISOString() })
  maxDate: string

  @Prop({ type: String, default: '' })
  minDate: string

  @Prop({ type: Boolean, default: false })
  dense: boolean

  @Prop({ type: String, default: '$calendar' })
  appendIcon

  @Prop({ type: Boolean, default: true })
  openPickerOnClick: boolean

  @Watch('datePickerMenu')
  datePickerOnChange(val) {
    if (this.type === 'dob') {
      if (val && this.dateFormatted === '') {
        this.date = this.$dayjs(this.maxDate).format('YYYY-MM-DD')
      }
    }
  }

  @Emit('save-picker')
  onSavePicker($event) {
    ;(this.$refs.datePickerMenu as any).save(this.date)
    this.parseDate(this.date)
    return $event
  }

  @Emit('input-change')
  @Debounce(500)
  onInputChange($event) {
    return $event
  }

  @Emit('input')
  emitInput(value) {
    return value
  }

  @Emit('click:clear')
  onClear($event) {
    return $event
  }

  @Watch('value', { immediate: true })
  onValueChange(newVal) {
    if (newVal) {
      this.parseDate(newVal)
    }
  }

  get datePicker(): any {
    return this.$refs.datePicker
  }

  datePickerMenu: boolean = false
  date: string = new Date().toISOString().substr(0, 10)

  mask: string = '##/##/####'

  get dateFormatted(): string {
    return this.value ? dayjs(this.value).format('DD/MM/YYYY') : this.value
  }

  set dateFormatted(value: string) {
    this.emitInput(value)
  }

  parseDate(date) {
    if (date) {
      if (
        dayjs(date, ['DD/MM/YY', 'DD/MM/YYYY', 'YYYY/MM/DD'], true).isValid()
      ) {
        const dateFormat = dayjs(
          date,
          ['DD/MM/YY', 'DD/MM/YYYY', 'YYYY/MM/DD'],
          true
        )
        this.addDateValue(dateFormat)
      } else if (dayjs(date).isValid()) {
        const dateFormat = dayjs(date)
        this.addDateValue(dateFormat)
      }
    }
  }

  addDateValue(date: any) {
    this.dateFormatted = date.format('DD/MM/YYYY')
    this.date = date.format().substring(0, 10)
    this.emitInput(date.format())
  }

  onClosePicker() {
    this.datePickerMenu = false
  }
}
</script>
