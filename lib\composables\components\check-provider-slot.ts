import { computed, ref, watch } from '@nuxtjs/composition-api'
import { useProviderTimeslots } from '../../repositories'

export function useProviderSlotCheck(providerSlug: string) {
  const alertTimeslot = ref(false)
  const alertError = ref(false)
  const timeslotCount = ref(0)
  const totalSlots = ref(0)
  const errorCount = ref(0)

  const {
    provider,
    executeSearchProvider,
    onGetProviderTimeslotsSuccess,
    onGetProviderTimeslotsError,
    timeslotsData
  } = useProviderTimeslots(
    computed(() => ({
      slug: providerSlug
    })),
    true
  )

  const checkProviderSlots = () => {
    executeSearchProvider()
  }

  onGetProviderTimeslotsSuccess(() => {
    timeslotCount.value++
    totalSlots.value += timeslotsData.length
    if (timeslotCount.value === 4) {
      alertTimeslot.value = totalSlots.value === 0
    }
  })

  onGetProviderTimeslotsError(() => {
    errorCount.value++
    if (errorCount.value === 4) {
      alertError.value = true
    }
  })

  const resetChecks = () => {
    timeslotCount.value = 0
    totalSlots.value = 0
    errorCount.value = 0
    alertTimeslot.value = false
    alertError.value = false
  }

  watch(
    () => providerSlug,
    () => {
      resetChecks()
      checkProviderSlots()
    }
  )

  return {
    alertTimeslot,
    alertError,
    checkProviderSlots,
    resetChecks,
    totalSlots,
    provider
  }
}
