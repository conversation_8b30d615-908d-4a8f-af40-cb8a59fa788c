import { useContext } from '@nuxtjs/composition-api'

export const dobFormat = (date: string | Date | undefined): string | null => {
  const { i18n, $dayjs } = useContext()

  const dob = $dayjs(date)
  const today = $dayjs()
  let returnText = ''

  const yearsDiff = today.diff(dob, 'year', true)
  const monthsDiff = today.diff(dob, 'month', true)
  const weeksDiff = today.diff(dob, 'week', true)
  const daysDiff = today.diff(dob, 'day')

  if (yearsDiff > 3) {
    returnText += `${Math.floor(yearsDiff)} ${i18n.t('years old')}`
  } else if (yearsDiff >= 1 && yearsDiff <= 3) {
    returnText += `${Math.floor(monthsDiff)} ${i18n.t('months old')}`
  } else if (weeksDiff >= 1 && weeksDiff < 4) {
    returnText += `${Math.floor(weeksDiff)} ${i18n.t('weeks old')}`
  } else {
    returnText += `${daysDiff} ${i18n.t('days old')}`
  }

  return returnText
}

/**
 * Function that formats a given date into a human-readable age format with the corresponding date.
 * If the date is more than 3 years old, it displays the age in years. If between 1 month and 3 years, it shows the age in months.
 * If between 7 days and 3 years, it displays the age in weeks. Otherwise, it shows the age in days.
 * The formatted date is appended at the end of the age information.
 * @param date - The date to be formatted, can be a string, Date object, or undefined.
 * @returns A ref containing the formatted age and date, or null if the input date is undefined.
 */
