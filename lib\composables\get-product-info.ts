import { computed, useStore } from '@nuxtjs/composition-api'
import { useProductOrder } from '../repositories'

export default function getProductInfo() {
  const { state }: any = useStore()

  const { orderItem, orderItems, authenUser, loading } = useProductOrder()
  const isMember = computed(() => authenUser.value?.isMember || false)
  const isMembershipProduct = computed<boolean>(() => {
    if (orderItems.value.length > 0 && orderItems.value)
      return orderItems.value.some((item: any) =>
        // /membership/.test(
        //   item?.product?.sku || /membership/.test(item?.product?.type?.name)
        // )
        {
          const conditions = [
            /membership/.test(item?.product?.sku),
            item?.product?.sku === 'health-program-baby-development'
          ]
          return conditions.includes(true)
        }
      )
    return false
  })

  const isPregnancyProduct = computed<boolean>(() => {
    return orderItems.value.some(
      (item: any) => item?.product?.sku === 'pregnancy-diary'
    )
  })

  const typeProduct = computed(() => {
    return (
      orderItem.value?.product?.sku || orderItems.value[0]?.type?.name || ''
    )
  })

  const skuProduct = computed(() => {
    return state.checkout?.orderItems[0]?.product?.sku
  })

  return {
    loading,
    isMember,
    skuProduct,
    typeProduct,
    isPregnancyProduct,
    isMembershipProduct
  }
}
