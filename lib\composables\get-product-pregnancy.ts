import { computed, useStore, ref, onMounted } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useElasticSearch } from '@wellcare/nuxt-module-notion/repositories'

const transform = (hits: Ref<any>) =>
  hits.value.reduce(
    (acc, curr) => {
      const product = {
        description: `${curr?.output?.slug}:description`,
        ...curr.output
      }

      if (product.sku === 'membership') {
        acc.member = product
      } else {
        acc.addon.push(product)
      }

      return acc
    },
    { member: {}, addon: [] }
  )

export const getPricePregnancy = () => {
  onMounted(() => {
    execute()
  })

  const { state } = useStore()

  const isMember = computed<boolean>(
    () => (state as any)?.authen?.user?.isMember
  )

  const query = ref({
    _source: ['output.*'],
    sort: [
      {
        'score.rating': 'desc'
      }
    ],
    query: {
      bool: {
        filter: [],
        must: [],
        should: [
          {
            match_phrase: {
              'search.inCollections.keyword': isMember.value
                ? 'member-pregnancy-diary'
                : 'non-member-pregnancy-diary'
            }
          },
          {
            match_phrase: {
              'search.inCollections.keyword': isMember.value
                ? 'member'
                : 'non-member'
            }
          },
          {
            match_phrase: {
              'search.inCollections.keyword': 'pregnancy-diary'
            }
          }
        ]
      }
    },
    from: 0,
    size: 5
  })

  const { hits, execute, loading } = useElasticSearch('catalog_product', query)

  const products = computed(() => (hits.value.length ? transform(hits) : []))

  return {
    products,
    hits,
    loading
  }
}
