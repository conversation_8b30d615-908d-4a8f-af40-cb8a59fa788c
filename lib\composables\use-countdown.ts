import { ref, onBeforeUnmount } from '@nuxtjs/composition-api'

export default function useCountdown() {
  const showCountdown = ref(false)
  const countdown = ref({ minutes: '00', seconds: '00' })
  let timer: any = null

  const startCountdown = (duration: number) => {
    showCountdown.value = true

    timer = setInterval(() => {
      const minutes = Math.floor(duration / 60)
      const seconds = Math.floor(duration % 60)

      countdown.value = {
        minutes: minutes < 10 ? '0' + minutes : minutes.toString(),
        seconds: seconds < 10 ? '0' + seconds : seconds.toString()
      }

      if (--duration < 0) {
        clearInterval(timer)
      }
    }, 1000)
  }

  onBeforeUnmount(() => {
    clearInterval(timer)
  })

  return { showCountdown, countdown, startCountdown }
}
