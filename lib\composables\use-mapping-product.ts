import {
  ref,
  useStore,
  useContext,
  computed,
  useRoute
} from '@nuxtjs/composition-api'

export function useMappingProduct() {
  const { state }: any = useStore()
  const { i18n } = useContext()
  const route = useRoute()
  const routeName = computed(() => route.value?.name)
  const isPaymentRoute = computed(() => routeName.value.includes('payment'))

  const isEminent = computed(() => {
    if (isPaymentRoute.value) {
      return checkEminentInPaymentRoute() || undefined
    }

    return checkEminentInCheckoutRoute()
  })

  function checkEminentInPaymentRoute() {
    return state.checkout?.orderItems?.some(
      (item) =>
        item?.meta?.benefits?.some((benefit) => benefit?.isEminent) &&
        item?.meta?.personalDoctorId
    )
  }

  function checkEminentInCheckoutRoute() {
    const product = state.checkout?.orderItems[0]?.product
    const isEminentMeta = product?.meta?.isEminent

    if (isEminentMeta == null) {
      return product?.sku === 'membership-personal-doctor' ? false : undefined
    }

    return isEminentMeta
  }

  const personalDoctorDesc = computed(() => {
    if (isPaymentRoute.value) {
      // Payment flow
      return isEminent.value
        ? 'payment personal-doctor with specialists'
        : 'payment personal-doctor without specialists'
    }

    // Checkout flow
    if (isEminent.value === true) {
      return 'checkout personal-doctor with specialists'
    } else if (isEminent.value === false) {
      return 'checkout personal-doctor without specialists'
    }
    return 'checkout personal-doctor desc'
  })

  const providerName = computed(() => {
    let orderItemWithDoctor = null

    if (!isPaymentRoute.value) {
      orderItemWithDoctor = state.checkout?.orderItems?.find(
        (item) => item?.meta?.provider?.name
      )
    } else {
      orderItemWithDoctor = state.checkout?.orderItems?.find(
        (item) =>
          item?.meta?.personalDoctorName &&
          item?.product?.sku === 'personal-doctor-addon'
      )
    }

    if (!orderItemWithDoctor) return ''

    const { provider, personalDoctorName, personalDoctorTitle } =
      orderItemWithDoctor.meta || {}

    if (!isPaymentRoute.value) {
      return provider?.name ? ` - ${provider.title}. ${provider.name}` : ''
    } else if (personalDoctorName && personalDoctorTitle) {
      return ` - ${personalDoctorTitle}. ${personalDoctorName}`
    }

    return ''
  })

  const tabsMapping = ref({
    'all-year-combo-10-questions': {
      title: 'asynchronous telehealth package',
      description: 'Membership + 10 question combo',
      moreDetail:
        'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care',
      price: '750000'
    },
    'all-year-member': {
      title: 'membership',
      description:
        'Membership (all-year waiver of the service fee and other benefits)',
      moreDetail:
        'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care',
      price: '250000'
    },
    'hanh-trinh-cung-con-khon-lon': {
      title: 'baby development',
      description: 'comprehensive remote healthcare solution for children',
      moreDetail: 'baby development desc',
      price: '250000'
    },
    'membership-personal-doctor': {
      title: 'membership',
      description:
        'Membership (all-year waiver of the service fee and other benefits)',
      moreDetail:
        'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care',
      price: '250000'
    },
    'combo-10-questions': {
      title: 'asynchronous telehealth package',
      description: 'Membership + 10 question combo',
      moreDetail:
        'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care',
      price: '500000'
    },
    'personal-doctor': {
      title:
        i18n.t('personal doctor rate') +
        `${providerName && providerName.value}`,
      description: personalDoctorDesc.value,
      price: '2280000'
    },
    'personal-doctor-addon': {
      title:
        i18n.t('personal doctor rate') +
        `${providerName && providerName.value}`,
      description: personalDoctorDesc.value,
      price: '2280000'
    }
  })

  function getBenefitName(key: string) {
    if (tabsMapping.value[key]) {
      return tabsMapping.value[key]
    }

    if (
      key.endsWith('-membership-personal-doctor') &&
      key !== 'membership-personal-doctor'
    ) {
      return {
        title: 'membership',
        description:
          'Membership (all-year waiver of the service fee and other benefits)',
        price: '250000',
        moreDetail:
          'Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care'
      }
    }

    return ''
  }

  function checkBenefitExist(orderItem: any) {
    const productSlug = orderItem?.product?.slug
    return (
      Object.keys(tabsMapping.value).includes(productSlug) ||
      (productSlug?.endsWith('-membership-personal-doctor') &&
        productSlug !== 'membership-personal-doctor')
    )
  }

  return {
    getBenefitName,
    checkBenefitExist,
    tabsMapping
  }
}
