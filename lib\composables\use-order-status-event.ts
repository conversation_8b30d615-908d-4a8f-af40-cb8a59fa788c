import {
  // useRoute,
  // useRouter,
  ref
} from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useEventSource } from '@wellcare/nuxt-module-data-layer'
// import getProductInfo from './get-product-info'

export default function useOrderStatusEvent(
  orderId: Ref<string> | ComputedRef<string>,
  timeout: number = 1000 * 60 * 10
) {
  // const route = useRoute()
  // const router = useRouter()
  // const { isMembershipProduct, isPregnancyProduct } = getProductInfo()
  const orderStateEvent = ref<string>('incart')
  const validStates = ['placed', 'fullfilled', 'partialfilled']

  const {
    execute: executeEvtSource,
    onMessage,
    onError: onEvtSourceErr
  } = useEventSource({
    path: `ecommerce/order/${orderId.value}/watch`,
    timeout
  })

  onEvtSourceErr((message) => {
    console.error('GET event source file error: ', message)
  })
  onMessage((message) => {
    const state = (message.toObject as any)?.data?.state || ''
    if (validStates.includes(state)) {
      // router.push({
      //   path: `/payment/order/${route.value.params.id}/success`,
      //   query: {
      //     membershipProduct: String(isMembershipProduct.value),
      //     pregnancyProduct: String(isPregnancyProduct.value)
      //   }
      // })
      orderStateEvent.value = state
      // stop use event source
      close()
    }
  })
  onEvtSourceErr((message) => {
    console.error('GET event source file error: ', message)
  })
  return {
    orderStateEvent,
    executeEvtSource
  }
}
