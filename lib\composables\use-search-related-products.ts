import { ref, watch } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useElasticSearch } from '@wellcare/nuxt-module-notion/repositories/use-elastic-search'
import { useMappingProduct } from './index'

interface Product {
  title: string
  price: number
  slug: string
  [key: string]: any
}

interface SearchParams {
  _source: string[]
  sort: any[]
  query: {
    bool: {
      filter: any[]
      must: any[]
      should: any[]
    }
  }
  from: number
  size: number
}

export function useSearchRelatedProducts(options: any) {
  const data: Ref<Product[]> = ref([])
  const indexExplore = 'catalog_product'
  const searchParams: Ref<SearchParams> = ref({
    _source: ['output.*'],
    sort: [
      {
        'score.rating': 'asc'
      }
    ],
    query: {
      bool: {
        filter: [],
        must: [],
        should: options.map((query: string) => ({
          match_phrase: { 'search.inCollections.keyword': query }
        }))
      }
    },
    from: 0,
    size: 5
  })

  const { execute, hits, total, loading, error } = useElasticSearch(
    indexExplore,
    searchParams
  )
  const { getBenefitName, tabsMapping, checkBenefitExist } = useMappingProduct()

  watch(
    () => hits.value,
    () => {
      const container: Product[] = []
      hits.value.forEach((hit: any) => {
        container.push({
          title: hit?.output?.name || '',
          price: hit?.output?.price || 0,
          slug: hit?.output?.slug || '',
          ...getBenefitName(hit?.output?.slug)
        })
      })
      data.value = [...container].sort((a, b) => a.price - b.price)
    }
  )

  return {
    execute,
    data,
    hits,
    total,
    error,
    loading,
    tabsMapping,
    getBenefitName,
    checkBenefitExist
  }
}
