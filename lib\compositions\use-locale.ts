import { useContext } from '@nuxtjs/composition-api'

export function useLocale() {
  const { app, route } = useContext()
  let locale = app.i18n.defaultLocale || 'vi'
  const setLocale = function (str: string) {
    app.$log.debug(`set locale -> ${str}`)
    app.i18n.setLocale(str)
    app.i18n.setLocaleCookie(str)
    locale = str
  }
  if (
    app.i18n.availableLocales.includes(route.value.query.locale?.toString())
  ) {
    setLocale(route.value.query.locale.toString())
    delete route.value.query.locale
  }
  return { locale, setLocale }
}
