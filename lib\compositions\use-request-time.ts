import { useContext, ref, computed, reactive } from '@nuxtjs/composition-api'

export default () => {
  const { i18n, $dayjs } = useContext()
  const day = ref()
  const timeSlots = ref([])
  const time = computed(
    () =>
      `${i18n.t(day.value)}${
        timeSlots.value.length > 0
          ? `,${timeSlots.value
              .map((timeSlot) => timeSlot.text)
              .toString()
              .toLowerCase()}`
          : ''
      }`
  )
  const displayTimeslot = ref()
  const requestedSlots = computed(() =>
    timeSlots.value.map((timeSlot: any) => {
      const slot = { from: '', to: '' }
      const _day = day.value === 'today' ? $dayjs().day() : $dayjs().day() + 1
      slot.from = $dayjs()
        .day(_day)
        .hour(timeSlot.value[0])
        .minute(0)
        .toISOString()
      slot.to = $dayjs()
        .day(_day)
        .hour(timeSlot.value[1])
        .minute(0)
        .toISOString()
      return slot
    })
  )
  const times = reactive([
    {
      key: 'morning',
      label: {
        title: i18n.t('morning'),
        brief: '08h00 - 12h00'
      },
      text: ' ' + i18n.t('morning') + ' (08h00 - 12h00)',
      value: [8, 12]
    },
    {
      key: 'noon',
      label: {
        title: i18n.t('noon'),
        brief: '12h00 - 14h00'
      },
      text: ' ' + i18n.t('noon') + ' (12h00 - 14h00)',
      value: [12, 14]
    },
    {
      key: 'afternoon',
      label: {
        title: i18n.t('afternoon'),
        brief: '14h00 - 16h00'
      },
      text: ' ' + i18n.t('afternoon') + ' (14h00 - 16h00)',
      value: [14, 16]
    },
    {
      key: 'lateAfternoon',
      label: {
        title: i18n.t('late afternoon'),
        brief: '16h00 - 18h00'
      },
      text: ' ' + i18n.t('late afternoon') + ' (16h00 - 18h00)',
      value: [16, 18]
    },
    {
      key: 'evening',
      label: {
        title: i18n.t('evening'),
        brief: '18h00 - 20h00'
      },
      text: ' ' + i18n.t('evening') + ' (18h00 - 20h00)',
      value: [18, 20]
    }
  ])
  return {
    times,
    time,
    day,
    timeSlots,
    displayTimeslot,
    requestedSlots
  }
}
