import { AssertionError } from 'assert'
import { defaultNamespace } from '.'

interface VAT {
  show: boolean
  useOrganizationId: string
}
export interface BuildConfig {
  level: number
  prefix: string
  vat: VAT
  fee: any
  payment: any
  topup: any
}

export interface PublicRuntimeConfig {}

export const validate = (
  _buildConfig: BuildConfig,
  _publicRuntimeConfig: PublicRuntimeConfig
) => {
  if (!_buildConfig.prefix)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config prefix`
    })

  if (!_buildConfig.level)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config level`
    })

  if (!_buildConfig.vat)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config vat`
    })

  if (!_buildConfig.fee)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config fee`
    })

  if (!_buildConfig.payment)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config payment`
    })

  if (!_buildConfig.topup)
    throw new AssertionError({
      message: `[${defaultNamespace}] missing config topup`
    })
}
