import { join, resolve } from 'path'
import { readdirSync, existsSync } from 'fs'
import { Module } from '@nuxt/types'
import { sortRoutes } from '@nuxt/utils'
import localeEn from './locales/en.json'
import localeVi from './locales/vi.json'
import { BuildConfig, validate } from './configs/module.config'
import {
  serverMiddlewares,
  defaultNamespace,
  modules,
  routers
} from './configs'

type NuxtModule = Module & { meta: string }

const myModule: NuxtModule = function (moduleOptions: BuildConfig) {
  const namespace = defaultNamespace
  const buildConfig: BuildConfig = {
    ...this.options[namespace],
    ...moduleOptions
  }

  // validate options
  validate(buildConfig, this.nuxt.options.publicRuntimeConfig[namespace])

  // ADD PLUGINS
  ;['./middleware', './plugins', './store'].forEach((folder) => {
    if (existsSync(resolve(__dirname, folder))) {
      const pluginPath = resolve(__dirname, folder)
      readdirSync(pluginPath).forEach((pluginFile) => {
        if (pluginFile.match(/(vue|ts|js)$/) && !pluginFile.match(/(d.ts)$/)) {
          const path = resolve(pluginPath, pluginFile)
          this.addPlugin({
            src: path,
            fileName: join(namespace, folder, pluginFile),
            buildConfig
          })
          console.info(`[${namespace}] plugin added ${folder}/${pluginFile}`)
        }
      })
    }
  })

  // ADD LAYOUT
  const layoutFolder = resolve(__dirname, './layouts')
  if (existsSync(resolve(__dirname, layoutFolder))) {
    readdirSync(layoutFolder).forEach((layoutFile) => {
      if (layoutFile.match(/(vue)$/)) {
        const path = resolve(__dirname, `./layouts/${layoutFile}`)
        this.addLayout(path, layoutFile.split('.')[0])
        console.info(`[${namespace}] layout added ${layoutFile}`)
      }
    })
  }

  // docs: https://nuxtjs.org/docs/configuration-glossary/configuration-components/
  this.nuxt.hook('components:dirs', (dirs: any) => {
    const dirPath = resolve(__dirname, 'components/global')
    dirs.push({
      path: dirPath,
      prefix: buildConfig.prefix,
      level: buildConfig.level // allow parent app to overwrite
    })
    console.info(`[${namespace}] components:dirs pushed`, dirs)
  })

  // REGISTER ROUTES
  this.nuxt.hook('build:extendRoutes', (routes: any[], resolve: any) => {
    routers.forEach((route) => {
      routes.unshift({
        name: route.name,
        path: route.path,
        component: resolve(__dirname, route.component),
        chunkNames: route.chunkName
      })
    })
    sortRoutes(routes)
    console.info(
      `[${namespace}] build:extendRoutes added`,
      routers.map((i) => i.path)
    )
  })

  this.nuxt.hook('i18n:extend-messages', (additionalMessages: any[]) => {
    let en = { ...localeEn }
    let vi = { ...localeVi }
    for (let i = 0; additionalMessages.length; i++) {
      en = { ...additionalMessages[i]?.en, ...en }
      vi = { ...additionalMessages[i]?.vi, ...vi }
      additionalMessages.pop()
    }
    additionalMessages.push({ en, vi })
    console.info(`[${namespace}] i18n:extend-messages`, {
      vi: Object.keys(vi).length,
      en: Object.keys(en).length
    })
  })

  this.nuxt.hook('modules:done', ({ requiredModules }: any) => {
    modules.forEach((module) => {
      if (Object.keys(requiredModules).includes(module))
        console.info(`[${namespace}] required module OK : ${module}`)
      else {
        throw new Error(`[${namespace}] missing module : ${module}`)
      }
    })
  })

  // ADD SERVER MIDDLEWARE
  serverMiddlewares.forEach((middleware) =>
    this.addServerMiddleware(middleware)
  )
}

export default myModule
export { BuildConfig } from './configs/module.config'
myModule.meta = require('./package.json')
