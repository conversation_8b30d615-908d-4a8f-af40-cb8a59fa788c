{"enter a number": "Enter a number", "choose an amount": "Choose an amount", "minimum top up": "Minimum top-up is", "you are out of usage": "You are out of usage", "you have": "You have", "Sponsored by Manulife": "Sponsored by <PERSON><PERSON><PERSON>", "usage": "Usage", "open consultation": "Open consultation", "Sponsored by AIA": "Sponsored by AIA", "Activate": "Activate", "unactivated": "Unactivated", "point": "point", "choose card": "Choose card", "wellcare-prepaid": "Wellcare Card", "wellcare card": "WELLCARE CARD", "Order is being processed": "Order is being processed", "Other": "Other", "choose payment type": "Choose payment type", "just": "Just", "Valid thru": "VALID THRU", "Valid to": "VALID TO", "Standard": "Standard", "Your member ID is not eligible for this service. Please choose another payment method": "Your member ID is not eligible for this service. Please choose another payment method.", "make the payment transfer from your bank. Input your mobile phone number as the note of bank transfer content": "<p>Make the payment transfer from your bank. Input your mobile phone number as the note of bank transfer content.</p>\n<p>Within 30 minutes after receiving the payment, Wellcare will send you a confirmation on the mobile app.</p>\n<p style=\"margin-bottom:0px\">Make a cellular call and internet call to the doctor from our mobile app: <a style='text-decoration: none' class='primary--text font-italic' href=\"https://khamtuxa.vn/download\" target=\"_blank\">https://khamtuxa.vn/download</a>.</p>", "this order has been abandoned": "This order has been abandoned", "Value is not a valid email address": "Value is not a valid email address", "Value is required": "Value is required", "This field should be at least 10 characters long": "This field should be at least 10 characters long", "redeem": "Redeem", "voucher applied successfully": "Voucher applied successfully", "Code applied": "Code applied", "Payment": "Payment", "Pay now": "Pay now", "your current balance": "Current balance", "your balance after successful payment": "After payment", "payment confirmation": "payment confirmation", "your order total is": "Order total", "press confirm button to finish your checkout": "Press confirm button to finish your checkout", "cancle": "<PERSON><PERSON>", "Wellcare Prepaid Card": "Wellcare Prepaid Card", "this order has been placed.": "This order has been placed.", "do you have a voucher or access code?": "Do you have a voucher or access code?", "confirmed payment": "Confirmed payment", "Something is occurred": "Something is occurred", "No Available Slot": "No Available Slot", "Please try again later or go to the Services section to continue exploring. Thank you for your interest": "Please try again later or go to the Services section to continue exploring. Thank you for your interest!", "Go back and try again or call us +84 366 905 905": "Go back and try again or call us +84 366 905 905", "call us +***********": "Call us +84 366 905 905", "your notice": "Your notice", "Enter your notice...": "Enter your notice...", "export e-invoice": "Export e-invoice", "Benefits": "Benefits", "time(s) of usage remaining": "time(s) of usage remaining", "minute(s) of usage remaining": "minute(s) of usage remaining", "your card balance is not enough for service booking, please purchase additional points": "Your card balance is not enough for service booking, please purchase additional points", "Want to": "Want to", "change your membership ?": "change your membership ?", "for special customer": "For special customer", "available": "Balance", "using member card": "Using member card", "Add-ons": "Add-ons", "Buy medical device at home": "Buy medical device at home", "Add member": "Add member", "Membership Payment": "Membership Payment", "your order": "order", "choose plan": "choose plan", "for premiere care": "For premiere care", "for conceirge care": "For conceirge care", "short question for doctor.": "Knowledge question for doctor.", "access Wellcare resource without limitation.": "Access Wellcare resource without limitation.", "register your own doctor and assistant for your families.": "Register your own doctor and assistant for your families.", "Choose membership type and topup to use Wellcare services": "Choose membership type and topup to use Wellcare services", "Payment methods": "Payment methods", "Topup method": "Topup method", "one time payment": "One time payment", "use membership card": "Use membership card", "enter code...": "Enter code...", "did you have": "Did you have", "voucher": "voucher", "access code?": "access code?", "1. lacking of the necessary information": "1. Lacking of the necessary information.", "1. the inquiry is having less information than what is needed.": "1. The inquiry is having less information than what is needed.", "100% satisfaction guarantee": "100% satisfaction guarantee", "2. the patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon": "2. The patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon.", "250 characters": "250 characters", "3. Confirmation": "3. Confirmation", "30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos. But it may take longer if you transfer manually through internet banking, depends on the time it arrives": "30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos. But it may take longer if you transfer manually through internet banking, depends on the time it arrives.", "Available to patient after doctor click complete": "Available to patient after doctor click 'Complete'", "By health record": "My Account", "Cannot upload!": "Cannot upload!", "Cellular Call or Internet Call (using Wellcare app) on schedule": "Cellular Call or Internet Call (using Wellcare app) on schedule", "Detailed description placeholder": "Describe chronologically from the beginning: symptoms, progress, treatment received, treatment effectiveness...", "Doctor and Wellcare reserve the right to decline, whereas:": "Doctors & Wellcare reserve the right to decline, if:", "I bought package": "My package", "Later": "Later", "Long Chau will call you for confirm this order": "<PERSON> will call you for confirm this order", "Master of psychology": "Master of psychology", "OTP confirmation": "OTP confirmation", "RECOMMENDED PRESCRIPTION": "RECOMMENDED PRESCRIPTION", "Recommended purchase": "Recommended purchase", "Satisfied": "satisfied", "Send patient": "Send patient", "Show Medical Record": "Show Medical Record", "Someone else": "Someone else", "Sponsored by": "Sponsored by", "Systolic": "Systolic", "The exact age and gender of the patient is advisable": "The exact age and gender of the patient is advisable", "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly", "Video Call using Wellcare app on schedule (you can always switch to Cellular Call once the internet is unstable)": "<p style=\"margin-bottom: 0px;\">Video Call on schedule through our mobile app (you can always switch to Cellular once the internet is down).</p>", "You can upload the images, video clip, audio and files using the short link in sms (or from mobile app) ONLY AFTER the payment was done and the appointment was confirmed.": "<p>You can upload the images, video clip, audio and files using the short link in sms (or from mobile app) <span style=\"\n    color: #ff5722;\n\">ONLY AFTER</span> the payment was done and the appointment was confirmed.</p>", "You don't have any package. Please purchase": "You don't have any package. Please purchase", "You don't have enough flexible benefits balance": "You don't have enough flexible benefits balance", "You have {quota} uses remained": "You have <br/>  <span style='font-size:18pt; font-weight:bold'>{quota}</span> uses remained", "a doctor of your choice": "A doctor of your choice", "about us": "About us", "accept": "Accept", "acceptable": "acceptable", "account": "Account", "account registered successfully": "Account registered successfully", "activate": "Activate", "activate voice call": "Activate voice call", "add": "ADD", "add event": "Add event", "add feature": "Add feature", "add location": "Add location", "add notification": "Add notification", "add question": "Add question", "add relative": "Add relationship", "add time": "Add time", "add to prescription": "Add to prescription", "additional instruction": "Additional instruction", "address": "Address", "after": "After", "after consultation, doctor may send you diagnosis, recommended prescriptions or treatment notes": "After consultation, doctor may send you diagnosis, recommended prescriptions or treatment notes", "after payment done": "After payment done:", "after payment done: add images & videos (if any) from our mobile app call ontime after consultation done: note and suggested prescription (if any) will be shown upon the completion notification one free follow-up question within 24 hours note: lacking of necessary medical information may lead to a cancellation no cash refund re-schedule once, within 30 days": "<div><ul style='list-style: none; padding-left: 0;'>After payment done:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>Add images & videos (if any) from our mobile app</span></li><li style='padding-left: 8px; display: flex;  align-items: flex-start;'><span>-&nbsp;</span><span>Call ontime</span></li></ul><ul style='list-style: none; padding-left: 0;'>After consultation done:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>Note and suggested prescription (if any) will be shown upon the completion notification</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>One free follow-up question within 24 hours</span></li></ul><ul style='list-style: none; padding-left: 0; align-items: flex-start;'>Note:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>Lacking of necessary medical information may lead to a cancellation</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-error-base)'>No refund</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-primary-base)'>Re-schedule once, within 7 days</span></li></ul></div>", "after payment is done, please: check your for our confirmation sent through sms or on the wellcare mobile app, there will be your e-medical record upload images and videos from the link in that confirmation notification or on our wellcare mobile app asap, our doctor will check it out touch the call button on your e-medical record on time. see the note and suggested prescription after receiving the completion confirmation send a follow-up question to the doctor as needed, within 24 hours. please note: we may cancel the appointment unless you provide all the necessary medical information as requested.": "<div><ul style='list-style: none; padding-left: 0;'>After payment is done, please:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>Check your for our confirmation sent through SMS or on the Wellcare mobile app, there will be your e-medical record</span></li><li style='padding-left: 8px; display: flex;  align-items: flex-start;'><span>-&nbsp;</span><span>Upload images and videos from the link in that confirmation notification or on our Wellcare mobile app asap, our doctor will check it out</span></li><li style='padding-left: 8px; display: flex;  align-items: flex-start;'><span>-&nbsp;</span><span>Touch the CALL button on your e-medical record on time</span></li><li style='padding-left: 8px; display: flex;  align-items: flex-start;'><span>-&nbsp;</span><span>See the note and suggested prescription after receiving the completion confirmation</span></li><li style='padding-left: 8px; display: flex;  align-items: flex-start;'><span>-&nbsp;</span><span>Send a follow-up question to the doctor as needed, within 24 hours.</span></li></ul><p style='margin-bottom: 0'>Please note: We may cancel the appointment unless you provide all the necessary medical information as requested.</p></div>", "age": "Age", "agree": "Agree", "all": "All", "all day": "All day", "all doctor": "All doctor", "all existing appointments will be rescheduled": "All existing appointments will be rescheduled", "all features have been hidden": "All features have been hidden", "all medical information is encrypted and secured stored in compliance with HIPPA": "All medical information is encrypted and secured stored in compliance with HIPPA", "all of cmi profits are dedicated to the children’s solidarity fund that pays for destitute vietnamese children to have cardiac surgery at the heart institute": "All of CMI’s profits are dedicated to the Children’s Solidarity Fund that pays for destitute Vietnamese children to have cardiac surgery at The Heart Institute.", "allow the patient to select": "Allow the patient to select", "also, you have right to cancel, see more": "Also, you have right to cancel, see more:", "amount": "Amount", "amount is": "Amount is", "amount to topup is": "Amount to topup is", "an error occurred!": "An error occurred!", "answer": "answer", "answer question": "Answer", "anytime": "Anytime", "anywhere": "Anywhere", "app version": "App version", "application": "Application", "apply": "Apply", "apply code?": "Apply code?", "appointment at clinic": "Appointment at clinic", "appointment is confirmed": "Appointment is confirmed", "appointment time passed": "Appointment time passed", "appointment-general": "In-depth consultation ", "add a family member": "Add a family member", "are you sure to remove it": "Are you sure to remove it", "are you sure you want to delete": "Are you sure you want to delete?", "articles": "Articles", "as-needed": "as needed", "ask a doctor": "Ask a doctor", "asynchronous telehealth": "Asynchronous telehealth", "asynchronous telehealth package": "Asynchronous telehealth package", "membership plus Combo 10 questions": "Membership plus Combo 10 questions", "Waiver of the service fee and other health benefits in 12 months": "Waiver of the service fee and other health benefits in 12 months", "ask a question": "Ask a question", "at a post office or viettel store, you make an interbank transfer. ask the cashier to input your mobile phone number (preferably the one you registered with wellcare) as the note of the transfer.": "<p style=\"\">- At a Post office or Viettel store, you make an interbank transfer. Ask the cashier to input your mobile phone number as the note of the transfer. Cashier will also print a receipt, please capture and send it to Wellcare's zalo/viber/whatsapp <a href=\"tel:***********\" target=\"_blank\">+***********</a> for a clarification.</p><p style=\"\">- 30 minutes after receiving the money, Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.</p><p style=\"margin-bottom: 0px;\">- Call the doctor: From the link we sent or our mobile app, you  have two options: cellular call and internet call.</p>", "at appointment time, patient call doctor by phone or video": "At appointment time, patient call doctor by phone or video", "at convenience stores or supermarkets": "At convenience stores or supermarkets", "at least": "At least", "at least 1 question required": "At least 1 question required", "at the appointment time, open medical record link and press CALL button to connect with doctor": "At the appointment time, open medical record link and press CALL button to connect with doctor", "at time": "At", "atm card": "ATM card", "attachments": "Attachments", "attendtion": "Attention", "attention": "Attention", "audio": "Audio", "auto redirect after": "Auto redirect after", "auto saved": "Saved automatically", "available balance": "Available balance", "back": "Back", "back to home": "back to home", "balance": "Balance", "bank card": "Bank card", "bank transfer": "Bank transfer", "bank transfer description": "Note of bank transfer must be your phone number", "basic package": "BASIC", "before proceeding with short question, please note that:": "Before proceeding with a knowledge question, please note that:", "book": "Book", "book a consultation": "Teleconsultation", "book appointment at doctor offices": "Book appointment at doctor offices", "book follow up": "Follow up  ", "book now": "Book now", "book online consultation with dr {doctor}, {slot} minutes phone or video call": "Book online consultation with dr {doctor}, {slot} minutes phone or video call", "brand guideline": "Brand guideline", "buccal": "buccal", "buy": "buy", "by day": "By day", "by month": "By month", "calendar": "Calendar", "call": "Call", "call ": "Call Now", "call 84 28 3622 6822 to schedule with doctor": "<p>Please text us through <PERSON><PERSON> <a href=\"https://zalo.me/***********\" target=\"_blank\">+84 366 905 905</a> to book a teleconsultation with Dr. %{doctor}</p>", "call again": "Call again", "call by phone": "Call by phone", "call by phone or by Internet": "Call directly to the system phone number or call via the Internet.", "call center": "Call center", "call credit": "Call credit", "call doctor": "Call", "call doctor from a number other than your setting one": "Input your phone number here", "call doctor guide": "Call doctor guide", "call doctor through": "Cellular call", "call doctor via Internet": "VOICE CALL by INTERNET", "call doctors as easily as your loved ones": "Call doctors as easily as your loved ones", "call doctors in three simple steps": "Call doctors in three simple steps", "call dr": "Call Dr", "call dr by other phone": "Call <PERSON> by other phone", "call ended": "Call ended", "call from phone number": "Call from phone number", "call patient via Internet": "VOICE CALL by INTERNET", "call pn": "Call patient", "call pn by other phone": "Call patient by another phone no. instead of...", "call pt": "Call PT", "call support": "Call support", "call time": "Call time", "callback": "Call back", "calling": "Calling...", "cancel": "Cancel", "cancel question": "<PERSON><PERSON> question", "cancelled": "Cancelled", "cannot return": "Cannot return", "capsule": "Capsule", "capture, upload images": "Upload images", "card code": "Card code", "cash": "Get the code to pay by cash", "cash at convenience stores": "Cash", "cash on delivery": "Cash on delivery", "categories": "Categories", "centre medical international": "Centre Medical International", "centre medical international (CMI) is an outpatient clinic fully equipped to provide international standard comprehensive and specialised medical services, all the physicians are french or vietnamese": "Centre Medical International (CMI) is an outpatient clinic fully equipped to provide international standard comprehensive and specialised medical services. All the physicians are French or Vietnamese.", "change": "Change", "change outgoing phone alert": "<PERSON><PERSON> tác này chỉ thay đổi số điện thoại gọi đi đối với cuộc tư vấn này, số điện thoại sổ khám của bạn sẽ không bị thay đổi", "change password": "Change password", "charge this consultation": "Charge this consultation", "charged by minutes of consultation": "Charged by minutes of consultation", "check sms message": "Check sms message", "check your SMS message, prepare medical record and call doctor": "Check your SMS message, prepare medical record and call doctor", "check your sms or the mobile app for the comfirmation you will receive the response within 24 hours please again check your sms or the mobile app": "<p>After payment done:<br>- Check the Wellcare mobile app for the confirmation<br>- You will receive the response within 24 hours, through sms and on the mobile app<br>- We may refuse your question, if it is not properly made<br>- <span style='color: var(--v-error-base)'>No refund</span><br>- <span style='color: var(--v-primary-base)'>A valid amended question can be resubmitted.</span></p>", "Waiver of the service fee for every consultationSpecial rate and prioritised scheduleBoundless answers from our HealthGPTOptional upgrade: Personal doctor,Asynchronous care": "<li>Waiver of the service fee for every consultation</li> <li>Special rate and prioritised schedule </li> <li>Boundless answers from our HealthGPT</li>  <li>Optional upgrade: Personal doctor, Asynchronous care</li>", "Premium Account": "Premium Account", "check your sms or the mobile app for the confirmation": "Check your SMS or the mobile app for the confirmation", "checkbox timeslot": "The call will automatically terminate when the allotted consultation time is used up so the next patient's appointment can take place", "chief complaint": "Chief complaint", "child": "Child", "choose": "<PERSON><PERSON>", "choose a bank": "Choose a bank", "choose a calendar to synchronize": "Choose a calendar to synchronize", "choose a day and a timeslot to continue": "Choose a day and a timeslot to continue", "choose a doctor": "Choose a doctor", "choose a patient to continue": "Choose a patient to continue", "choose a time": "Choose a time", "choose account": "Choose account", "choose an appointment": "Choose an appointment", "choose an option to continue": "Choose an option to continue", "choose appointment time": "Choose appointment time", "choose at lease one day": "Choose at lease one day", "choose available slot": "Choose available slot", "choose consultation package": "Choose consultation package", "choose doctor": "Choose doctor", "choose gender": "Choose gender", "choose package": "Choose package", "choose the amount to topup": "Choose the amount", "choose the duration": "Choose the duration", "choose the time frame": "Choose the time frame", "choose time": "Choose time", "choose type of communication": "Choose type of communication", "choose vendor": "Choose vendor", "choose your date of birth": "Choose your date of birth", "choose membership package": "Choose membership package", "member benefits": "Member benefits", "additional benefits": "Add-on", "Select Member": "Select A Member", "10 Knowledge questions or follow-up messages with your personal doctor": "10 Knowledge questions or follow-up messages with your personal doctor.", "Up to 150 minutes for voice/video calls and voice/text responses": "<div class='text-14'>150-minute consultation through messages, voice or video calls without appointment.</div>", "checkout personal-doctor without specialists": "<div class='text-14'>Up to 150 minutes for voice/video calls and voice/text responses.</div><v-divider style='border: none; border-top: 1px solid rgba(0, 0, 0, 0.12); margin: 8px 0;'></v-divider><ul class='mt-2 pl-0' style='display: grid; row-gap: 12px;'><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Sunday and holidays Surcharge</span><span class='primary--text' style='font-size: 14px;'>570.000₫<span class='personal-doctor-charge'>/Year</span></span></li></ul>", "checkout personal-doctor with specialists": "<div class='text-14'>Up to 150 minutes for voice/video calls and voice/text responses.</div><v-divider style='border: none; border-top: 1px solid rgba(0, 0, 0, 0.12); margin: 8px 0;'></v-divider><ul class='mt-2 pl-0' style='display: grid; row-gap: 12px;'><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Sunday and holidays Surcharge</span><span class='primary--text' style='font-size: 14px;'>570.000₫<span class='personal-doctor-charge'>/Year</span></span></li><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Eminent Provider Surcharge</span><span class='primary--text' style='font-size: 14px; width: 112px; display: inline-block; text-align: end;'>500.000₫<span class='personal-doctor-charge'>/Year</span></span></li></ul>", "checkout personal-doctor desc": "<div class='text-14'>Up to 150 minutes for voice/video calls and voice/text responses.</div><v-divider style='border: none; border-top: 1px solid rgba(0, 0, 0, 0.12); margin: 8px 0;'></v-divider><ul class='mt-2 pl-0' style='display: grid; row-gap: 12px;'><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-16' style='color: rgba(0, 0, 0, 0.87);'>Sunday and holidays Surcharge</span><span class='primary--text' style='font-size: 14px;'>570.000₫<span class='personal-doctor-charge'>/Year</span></span></li><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-16' style='color: rgba(0, 0, 0, 0.87);'>Eminent Provider Surcharge</span><span style='font-size: 14px; width: 112px; display: inline-block; text-align: end; color: #b2b2b2'>500.000₫*<span class='personal-doctor-charge'>/Year</span></span></li></ul>", "payment personal-doctor without specialists": "<div class='text-14'>Up to 150 minutes for voice/video calls and voice/text responses.</div><v-divider style='border: none; border-top: 1px solid rgba(0, 0, 0, 0.12); margin: 8px 0;'></v-divider><ul class='mt-2 pl-0' style='display: grid; row-gap: 12px;'><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Sunday and holidays Surcharge</span><span class='primary--text' style='font-size: 14px;'>570.000₫<span class='personal-doctor-charge'>/Year</span></span></li></ul><ul style='list-style: none; padding-left: 0; align-items: flex-start;'>Note:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-error-base)'>No refund</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-primary-base)'>Re-schedule once, within 7 days</span></li></ul>", "payment personal-doctor with specialists": "<div class='text-14'>Up to 150 minutes for voice/video calls and voice/text responses.</div><v-divider style='border: none; border-top: 1px solid rgba(0, 0, 0, 0.12); margin: 8px 0;'></v-divider><ul class='mt-2 pl-0' style='display: grid; row-gap: 12px;'><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Sunday and holidays Surcharge</span><span class='primary--text' style='font-size: 14px;'>570.000₫<span class='personal-doctor-charge'>/Year</span></span></li><li class='d-flex align-center justify-space-between font-weight-bold'><span class='text-14' style='color: rgba(0, 0, 0, 0.87);'>Eminent Provider Surcharge</span><span class='primary--text' style='font-size: 14px; width: 112px; display: inline-block; text-align: end;'>500.000₫<span class='personal-doctor-charge'>/Year</span></span></li></ul><ul style='list-style: none; padding-left: 0; align-items: flex-start;'>Note:<li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span>Lacking of necessary medical information may lead to a cancellation</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-error-base)'>No cash refund</span></li><li style='padding-left: 8px; display: flex; align-items: flex-start;'><span>-&nbsp;</span><span style='color: var(--v-primary-base)'>Re-schedule once, within 7 days</span></li></ul>", "program applies to children under 6 years old": "Program applies to children under 6 years old", "program applies to children under 16 years old": "Program applies to children under 16 years old", "Eminent": "Eminent", "learn more": "Learn more?", "city": "City", "click": "Click", "close": "Close", "collapse": "Collapse", "come to one of the following stores": "Come to one of the following stores:", "coming soon": "Coming soon", "company name": "Company name", "complete": "Complete", "completed": "Completed", "completed call": "Completed call", "conditions diagnosed or treated via telemedicine": "Conditions diagnosed or treated via telemedicine", "confirm": "Confirm", "confirm merge": "Confirm merge", "confirm payment": "Confirm payment", "confirm phone": "Confirm phone", "confirm step question": "to receive a medical form to fill with more information, relevant images & videos, and to have our physicians interpret all of your symptoms to you.", "confirmation": "Confirmation", "consult for": "Consult for", "consult for adult": "Consult for adult", "consult for kids": "Consult for Kids", "consult for mental health": "Consult for mental health", "consult with doctor on time": "Consult with doctor on time", "consultation": "Consultation", "consultation by appointment": "Consultation by appointment", "consultation by appointment then followup within": "Consultation by appointment then followup within", "consultation by appointment within the timeslot specified": "Consultation by appointment within the timeslot specified", "consultation details": "Consultation details", "consultation hours": "Consultation hours", "consultation packages": "Your packages", "consultation questions": "Questions", "consultation request": "Consultation request", "consultation was cancelled by": "Consultation was cancelled by", "consultation with 7 days followup, suitable when you want to keep communicating with doctors until get well": "Consultation with 7 days followup, suitable when you want to keep communicating with doctors until get well", "consultation-question": "Ask doctor", "consultation-question-provider": "Ask doctor", "consultation-question-provider.private-doctor": "ASK DOCTOR", "consultations that have 4 or 5 start ratings": "Consultations that have 4 or 5 start ratings", "consultations that have detailed treatment notes, 16% have e-prescriptions": "Consultations that have detailed treatment notes, 16% require prescriptions", "consultations today": "Consultations today", "consulting duration": "Consulting duration", "contact": "Contact", "continue": "Continue", "continue question": "Next", "continue updating": "Continue updating", "convenience stores": "Convenience stores", "convenience stores or supermarkets": "Get the code to pay by cash", "convert flexible benefits": "Convert flexible benefits", "copied": "<PERSON>pied", "country": "Country", "coupon applied": "Coupon applied", "create": "Create", "create consultation schedule": "Create consultation schedule", "create event": "Create event", "create new account": "Create new account", "created successfully": "Created successfully", "credit": "Credit", "credit card or debit card": "Credit card or Debit card", "cs": "CS", "current medication": "Current medication", "current password is invalid": "Current password is invalid", "current phone number": "Current phone: +{number}", "currently, Internet call is supported from IOS version 11 or above": "Currently, Internet call is supported from IOS version 11 or above", "customer support": "Customer support", "dans": "Within", "dashboard": "Dashboard", "data not affected when role is changed": "Data not affected when role is changed", "data of the two accounts (including relatives, medical records, profiles, transactions) will be merged": "Data of the two accounts (including relatives, medical records, profiles, transactions) will be merged", "date of birth": "Date of birth", "day": "Day", "days in week": "Days in week", "days old": "days old", "debit card description": "<p>Within 30 minutes after receiving the payment, Wellcare will send you a confirmation on the mobile app.</p> <p style=\"margin-bottom:0px\">&nbsp;Make a cellular call or an internet call to the doctor from our mobile app: <a href='https://khamtuxa.vn/download' style='text-decoration: none' class='primary--text font-italic' target='_blank'>https://khamtuxa.vn/download</a> </p>", "decline": "Decline", "default view": "Default view", "default_": "<PERSON><PERSON><PERSON>", "delivered": "Delivered", "delivery address": "Address", "delivery information": "Delivery Information", "delivery medication": "Drug delivery", "delivery note": "Note (optional)", "dental": "dental", "describe and upload images, video clips and files related to current medications, lab results, imaging results, doctor will review carefully your record and may request additional information": "Describe and upload images, video clips and files related to current medications, lab results, imaging results. Doctor will review carefully your record and may request additional information", "description": "Description", "detailed instructions": "Detailed instructions", "detailed description": "Detailed description", "dhl telemedicine": "DHL Telemedicine", "diagnosis": "Diagnosis", "diagnosis.already_existed": "Diagnose is already existed", "diastolic": "diastolic", "disappointed": "disappointed", "discard change": "Discard change", "discount": "Discount", "distributed by long chau pharmacy": "Distributed by Long Chau Pharmacy", "district": "District", "do you want to cancel?": "Do you want to cancel?", "doctor": "Doctor", "doctor article": "Doctor Articles", "doctor does not support this": "Doctor does not support this", "doctor response time": "❗️ The doctor will respond within {time} hours", "doctor sending patient": "doctor sending patient", "doctors": "Doctors", "doctors and wellcare reserve the right to cancel the appointment when you decline to provide additional proper information so as to achieve optimal telehealth results. fee will be refunded to your wellcare account for later uses.": "Doctors and Wellcare reserve the right to cancel the appointment when you decline to provide additional proper information so as to achieve optimal telehealth results. Fee will be refunded to your Wellcare account for later uses.", "doctors answer": "Waiting for answer", "doctors are chosen based on: experience, expertise and service mindset": "Doctors are chosen based on: experience, expertise and service mindset", "doctors practice evidence based medicine, avoid overuse of drugs and labs, aim at treatment plans that are safe and minimally invasive": "Doctors practice evidence based medicine, avoid overuse of drugs and labs, aim at treatment plans that are safe and minimally invasive", "document": "Document", "does not support uploading video longer than 1 minute": "Does not support uploading video longer than 1 minute", "don't receive the otp?": "Don't receive the otp?", "done": "Done", "dont show again": "Dont show again", "download at": "Download at", "download now": "Download now", "dr": "Dr.", "drop": "Drop", "duration": "Duration", "e-medical record": "E-medical record", "each time use": "Each time use", "earliest": "Calendar", "easy access to your preferred doctor": "Easy access to your preferred doctor", "easy payment": "Easy payment", "edit": "Edit", "edit event": "Edit event", "edit question": "Edit question", "education": "Education", "education messages have been sent to patients to help develop healthy living habits": "Education messages have been sent to patients to help develop healthy living habits", "effective": "Effective", "effective for 12 months": "Effective for 12 months", "12 months of validation until": "12 months of validation until", "email": "Email", "email content not found": "Email content not found", "email <EMAIL> or sms +84 366 905 905: name, phone, address to have card delivered. If you already have a card, scratch and enter code:": "Email <EMAIL> or sms +84 366 905 905: name, phone, address to have card delivered. If you already have a card, scratch and enter code:", "email not validated": "Email not validated", "email us or message to hotline +84 366 905 905 your NAME, PHONE, ADDRESS for home delivery": "<p>-Email us or message to hotline +84 366 905 905 your NAME, PHONE, ADDRESS for home delivery.</p><p style=\"margin-bottom:0px\">&nbsp;- If you already have a code, please key in:</p>", "employeeId": "Employee Id", "employeeNumber": "Employee Number", "empty search": "No matching results", "end": "End", "end date": "End date", "end of list": "End of List", "end time": "End time", "engage with your doctor for": "Engage with your doctor for", "engage with your doctor for 12 months, charged by minutes of consultation, suitable if you seek regular help": "Engage with your doctor for 12 months, charged by minutes of consultation, suitable if you seek regular help", "engage with your doctor for 12 months, suitable if you seek regular help": "Engage with your doctor for 12 months, suitable if you seek regular help", "enter a comment on the consultation, the doctor and the wellcare system": "Enter a comment on the consultation, the doctor and the Wellcare system", "enter a password": "Enter a password", "enter a valid amount of money": "Enter a valid amount of money", "enter account phone": "Please enter the phone number of your examination book", "enter another amount": "Enter another amount", "enter card code": "Enter card code", "enter chief complaint": "Enter chief complaint...", "enter code": "Enter code", "enter code to continue": "Enter code to continue", "enter current password": "Enter current password", "enter current password to continue": "Enter current password to continue", "enter gift code": "Enter gift code", "enter new password": "Enter new password", "enter otp": "Enter OTP", "enter otp to continue": "Enter OTP to continue", "enter question to continue": "Enter question to continue", "enter symptoms": "Enter symptoms...", "enter the promo code": "Enter the promo code", "enter title": "Enter title", "enter your password": "Welcome back, enter your password to continue", "enter your phone number": "Enter your phone number", "even better than physical visit": "Even better than physical visit", "event": "Event", "every-01-hour-as-needed": "every 1 hour as needed", "every-02-hours-as-needed": "every 2 hours as needed", "every-03-hours-as-needed": "every 3 hours as needed", "every-10-minutes-as-needed": "every 10 minutes as needed", "every-20-minutes-as-needed": "every 20 minutes as needed", "every-30-minutes-as-needed": "every 30 minutes as needed", "every-4-6-hour-as-needed": "every 4 - 6 hours as needed", "every-other-day": "every other day", "evidence based medicine": "Evidence based medicine", "excellent": "excellent", "expand": "Expand", "experience": "Experience", "expiration date": "Expiration date", "explore": "Explore", "external": "external", "eye-drops": "eye drops", "favorite provider": "Favorite doctors", "featured pediatricians": "Featured pediatricians", "fee policy": "Fee policy", "female": "Female", "fill out all required information to continue": "Fill out all required information to continue", "filled by doctor": "Filled by doctor", "filled by dr": "Filled by DR", "filled by patient": "Filled by patient", "filled by pt": "Filled by PT", "filter": "Filter", "filter condition": "Filter condition", "find momo topup locations": "Find momo topup locations", "find momo topup locations (see Guide below)": "Find a momo top-up location (below)", "five-times-a-day": "five times a day", "follow up": "Follow up", "follow-up": "Follow up  ", "for a period": "For a period", "for any extra payment that out of manulife free of charge services within 30 minutes after receiving the payment wellcare will send you a text message to confirm including the link to the medical record where you could upload the images and videos it may take longer if you transfer manually through internet banking depends on the time it arrives": "For any extra payment that out of Manulife free-of-charge services: Within 30 minutes after receiving the payment, Wellcare will send you a text message to confirm, including the link to the medical record where you could upload the images and videos. It may take longer if you transfer manually through internet banking, depends on the time it arrives.", "for assistance": "For assistance", "for quick support please copy or screenshot the error message and send us": "For quick support please copy or screenshot the error message and send us", "for someone else": "For someone else", "for this consultation": "for this consultation", "for this transaction only": "for this transaction only", "only": "only", "for this transaction or more for later uses": "for this transaction or more for later uses", "forgot password": "Forgot password", "four-times-a-day": "four times a day", "free": "Free", "from": "From", "from date": "From date", "from here": "From here", "frustrated": "frustrated", "full refund to your Wellcare’s account when declined": "Full <span style=\"font-weight:700\">refund</span> to your Wellcare’s account when declined", "fullname": "Full name", "fully booked": "Fully booked", "further instruction": "Further instruction", "gender": "Gender", "get well anywhere": "Get well anywhere", "group doctors": "Group Doctors", "growth chart": "Growth chart", "guest": "Guest", "guest permission": "Guest permission", "guide": "Guide", "have GotIt voucher/coupon?": "Have GotIt voucher/coupon?", "have a gift code?": "Have a gift code?", "have an account yet?": "Have an account yet?", "headCircumference": "Head Circumferece", "height": "Height", "hello": "Hello", "here": "Here", "hi": "Hi", "history": "History", "hold and drag to change order": "Hold and drag to change order", "holiday": "Holiday", "holiday surcharge": "Holiday surcharge", "home-treatment-f0": "Home Treatment", "homepage": "Homepage", "how would you rate the doctor?": "How would you rate the doctor?", "how would you rate wellcare?": "How would you rate wellcare?", "i already know that in case i worry, i definitely could ask wellcare": "I already know that in case I worry, I definitely could ask Wellcare +*********** (Zalo/Viber/Whatsapp) to request urgent advice from doctors prior to the appointment time.", "if there is anything you believe we should improve to meet your expectation please tell us": "If there is anything you believe we should improve to meet your expectation, please tell us!", "image": "Image", "images and video": "Images and video", "imaging results": "Imaging", "in account": "In account", "in-both-eyes": "in both eyes", "in-the-left-eye": "in the left eye", "in-the-right-eye": "in the right eye", "incoming call": "Incoming call", "inconsultation": "In-consultation", "indepth": "Indepth", "indepth consultation": "indepth consultation", "indepth consultation, without waiting or travelling": "Indepth consultation, without waiting or travelling", "inhalation": "inhalation", "input doctor name": "Input doctor name", "input specialty": "Input specialty", "insert here": "Insert here", "install wellcare app and connect to our doctors in multiple ways": "Install Wellcare app and connect to our doctors in multiple ways", "instead of": "Instead of", "instruction step": "Instruction", "intramuscularly": "Intramuscularly", "intravenous": "intravenous", "introduction": "Introduction", "about": "About", "introductory video": "Introductory video", "invalid date": "invalid date", "invalid date of birth": "Invalid date of birth", "invalid email": "Invalid email", "invalid otp": "Invalid OTP", "invalid password": "Invalid password", "invalid phone": "Invalid phone", "invalid time": "invalid time", "invite": "Invite", "invoice information": "Invoice information", "is having a pending consultation with": "is having a pending consultation with", "is no longer available": "is no longer available", "its not you, its us. go back and try again": "It's not you, it's us. Go back and try again", "just now": "Just now", "keep it short & simple, straight to the point (maximum 250 characters).": "Keep it short & simple, straight to the point (maximum 250 characters).", "la0208 lexington office, 67 mai chi tho, d2, hcmc": "LA0208 Lexington Office, 67 Mai Chi Tho, District 2, Ho Chi Minh City", "lab results": "Lab results", "language": "Language", "later": "Later", "licensed ********** by DPI on 8 May 2015": "Licensed ********** by DPI on 8 May 2015", "limited": "limited", "list of doctors": "Doctors", "list of specialties": "Specialties", "loading": "Loading...", "local bank card": "Credit card or debit card", "locked": "Locked", "log in to continue with your saved data": "Log in to continue with your saved data", "login": "<PERSON><PERSON>", "login by": "Login by", "login now": "Login now", "logout": "Log out", "logout from current device": "Logout from current device", "made the call": "{name} made the call", "make a list of concerns but keep it short and focused. Each one is a question.": "Make a list of concerns but keep it short and focused. Each one is a question.", "male": "Male", "mannual instruction": "Mannual instruction", "manual instruction": "Manual instruction", "max": "Max", "maximum 50 characters": "Maximum 50 characters", "me": "Me", "medical assistant": "Medical assistant", "medical record": "Medical record", "medical record link and guide has been sent to your phone": "Medical record link and guide has been sent to your phone", "medical records have just been updated": "Medical records have just been updated", "medication": "Medication", "medium-phone": "Voice call", "medium-video": "Video call", "meet our pediatricians": "Meet our pediatricians", "name account": "Name account", "wellcare service": "THẺ DỊCH VỤ", "membership": "Membership", "membership card": "MEMBERSHIP", "message": "Message", "minimum": "Minimum", "minimum 10 characters": "Minimum 10 characters", "minimum 25 characters": "Minimum 25 characters", "minimum topup 20,000đ": "Minimum topup 20,000đ", "minute": "Minute", "minutes": "Minutes", "minutes/consultation": "Minutes/consultation", "missed": "Missed", "missed call": "Missed call", "mission: Wellcare helps you maintain a good health and when you have problems we connect you with the best specialists": "Mission: Wellcare helps you maintain a good health and when you have problems we connect you with the best specialists", "Usable balance is valid in 6 months": "Usable balance is valid in 6 months.", "The remaining balance will be kept in your account and can be used within 6 months": "Any remaining balance will be automatically converted into a coupon valid for 6 months from the payment date.", "ml, mg, capsule": "Ml, mg, capsule...", "moca e-wallet": "Moca", "momo e-wallet": "<PERSON><PERSON> ", "momo locations": "Momo locations", "momo success": "Success!", "momo success description": "<p>- Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.</p><p style=\"margin-bottom:0px\">&nbsp;- Call the doctor: From the link we sent or our mobile app, you have two options: cellular call and internet call.</p>", "money transfer at vnpost or viettel stores": "Money transfer at VNPost or Viettel stores", "month": "Month", "months": "Months", "months old": "months old", "more details": "More details", "more events": "More events", "my google calendar": "My google calendar", "my profile": "My profile", "name": "Name", "name require at least 6 characters": "Name require at least 6 characters", "nasal": "nasal", "new phone number": "New phone number", "new update": "New update", "next": "Next", "no": "No", "no appointment today": "No appointment today", "no appointment yet": "No appointment yet", "no consultation yet!": "No consultation yet!", "no data": "No data", "no event yet": "No event yet", "no message yet": "No message yet", "no phone connect": "No phone connect", "no phone yet": "No phone yet", "no questions yet!": "No questions yet!", "no timeslots available, please choose another doctor or call support": "No timeslots available, please choose another doctor or call support", "no treatment note yet": "Patient is awaiting for your note", "not enough money": "Not enough money in wallet", "not fixed": "Not fixed", "not open yet": "Not open yet", "not unless you input the proper information": "Not unless you input the proper information", "note": "Note", "note to the delivery man": "Note to the delivery man", "number": "Number", "number days": "{number} days", "number hours": "{number} hours", "number minutes": "{number} minutes", "number of member": "Number of member", "on average": "On average", "once transfered successfully, you will receive SMS from Wellcare, then return to homepage, choose again your timeslot and hit CONFIRM button.": "Once transfered successfully, you will receive SMS from Wellcare, then return to homepage, choose again your timeslot and hit CONFIRM button.", "once you have money in your mHealth account, make an appointment by returning to the home page, selecting a doctor, choosing a time and pressing the CONFIRM button.": "Once you have money in your mHealth account, make an appointment by returning to the home page, selecting a doctor, choosing a time and pressing the CONFIRM button.", "once-a-day": "once a day", "once-a-week": "once a week", "onduty schedule": "Onduty schedule", "online consultation": "Online consultation", "open": "Open", "open the medical record from SMS or mobile app": "Open the medical record from SMS or mobile app", "open your medical record, press CALL DR button": "Open your link, choose CALL DOCTOR button (There are options to choose cellular or internet call, or set to call from another phone number)", "or": "Or", "or contact": "Or contact", "or top up more for later uses": "Or top up more for later uses", "orally": "Orally", "order confirmation": "Order confirmation", "other": "Other", "other amount": "Other amount", "other files": "Others", "other images": "Other images", "other options": "Other options", "otic": "otic", "otp resent": "Otp resent", "our doctors responses will not include any diagnoses or medicines": "<span style=\"font-weight:700\">No</span> diagnosis or prescription will be given", "out of office": "Out of office", "out of schedule": "Out of registration date ", "outcome of consultation 70% depends on quality of information you provide": "Outcome of consultation 70% depends on quality of information you provide", "outdated please upgrade new version": "Outdated please upgrade new version", "paid": "Paid", "pack": "Pack", "parent": "Parent", "password": "Password", "password not match": "Password and confirmation password do not match", "password require at least 6 characters": "Password require at least 6 characters", "password reset, login to continue": "Password reset, login to continue", "patient": "Patient", "patient information": "Patient information", "patient is invited to rate doctor and service after consultation completed": "Patient is invited to rate doctor and service after consultation completed", "patient profile": "Patient profile", "patients": "Patients", "patients can select one or multiple appointment slot in an consultation": "Patients can select one or multiple appointment slot in an consultation", "pay": "Pay", "pay directly on momo app": "Through momo e-wallet", "pay directly on zalopay app": "Pay directly on zalopay app", "pay for consultation fee": "Pay for consultation fee", "pay for telemedicine service by credit card, cash at convenient store or bank transfer, eligible for insurance claim": "Pay for telemedicine service by credit card, cash at convenient store or bank transfer. Eligible for insurance claim", "pay for the services conveniently by bank transfer, credit cards, momo e-wallet or cash at nearby convenience stores": "Pay for the services conveniently by bank transfer, credit cards, momo e-wallet or cash at nearby convenience stores", "pay from": "Pay from", "pay via": "Pay via", "payment": "Successful!", "payment code": "Payment code", "payment failed": "Payment failed", "payment method": "Payment methods", "payment successful": "Payment successful", "payment via zalopay wallet": "Payment via ZaloPay wallet", "payoo e-wallet": "<PERSON><PERSON>", "pending": "Comfirmation", "personal information": "Personal information", "personalize features": "Personalize features", "phone": "Phone", "voice call": "Voice call", "phone not available": "Phone not available", "phone number": "Phone number", "phone or video call": "Tele-consultation", "switch to teleconsultation": "Switch to teleconsultation", "pill": "<PERSON>ll", "placed": "Already requested delivery", "please agree to the consulting fee policy": "Please agree to the consulting fee policy", "please call": "Please call", "please call ontime": "Please call ontime", "please choose medium before continuing.": "Please choose medium before continuing.", "please choose package before continuing": "Please choose package before continuing.", "please choose private provider before continuing": "Please choose private doctor before continuing", "please choose provider": "Please choose provider", "please choose the time before continuing": "Please choose the time before continuing", "please complete the complete medical record before continuing": "Please complete the complete medical record before continuing", "please confirm question": "Please confirm continuing to \"Ask doctor\" or change to \"In-dept consultation\"", "please describe in details current health conditions": "Please describe in details current health conditions", "please enter a valid amount": "Please enter a valid amount", "please enter fullname": "Please enter fullname", "please enter main reason for consultation": "Please enter main reason for consultation", "please enter title to continue": "Please enter title to continue", "please enter valid format": "Please enter valid format", "please enter your name": "Please enter your name", "please enter your phone": "Please enter your phone", "please enter your question": "Please enter your question", "please fill in information": "Please fill in information", "please reopen your health record after receiving notification that this consultation is completed": "Please reopen your health record after receiving notification that this consultation is completed", "please select": "Please select", "please select a consultation time frame before continuing": "Please select a consultation time frame before continuing", "please select a person to be examined before continuing": "Please select a person to be examined before continuing", "please select a reason to cancel the consultation": "Please select a reason to cancel the consultation.", "please select another appointment time": "Please select another appointment time.", "please select another password": "Please select another password", "please select duration": "Please select duration before continuing", "please try again": "Please try again", "please upload images and video to your e-medical record (from our mobile app or the link sent through sms) after receiving our confirmation": "Please upload images and video to your e-medical record (from our mobile app or the link sent through sms) <span style=\"    color: #ff5722;\">AFTER</span> receiving our confirmation.", "please wait": "Please wait", "post delivery to home within 3 working days": "COD by postman", "postal code": "Postal code", "pre confirm step question": "Unlike teleconsultation - providing you a two-way communication with a physician, knowledge questions seek to solve only <span style=\"font-weight:700\">simple inquiries</span> and provide no diagnosis or prescription revision. If a specific diagnosis is what you are looking for, then make a", "preferential fee": "<PERSON><PERSON> <PERSON><PERSON> đãi", "premium package": "PREMIUM", "prepaid card": "Prepaid card", "prepaid wellcare card": "Wellcare's Account", "prepare medical record": "Prepare medical record", "prescription": "Prescription", "prescription frequency": "Prescription frequency", "prescription instruction": "Prescription instruction", "prescription not needed for now": "Not needed for now", "prescription take": "Prescription take", "press": "Press", "press again to quit app": "Press again to exit the app", "preview": "Preview", "price": "Price", "privacy policy": "Privacy policy", "private": "Private", "personal doctor": "Personal doctor", "personal doctor rate": "Personal Doctor Rate", "Phí Bác Sĩ Riêng": "Personal Doctor Rate", "private doctor package": "Private doctor package", "private-doctor": "Private doctor", "processing": "Processing", "promotion code/gift code": "Promotion code/gift code", "province or city": "Province / City", "pt": "Pt", "public": "Public", "quantity": "Quantity", "quantity total": "Total", "question includes only text, no images or video clips.": "Question includes only text, <span style=\"font-weight:700\">no</span> images or video clips.", "questions": "Questions", "quick call": "Quick call", "rate your doctor": "Rate your doctor", "rating": "Rating", "re-enter new password": "Re-enter new password", "re-enter password": "Re-enter your password", "recipient email": "Recipient email", "recommended prescription": "RECOMMENDED PRESCRIPTION", "Recommendation": "Recommendation", "Bundle & Save": "Bundle & Save", "record video": "Record video", "rectal": "rectal", "rectally": "rectally", "register": "Register", "rejected": "Rejected", "relationship": "Relationship", "relationship type": "relationship type", "reload page, or contact": "Reload page, or contact", "remind": "Remind", "remove": "Remove", "remove confirmation": "Remove confirmation", "remove relative": "Remove relative", "remove this event": "Are you sure to remove this event from your calendar", "removed": "Removed", "removed successfully": "Removed successfully", "renewal": "Renewal", "response before": "Response before", "reputable doctors": "Reputable doctors", "request": "Request", "require headphone and strong, reliable internet": "Stable internet is a must and earphone is preferable", "require strong, reliable Internet from both sides at consultation time": "Require strong, reliable Internet from both sides at consultation time", "required field": "Required field", "required question": "Required", "reschedule for an earlier time if available": "Reschedule for an earlier time if available", "rescheduled successfully": "Rescheduled successfully", "reset password": "Reset password", "reset your password": "Reset your Password", "route of administration": "Route of administration", "same meaning": "Same meaning", "satisfied": "Satisfied", "save": "Save", "search": "Search", "search again": "Search again", "search doctor": "Search doctor", "search doctor by name": "Search doctor by name or specialty", "search not found": "No results found", "searching": "Searching", "seconds": "Seconds", "secured online medical record, accessible from your computers or phones": "Secured online medical record, accessible from your computers or phones", "security certificates": "Security certificates", "see more": "See more", "show less": "Show less", "select a date": "Select a date", "select audio from gallery": "Select audio from gallery", "select call method": "Select call method", "select card type": "Choose card type", "select country code": "Select country code", "select image from gallery": "Select image from gallery", "select video from gallery": "Select video from gallery", "send your first message": "Send your first message", "services": "Services", "set password": "Enter a password", "setting consultation schedule": "Consultation schedule", "setting consultation schedule (private doctor)": "Setting consultation schedule (private doctor)", "setting fee": "Setting fee", "setting for consultations": "Setting for consultations", "share": "Share", "short question": "Knowledge question", "should you have any discomfort while taking medicine, please do get back to the physician for revision of prescription or to the nearest medical center for help.": "Should you have any discomfort while taking medicine, please do get back to the physician for revision of prescription or to the nearest medical center for help.", "show the payment code to the cashier saying you want to pay for the transaction type: 888": "Show the payment code to the cashier saying you want to pay for the transaction type: \"888\".", "shrink": "Shrink", "sibling": "Sibling", "skip": "<PERSON><PERSON>", "slot duration": "Slot duration", "sms or call +84 366 905 905 to confirm": "Sms or call +84 366 905 905 to confirm.", "sms or phone hotline": "Sms or phone hotline", "sms, phone, or video call anytime to your doctor": "Sms, phone, or video call anytime to your doctor", "soldout": "Soldout", "something went wrong": "Something went wrong.", "sort by": "Sort by", "speciality": "Specialty", "specialty": "Specialty", "spouse": "Spouse", "spray-nasal": "spray nasal", "start": "Start", "start date": "Start date", "started call": "Started call", "stat-in-the-clinic": "STAT in the clinic", "state": "State", "status": "Status", "stay in touch": "Stay in touch", "stay in touch with your doctors whenever you are, easy follow-ups, save your time from traveling": "Stay in touch with your doctors whenever you are. Easy follow-ups. Save your time from traveling.", "step": "Step", "still not satisfied": "Still not satisfied", "store and supermarkets accept momo": "Top up to our momo's account", "stores": "Stores", "stores momo": "Momo store", "stores which support momo topup": "Stores which support momo topup", "strong, reliable internet required": "Strong, reliable internet required", "subcutaneous": "subcutaneous", "sublingual": "sublingual", "submit": "submit", "subtotal": "Subtotal", "subtotal summary": "Subtotal", "success! check your wellcare account (if top-up solely), or sms for the appointment confirmation": "Success! Wellcare will text you a confirmation including the link to the medical record where you could upload the images and videos.\\nCall the doctor: From the link we sent or our mobile app, you have two options: cellular call and internet call.", "successful": "Successful", "successful order confirmation": "Successful order confirmation", "successful payment, via medical link in sms message.": "successful payment, via medical link in sms message.", "successfully added": "Successfully added", "successfully canceled the appointment": "Successfully canceled the appointment!", "suggested prescription": "Recommended prescription", "suggested prescription for reference only - based on information provided by patient on medical record and during consultation, patient should stop using upon adverse symptoms, or visit the nearest clinic": "Recommended prescription is for reference purpose - based on information provided on the medical record and conversation between you and the physician within the context of the teleconsultation. We both understand that you decide whether to take medicine at your own will.", "suitable when you want to keep communicating with doctors until get well": "Suitable when you want to keep communicating with doctors until get well", "suitable when you want to seek help regularly from doctor": "Suitable when you want to seek help regularly from doctor", "support": "Support", "surcharge on Sundays and holidays": "Surcharge on Sundays and holidays", "switch to patient role": "Switch to patient role", "switch to provider role": "Switch to provider role", "synchronize": "Synchronize", "synchronize with Google": "Synchronize with Google", "syringe": "Syringe", "systolic": "systolic", "service fee": "Service fee", "annual service fee": "Annual service fee", "one-time": "One-time", "tablespoon": "Tablespoon", "take picture": "Take picture", "tax code": "Tax code", "tax identification number": "Tax identification number", "teaspoon": "Teaspoon", "technical support": "Technical support", "teleconsultation": "teleconsultation", "telemedicine": "Telemedicine", "telemedicine charge": "Telemedicine charge", "telemedicine expenses may be eligible for insurance claims, please check with your insurers": "Telemedicine expenses may be eligible for insurance claims, please check with your insurers", "telemedicine for adults": "Telemedicine for adults", "telemedicine for kids": "Telemedicine for kids", "telemedicine for mental health": "Telemedicine for mental health", "telemedicine hours": "Telemedicine hours", "telemedicine in three simple steps": "Telemedicine in three simple steps", "telemedicine mobile app": "Telemedicine mobile app", "telemedicine service": "Telemedicine service", "telemedicine: video or phone call with your preferred doctor": "Telemedicine: video or phone call with your preferred doctor", "terms and conditions": "Terms and conditions", "text only, no images or videos": "Text only, no images or videos.", "thanks so much for sharing your experience with us": "Thanks so much for sharing your experience with us", "the appointment time": "The appointment time", "the balance is not enough. please top up or choose another option.": "The balance is not enough. Please top up or choose another option.", "the call is automatically terminated when time is up": "The call is automatically terminated when time is up", "the call will automatically terminate when the allotted consultation time is used up so the next patient appointment can take place": "The call will automatically terminate when the allotted consultation time is used up so the next patient's appointment can take place", "the doctor may reschedule; contact us if it is inconvenient for you": "The doctor may reschedule; contact us if it is inconvenient for you", "the exact age and gender of the patient is important for doctor answer": "The exact age and gender of the patient is important for doctor answer", "the main reason from 7-10 words": "The main reason from 7-10 words", "the most trusted telemedicine platform": "The most trusted telemedicine platform", "the pride of our doctors": "The pride of our doctors", "the selected date has no available slots, please choose another date": "The selected date has no available slots, please choose another date", "the selected time is invalid": "Time selected is invalid, please select another time", "then text or call": "Then text or call to our hotline", "the time slot is on hold for 5 minutes awaiting payment": "The time slot is on hold for 5 minutes awaiting payment", "there will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, Wellcare may reschedule again": "Receive a notification when the physician reschedules (sooner or later, because of unexpected happenings). Contact Wellcare for another rescheduling if I am not available at that time.", "there will be a notification if the doctor reschedules sooner or later because of unexpected events if it is inconvenient for you wellcare may reschedule again": "There will be a notification if the doctor reschedules (sooner or later, because of unexpected events). If it is inconvenient for you, contact Wellcare to reschedule again.", "this activation is required only for the first time use.": "This activation is required only for the first time use.", "this consultation": "Consultation", "this field is required, please check to continue": "This field is required, please check to continue", "this field is required, please enter to continue": "This field is required, please enter to continue", "this is medical record link (also in sms)": "This is medical record link (also in sms)", "this month": "This month", "this phone already existed in the system": "This phone already existed in the system", "this timeslot is holding in": "This timeslot is holding in", "this timeslot is on hold for 5 minutes. please move to next step!": "This timeslot is on hold for 5 minutes. Please move to NEXT step!", "this week": "This week", "thousands of thanks": "Thousands of thanks", "three-times-a-day": "three times a day", "throat-spray": "throat spray", "through moca e-wallet": "Through moca e-wallet", "through payoo e-wallet": "Through Payoo e-wallet", "through zalopay e-wallet": "Through zalopay e-wallet", "ticket": "Ticket", "ticket info": "Ticket information", "time": "Time", "time on duty": "On duty", "time patient can call": "Time patient can call", "timezone": "Timezone", "to": "To", "to avoid misadvise, Wellcare may decline to forward those questions that include new symptoms or any topic that has not been discussed with the physician": "To avoid misadvise, Wellcare may decline to forward those questions that include new symptoms or any topic that has not been discussed with the physician.", "to confirm": "to confirm", "to continue": "To continue", "to date": "To date", "to download mobile app": "To download mobile app", "to receive medical record link": "To receive medical record link", "to retry": "To retry", "to system?": "To system?", "to view phone": "To view phone", "today": "Today", "tool": "Tool", "top up via": "Top up via", "top up your prepaid card": "Top up your prepaid card", "topup": "Top up", "topup momo account +84 366 905 905": "Topup momo account +84 366 905 905.", "topup successfully": "Topup successfully", "topup to Momo phone number": "Topup to <PERSON><PERSON> phone number", "topup to continue": "Topup to continue", "total": "Total", "total points": "Total Points", "points": "point(s)", "touch the CALL button on time": "Touch the CALL button on time", "touch the call button on the e-medical record ontime": "Touch the call button on the e-medical record ontime", "touch to add your date of birth": "Touch to add your date of birth", "transaction": "Transaction", "transactions and topup": "Transactions and topup", "transfer from a atm card": "Transfer from a ATM card", "transfer from a bank": "Transfer from a bank", "transfer to": "Transfer to", "treatment note": "Notes", "try again": "Try again", "twice-a-day": "twice a day", "type description": "Type description", "type here": "Type here", "type message": "Type message...", "type name to search": "Type name to search...", "type or select": "type or select", "type question": "Type question...", "ultrasound": "Ultrasound", "unable to delete this relative": "Unable to delete this relative", "unit": "Unit", "unlike teleconsultation, short question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.": "Unlike teleconsultation, knowledge question solves only simple inquiry about selfcare. Please ask one thing at a time. Keep it short, simple, and straight to the point. No abbreviation or slang.", "up to": "Up to", "upcoming appointment": "⏰ Upcoming appointment", "upcoming appointments": "Upcoming appointments", "update": "Update", "update change": "Update change", "update diagnosis": "Update diagnosis", "update failed": "Update failed", "update information": "Update your profile", "update now": "Update now", "updated": "Updated", "updated successfully": "Updated successfully", "upload": "Upload", "upload cancelled": "Upload cancelled", "upload documents": "Upload documents", "upload failed": "Upload failed", "upload file": "Upload", "upload files and videos on next screen": "Upload files and videos on next screen", "upload from gallery": "Upload from gallery", "upload images & video upon completion": "Upload images & video upon <span style=\"color: #ff5722;\">Completion</span>", "upload images & videos": "Upload images & videos", "upload images & videos from the link in the sms or our mobile app assp, doctor will examine them": "Upload images & videos from the link in the sms or our mobile app assp, doctor will examine them", "upload images and videos after you are done with the booking": "Upload images and videos <span style=\"color:#ff5722;\"> AFTER </span> you are done with the booking", "upload images and videos to your e medical record shortly after the appointment is confirmed because the doctor will read it thoroughly cellular call or internet call using wellcare app on schedule": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly Cellular Call or Internet Call (using Wellcare app) on schedule", "upload images and videos to your emedical record shortly after the appointment is confirmed because the doctor will read it thoroughly video call on schedule through our mobile app you can always switch to cellular once the internet is down": "Upload images and videos to your e-medical record shortly after the appointment is confirmed, because the doctor will read it thoroughly Video Call on schedule through our mobile app (you can always switch to Cellular once the internet is down).", "upload successfully": "Upload successfully", "urethral": "urethral", "usable": "Usable", "usable call times": "Usable call times", "use another account": "Use another account", "use another phone": "Use another phone", "use later": "Use later", "use within": "Use within", "username is not valid": "Username is not valid", "vaccine": "Vaccine", "vaginal": "vaginal", "vat invoice": "VAT invoice", "video": "Video", "video call": "Video call", "video call fee": "Video call fee", "view": "View", "view and edit consultation": "View and edit consultation", "view detail": "View detail", "view image": "View image", "view medical record": "View medical record", "visa/mastercard/jcb card": "Through Visa/Mastercard/JCB card", "vision: the most TRUSTED telemedicine platform": "Vision: the most TRUSTED telemedicine platform", "vnpost or viettel stores": "Money transfer at VNPost or Viettel Stores", "voice call by internet": "Voice call by Internet", "voice call fee": "Voice call fee", "wait a second": "Wait a second", "waiver of the service fee for member": "Waiver of the service fee for member", "wards": "Wards", "we help you maintain good health and when you have problems, we connect you to the best specialists via phone or video 24/7": "We help you maintain good health and when you have problems, we connect you to the best specialists via phone or video 24/7", "we may cancel the appointment unless you provide all the necessary medical information as requested": "We may refuse the appointment unless you provide all the necessary medical information as requested.", "we understand that this registered number belongs to you - an adult (above 16 years old). If the patient is someone else, please provide it in the later section.": "We understand that this registered number belongs to you - an adult (above 16 years old). If the patient is someone else, please provide it in the later section.", "we will contact you as soon as possible": "We will contact you as soon as possible", "week": "Week", "weekend": "Weekend fee", "weeks": "Weeks", "weeks old": "weeks old", "weight": "Weight", "wellcare mobile app": "Wellcare mobile app", "wellcare offers free consultation with another specialist, even with higher fee": "Wellcare offers free consultation with another specialist, even with higher fee", "wellcare prepaid card": "Wellcare prepaid card", "wellcare telemedicine": "Wellcare telemedicine", "wellcare's choice": "Wellcare's choice", "wellness wallet": "Wellness wallet", "what patients say?": "What patients say?", "who is the patient?": "Who is the patient?", "whom is this consultation for?": "Whom is this consultation for?", "with me": "With me", "within": "Within", "work location": "Work location", "work schedule": "Work schedule", "workplace": "Workplace", "xray": "XRay", "years": "Years", "years old": "Y.O. ", "yes": "yes", "you": "You", "you agree with": "You agree with", "you are choosing to send our physians an inquiry before proceeding with a short question please note that question includes only text no images or video clips no diagnosis or prescription will be given doctors wellcare reserve the right to decline if the inquiry is having less information than what is needed the patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon": "<p>You are choosing to send our physians an inquiry. Before proceeding with a knowledge question, please note that:<br>- Question includes only text, <strong>no</strong> images or video clips.<br>- <strong>No</strong> diagnosis or prescription will be given<br>- Doctors & Wellcare reserve the right to decline, if:<br>  1. The inquiry is having less information than what is needed.<br>  2. The patient should make a voice or video call with our doctor for a proper diagnosis or come to the nearest medical center soon.<br>.<br><em>Clarification: Unlike teleconsultation - providing you a two-way communication with a physician, knowledge questions seek to solve only </em><strong><em>simple inquiries</em></strong><em> and provide no diagnosis or prescription revision. If a specific diagnosis is what you are looking for, then make a teleconsultation to receive a medical form to fill with more information, relevant images & videos, and to have our physicians interpret all of your symptoms to you.</em></p>", "you are missing some required information. Please follow the steps again.": "You are missing some required information. Please follow the steps again.", "you are offline": "You are offline", "you can move the appointment time sooner or later. Don't forget patient calls only within the appointment slot, but you may call any time.": "You can reschedule to the earlier or later time, and the patient will accordingly call within the new allotted timeslot. But remember: unlike the patient, you can call anytime, at your convenience.", "you have entered the promo code": "You have entered the promo code", "you have read the consultation fee policy, agreed to deduct the percentage of cancellation fee and refund the remaining amount to the examination book for the next consultation": "You have read the consultation fee policy, agreed to deduct the percentage of cancellation fee and refund the remaining amount to the examination book for the next consultation.", "you might get the answer before": "You might get the answer before", "you need additional topup": "You need to top up at least {amount} for this consultation", "you need to top up": "You need to top up", "you will be able to upload images and videos to your e-medical record from our mobile app or the link we sent through sms after you are done with the booking": "You will be able to upload images and videos to your e-medical record from our mobile app or the link we sent through sms <span style=\"color: #ff5722;\">AFTER</span> you are done with the booking", "you will be in regular contact with your doctor through voice and video calls.": "You will be in regular contact with your doctor through voice and video calls.", "you will receive the response within 24 hours": "You will receive the response within 24 hours. Please check your SMS or Wellcare mobile app", "your account has been created successfully": "Your account has been created successfully", "your appointment time is": "Your appointment time is", "your avatar": "Your avatar", "your balance": "Your balance", "your data is secured with HIPPA compliance": "Your data is secured with HIPPA compliance", "your feedback about the system and its people": "Your feedback about the system and its people", "your full name": "Your full name", "your name": "Your name", "your phone number is the login id and is use to connect with wellcare": "Your phone number is the login id and is use to connect with Wellcare", "your question?": "Your question?", "your rating reflects the doctors credibility and his her raking on our system": "Your rating reflects the doctor's credibility and his/her raking on our system", "your relatives": "Your relatives", "your thought about the doctor your thought about wellcare": "- Your thought about the doctor\\n- Your thought about the system", "zalopay": "Payment ZaloPay", "zalopay e-wallet": "ZaloPay", "i know": "i know", "question": "Question", "consultation for the others": "For other", "please select day": "Please select day", "lab tests": "Lab tests", "first meeting": "First meeting", "video call payment instruction": "<ul style='list-style-type:disc'><li>Video appointments can make both video or voice call. Voice appointments can only make voice call</li><li>Please download Wellcare's mobile app to make a video or internet voice call</li><li>If you are unable to download the mobile app or your internet connection is unstable, please dial our Vinaphone <a href='tel:+842836226822' target='_self'>02836226822</a>, Viettel <a href='tel:+84345220099' target='_self'>0345220099</a>, or Mobifone <a href='tel:+84765220099' target='_self'>0765220099</a> from your registered phone number on time.</li><li>For further support, please message our care team via Zalo at <a href='tel:+***********' target='_self'>0366.905.905.</a></li></ul>", "download app": "DOWNLOAD APP", "to make a voice or video call to the physician": "to make a voice or video call to the physician", "to receive the physician's response": "to receive the physician's response", "choose your platform": "Choose your platform", "order successful": "Successful", "install": "Install", "morning": "Morning", "noon": "<PERSON>on", "afternoon": "Afternoon", "late afternoon": "Late afternoon", "evening": "Evening", "tomorrow": "Tomorrow", "time slot": "Time slot", "request time slot": "Request time slot", "please choose": "Please choose", "personal health record": "Personal Health Record", "your first meeting purpose": "Your first meeting purpose", "for check-up": "For check-up", "for diagnosis": "For diagnosis", "current": "Now", "before": "History", "medical history detailed description": "This is about your Chronic health conditions, Medications and nutritional supplements, Childhood illnesses, Current infections, Surgical procedures, Family illnesses, or anything else you want to note to the physian", "can't find your order please try again": "Can't find your order. Please reload to try again", "requested time": "Requested time", "today schedule of doctor is not fixed": "<p>Today's schedule of Dr. %{doctor} is not fixed. Please text us through viber whatsapp zalo <a href='tel:+***********'>+***********</a> so that our care team can arrange a suitable time for your appointment.</p>", "or choose common reason": "or choose from common reason", "psychotherapy": "Psychotherapy", "what are your expectations from your therapist": "What are your expectations from your therapist ?", "consultation-inperson": "Inperson consultation", "maximum is 3 option": "Maximum is 3 options", "require avatar": "Require avatar", "please enter complete information below": "Please enter complete information below", "relationship with user": "Relationship with user", "maximum is {maximum} questions": "Maximum is %{maximum} questions", "i do consent": "I do consent", "each 15-minute block": "Each 15-minute block is good for 3-5 questions", "each 15-minute block is good for 3-5 questions": "Each 15-minute block is good for 3-5 questions. Reserve longer or cut down the list of questions.", "can't copy to clipboard": "Can't copy to clipboard", "fill in information": "Fill in information", "reciever": "Reciever", "saved address": "Saved address", "add new address": "Add new address", "edit address": "edit address", "province/city": "Province / City", "the prescription has been canceled": "The prescription has been canceled.", "reorder the prescription": "Reorder the prescription", "confirm prescription order": "Confirm prescription order?", "please choose to continue": "Please choose to continue.", "processing the prescription": "Processing the prescription ...", "electronically sign prescriptions": "Electronically sign prescriptions", "processing, please wait": "Processing, please wait ...", "transfer the prescription to the pharmacy": "Transfer the prescription to the pharmacy", "the pharmacy confirms": "The pharmacy confirms", "your prescription has been transferred to the pharmacy": "Your prescription has been transferred to the pharmacy. You will receive a confirmation call within 30 minutes.", "delivery prescription in progress": "Delivery in progress", "choose the language to communicate with the doctor": "Choose the language to communicate with the doctor", "english": "English*", "vietnamese": "Tiếng <PERSON>", "surcharge": "surcharge", "please choose language before continuing.": "Please choose language before continuing.", "surcharge {surcharge}": "surcharge {surcharge}", "surcharge {surchargeLang} per slot for consulting in English": "*surcharge {surchargeLang} per slot for consulting in English", "you have chosen consulting in English, surcharge {surchargeLang} per slot.": "*you have chosen consulting in English, surcharge {surchargeLang} per slot.", "please choose patient": "Please choose before continuing.", "you have chosen consulting in {lang}, surcharge {surchargeLang} per slot.": "*you have chosen consulting in {lang}, surcharge {surchargeLang} per slot.", "you are already a member": "You are already a member", "knowledge question": "Knowledge question", "your question": "Your question", "choose your option": "Choose your option", "response from Wellcare, with reference": "Response from Wellcare, with reference", "addon": "<PERSON><PERSON>", "physicians comment": "Physician's comment", "your physician": "Your physician", "the choice is based on your medical history of": "The choice is based on your medical history of ", "more detail": "More details", "explore other matches": "Explore other matches", "choose your physician": "Choose your physician", "index updated successfully": "Index updated successfully", "index updated failed": "Index update failed", "body-index": "Body index", "body index": "Vital Signs", "vaccinations": "Vaccination", "required height and weight": "Required height and weight", "required": "Required", "choose language": "Choose language", "needle": "{vaccines} needle", "Bs": "Dr", "change patient": "Change patient", "do you want to change the patient?": "Do you want to change the patient?", "false information may lead to misdiagnosis and inappropriate treatment decisions": "False information may lead to misdiagnosis and inappropriate treatment decisions.", "Vaccines prevent millions of deaths worldwide every year and are the best way to not only protect ourselves and our children against certain preventable diseases themselves but also against the dangerous complications or consequences that they can bring.": "Vaccines prevent millions of deaths worldwide every year and are the best way to not only protect ourselves and our children against certain preventable diseases themselves but also against the dangerous complications or consequences that they can bring.", "Refuse to provide. Affirm a fully in-time vaccination.": "Refuse to provide. Affirm a fully in-time vaccination.", "None or Unknown": "None or Unknown", "Vaccine & Date": "Vaccine & Date", "please update the index": "Your index has not been updated for the past year. Please update it with the latest index", "ask about the article": "Ask about the article", "title": "Title", "link": "Link", "your order will be automatically confirmed upon bank transfer": "Your order will be automatically confirmed upon bank transfer.", "pending payment in": "Pending payment in ", "incl. Membership (all year waiver of the service fee and other benefits )": "<span class='font-italic'>incl. Membership (<span class='primary--text font-weight-bold'>all-year</span> waiver of the service fee and other benefits)</span>", "10 question combo": "10 question combo", "Membership + 10 question combo": "Membership + 10 question combo", "Membership (all-year waiver of the service fee and other benefits)": "<span>All-year <span class='primary--text font-weight-bold'>waiver</span> of the service fee and other benefits. Better rate, early appointment. Free access to all EduHub & HealthGPT (health tools and chat). </span>", "choose date of birth": "Choose date of birth", "expected due date": "Expected due date", "pregnancy week": "Pregnancy week", "basic": "Basic", "membership:description": "Pregnancy week-by-week, Health GPT, EduHub etc..", "combo-10-questions:description": "10 Knowledge questions or follow-up messages with your personal doctor", "all-year-combo-10-questions-750k:description": "Basic package + 10 Knowledge questions or follow-up messages with your personal doctor", "All year + combo 10 questions (750k)": "Basic package + 10 Knowledge questions", "pregnancy package": "Pregnancy Package", "pregnancy-diary": "Pregnancy Package", "my due date is": "My due date is", "choose a package": "Choose a package", "year": "Year", "baby development": "Baby development", "baby development desc": "Receive professional medical care and companionship from anywhere in the world. Cùng Con Khôn Lớn combines the heart of medicine with the convenience of modern technology. Say goodbye to crowded waiting rooms and rushed appointments. The Cùng Con Khôn Lớn program guides parents through every stage of their child’s development. Parents can also choose a pediatrician for ongoing companionship and remote health consultations. With knowledge about developmental milestones, warning signs to watch for, essential tasks, and optimal care guidelines, your child will receive comprehensive nurturing to grow up healthy and happy.", "comprehensive remote healthcare solution for children": "Comprehensive remote healthcare solution for children", "warning: expMembership": "Your membership is expired on {date}. Please renew your subscription to maintain uninterrupted access to the pregnancy package", "Combo 10 questions": "Asynchronous telehealth", "Free access to Pregnancy by week and HealthGPT for member": "Free access to Pregnancy by week and HealthGPT for member", "dueDateValid: 1": "Bạn đã nhập: {date} là ngày kết thúc của thai kỳ 280 ngày. Bạn sẽ chuyển dạ trong tuần này, nế<PERSON>h<PERSON>, bạn sẽ được dục sinh khi được 42 tuần. Trong lúc chờ đợi, hãy báo với bác sĩ của bạn ngay lập tức nếu bé cử động chậm hoặc có bất kỳ chất lỏng nào rò rỉ từ âm đạo. Nếu bạn đã sinh em bé, hãy nhấp vào nút 'ĐÃ SANH' để chuyển sang Nhật ký BÉ CHÀO ĐỜI 0 - 3 TUỔI. Hoặc nhập lại ngày dự sinh để tiếp tục Nhật ký THAI KỲ dành cho bé CHƯA SINH.", "dueDateValid: 2": "Bạn có nhầm lẫn? Ngày dự sinh vượt quá thai kỳ 280 ngày tính từ hôm nay. Ngày dự sinh là ngày DỰ ĐOÁN bạn sẽ sinh - mà bác sĩ đã nói trong các lần khám thai. Bạn cũng có thể tự tính ngày dự sinh dựa trên ngày đầu tiên của kỳ kinh trước +280 ngày (40 tuần).", "pregnancy program": "Pregnancy Program", "Pregnancy week-by-week and Asynchronous telehealth": "Pregnancy week-by-week and Asynchronous telehealth", "Estimated due date": "Estimated due date", "Input your EDD": "Input your EDD", "congratulations! you are {weekDueDate} weeks pregnant.": "Congratulations! You are {weekDueDate} weeks pregnant.", "Pregnancy Due Date is the estimated delivery date for a pregnant woman. Normal pregnancies last between 38 and 42 weeks. Children are delivered on their expected due date about 4% of the time.": "Pregnancy Due Date is the estimated delivery date for a pregnant woman. Normal pregnancies last between 38 and 42 weeks. Children are delivered on their expected due date about 4% of the time.", "your subscription is activated, enjoy your exclusive benefits": "Your subscription is activated, enjoy your exclusive benefits!", "Congratulations": "Congratulations", "12 months of validation until {getExpirationDate}": "12 months of validation until {getExpirationDate}", "required field 1": "Pregnancy Due Date is the estimated delivery date for a pregnant woman. Normal pregnancies last between 38 and 42 weeks. Children are delivered on their expected due date about 4% of the time.", "enter weight": "Enter weight", "enter height": "Enter height", "enter head": "Enter head circumference", "choose baby": "Choose a baby", "choose packages": "Choose packages", "choose personal doctor": "Choose personal doctor", "baby information": "Baby infomation", "baby-info": "{age} years old", "require-avatar": "Require avatar", "valid:headCircumference-1": "Head Circumference invalid", "valid:height-1": "Height invalid", "valid:weight-1": "Weight invalid", "Input your due date": "Input your due date", "Due date calculator": "Due date calculator", "my self": "my self", "teenager": "teenager", "couples": "couples", "choose target": "mental health counseling", "choose reason": "Reason", "choose expectations": "Expectations", "please select at least one option before proceeding.": "Please select at least one option before proceeding.", "please choose a chief complaint before proceeding.": "Please choose a chief complaint before proceeding.", "present": "present", "newborn": "newborn", "Growing Up Together": "Growing Up Together", "Exclusive for Members": "Exclusive for Members", "Track your baby_s growth and milestones, and receive weekly advice": "Track your baby's growth and milestones, and receive weekly advice.", "personal-doctor-12-months": "Personal Doctor - 12-Month Plan", "exclusive-member": "Exclusive to Members", "money-per-minute-10-consultations": "Only {money}k/min, billed by the minute", "personal-doctor-benefits": "<h3>Benefits when having a Personal Doctor</h3><ul><li><strong>Personalized and Efficient Care:</strong> Your doctor knows your medical history and understands your unique health needs.</li><li><strong>No Appointment Needed:</strong> The doctor will proactively call back upon receiving your request.</li><li><strong>Time-Saving:</strong> No need to repeatedly fill out your medical history for each consultation.</li><li><strong>No Extra Charge for Video Calls:</strong> Helping you optimize costs.</li><li><strong>Free for Short Conversations:</strong> No fees for voice calls or voice messages under 30 seconds, or text messages under 50 characters—designed to minimize costs for patients.</li><li><strong>Unlimited Call Duration:</strong> Make calls of any length, as long as they stay within your annual minutes quota.</li><li><strong>Easy Communication via Text or Voice Messages:</strong> A more convenient and cost-effective option.</li><li><strong>Minute-Based Billing:</strong> An efficient and cost-optimized model compared to fixed-duration packages (15, 20, 30 minutes).</li><li><strong>Exclusive Lower Rates:</strong> Lower fees per minute and per text message response compared to one-off consultations or single inquiries (19,000đ/minute or 22,333đ/minute for specialist doctors).</li></ul>", "ask-for": "Ask for", "expert-verify": "Expert verify", "bot-response": "HealthGPT's Response"}