# MIDDLEWARE
Document: https://nuxtjs.org/docs/directory-structure/middleware/

Custom functions that can be run before rendering either a page or a group of pages (layout).
The filename will be the name of the middleware (middleware/auth.js will be the auth middleware). You can also define page-specific middleware by using a function directly, see anonymous middleware .

A middleware receives the context  as the first argument.


**Keep the directory and readme.md file even if you don't use this**
