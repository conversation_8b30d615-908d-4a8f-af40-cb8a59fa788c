import { computed } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import dayjs from 'dayjs'

interface IDurg {
  index: number
  name: string
  description: string
  quantity: number | string
}
interface IPdfData {
  _id: string
  patient: {
    name: string
    gender: string
    age: number
    dob: string
    address: string
  }
  doctor: {
    sign: string
  }
  diagnosis: Array<string>
  drugs: Array<IDurg>
  date: {
    date: string
    time: string
    full: string
  }
}

function caculateAge(dob: string) {
  return dayjs().diff(dayjs(dob), 'years')
}

function parseDate(date: string) {
  return dayjs(date).format('DD/MM/YYYY')
}

function getDecsription(prescription: any) {
  let route = ''
  if (
    prescription?.route?.order !== undefined &&
    prescription?.drug?.form !== undefined
  ) {
    route = `, mỗi lần ${prescription.route.order} ${prescription.drug.unit}`
  }
  let duration = ''
  if (prescription?.duration !== undefined) {
    duration = `, trong vòng ${prescription.duration} ngày`
  }
  let note = ''
  if (prescription?.note !== '') {
    note = ` ${prescription.note}`
  }
  return (
    (prescription?.route?.name?.vi || '') +
    ' ' +
    (prescription?.frequency?.name?.vi || '') +
    route +
    duration +
    note
  )
}

function getAddress(data: any): string {
  if (!data) return ''
  // console.log("line 61 ", data);
  const address = [
    data?.name || '',
    data?.phone || '',
    data.address?.line1 || '',
    data.address?.province || ''
  ]
  return address.filter((item) => item !== '').join(' - ')
}

function getQuantity(prescription: any): number | string {
  if (!prescription.followDirection) return ''
  const quantityPerUse: number = prescription?.take || 1
  const quantityPerDay: number = prescription?.frequency?.value || 1
  const totalDays: number = prescription?.duration || 1
  return quantityPerDay * totalDays * quantityPerUse
}

function getOptions(data: IPdfData) {
  const lineDiagnosis: number = data.diagnosis.length
  const lineDrugs: number = data.drugs.length
  const defaultWidth: number = 300
  const defaultHeight: number = 500
  return {
    width: defaultWidth + 'mm',
    height: defaultHeight + (lineDiagnosis * 10 + lineDrugs * 15) + 'mm'
  }
}

function adapt(source, item): IPdfData {
  return {
    _id: source?.consultation?._id,
    patient: {
      name: source?.consultation?.patient?.name,
      gender: source?.consultation?.patient?.gender === 'M' ? 'nam' : 'nữ',
      age: caculateAge(source?.consultation?.patient?.dob),
      dob: parseDate(source?.consultation?.patient?.dob),
      address: getAddress(item?.orderItem.address)
    },
    doctor: {
      sign: item?.signData || ''
    },
    diagnosis:
      source?.consultation?.diagnosis?.map((item) => item.vi.name) || [],
    drugs: source?.prescriptions?.map((item, index) => {
      return {
        index,
        name: item.drug?.name,
        description: getDecsription(item),
        quantity: getQuantity(item)
      }
    }),
    date: {
      date: dayjs().format('DD/MM/YYYY'),
      time: dayjs().format('HH:mm'),
      full: dayjs().format('DD/MM/YYYY HH:mm')
    }
  }
}

export default function precriptionAdapter(
  source: Ref<any> | ComputedRef<any>,
  item: Ref<any> | ComputedRef<any>
) {
  // console.log("adapter line 20: ", item.value);
  // console.log("adapter line 21: ", source.value?.prescriptions?.length);
  const data = computed<IPdfData>(() => adapt(source.value, item.value))
  const option = computed(() => getOptions(data.value))
  // console.log("adapter line 40: ", data.value);
  return { data: data.value, option: option.value }
}
