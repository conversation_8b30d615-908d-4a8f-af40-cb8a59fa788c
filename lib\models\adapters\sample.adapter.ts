import { ref, watch } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

interface ISource {}
interface ITarget {}

export function sampleAdapter(source: Ref<ISource> | ComputedRef<ISource>) {
  function adapt(source: ISource): ITarget {
    const target = {
      ...source
    }
    return target
  }

  const target = ref<ITarget>(adapt(source))

  watch(source, (newValue) => {
    target.value = adapt(newValue)
  })
  return { target, adapt }
}
