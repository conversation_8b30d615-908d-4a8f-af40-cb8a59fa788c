interface IFileUpload {
  labels: string[]
  description?: string
  file: any
  folder: string
  quantity?: string
  type: string
}

interface IFile {
  _id: string
  name: string
  description?: string
  labels: string[]
  state: string
  folder: string
  quantity?: string
  tags?: string[]
  user: string
  mimetype?: string
  type: string
  size?: number
  vendor?: string
  project?: string
  progress?: number
  url: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
}
export { IFile, IFileUpload }
