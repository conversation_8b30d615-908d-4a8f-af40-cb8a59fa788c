export interface IOrderAPI {
  createOrder(data: any): Promise<any>
  getOrder(orderId: string): Promise<any>
  getOrderItem(orderId: string, itemId: string): Promise<any>
  addOrderItem(orderId: string, data: any): Promise<any>
  updateOrder(orderId: string, data: any): Promise<any>
  updateOrderItem(orderId: string, itemId: string, data: any): Promise<any>
  getOrderState(orderId: string): Promise<any>
  updateOrderItemV2(orderItemId: string, data: any): Promise<any>
  createOrderV2(order: object, orderItems: any): Promise<any>
}
