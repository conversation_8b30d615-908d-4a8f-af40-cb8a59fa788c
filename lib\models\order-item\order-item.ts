const ConstOrderItemState = <const>[
  'pending',
  'placed',
  'free',
  'completed',
  'returned',
  'cancelled'
]
type OrderItemStateType = (typeof ConstOrderItemState)[number]

export default interface OrderItem {
  order: string
  type: string // ref product type
  state: OrderItemStateType
  product: string // ref product
  provider?: any // ref provider
  deliverTo: string // ref user
  quantity: number
  price?: number
  total?: number
  customPrice?: boolean
  meta: any
  description?: string
  note?: string
  autoCreateOrder?: boolean
  voucher?: string // ref voucher
  source: string
  case?: any
}
