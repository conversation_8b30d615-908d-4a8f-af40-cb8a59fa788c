interface IMetaItemOrder {
  user: string
  provider: string
  patient: string
  patientInfo: any
  product: string
  type: string
  [key: string]: any
}
interface IOrderItem {
  order: string
  type: string
  state: string
  product: string
  meta: IMetaItemOrder
  source: string
  deliverTo: string
  quantity: number
  case?: any
}

interface IOrderItemMedicineDelivery {
  _id: string
  product: {
    _id: string
    name: string
    slug: string
    sku: string
    type: {
      _id: string
      name: string
    }
    price: number
  }
  meta: IMetaItemOrder
  total: number
  price: number
  quantity: number
  state: string
}

interface IoptionsCreateAddress {
  user: string
  phone?: string
  name?: string
  province?: string
  line1?: string
}

interface IAddressee {
  createdAt: string
  isDefault: string
  name: string
  line1: string
  phone: string
  province: string
  retired: string
  updatedAt: string
  user: string
  _id: string
}

export {
  IOrderItem,
  IMetaItemOrder,
  IOrderItemMedicineDelivery,
  IoptionsCreateAddress,
  IAddressee
}
