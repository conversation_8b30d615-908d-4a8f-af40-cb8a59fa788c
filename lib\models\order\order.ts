const ConstOrderState = <const>[
  'incart',
  'placed',
  'abandoned',
  'cancelled',
  'fullfilled',
  'partialfilled',
  'returned'
]
type OrderStateType = (typeof ConstOrderState)[number]

const ConstOrderPaymentState = <const>[
  'cash-on-delivery',
  'bank-transfer',
  'payment-gateway',
  'e-wallet',
  'creditcard'
]
type OrderPaymentStateType = typeof ConstOrderPaymentState

const ConstOrderPaymentType = <const>[
  'cash-on-delivery',
  'bank-transfer',
  'payment-gateway',
  'e-wallet',
  'creditcard'
]
type OrderPaymentTypeType = typeof ConstOrderPaymentType

export default interface Order {
  _id?: string
  state: OrderStateType
  payment?: {
    state: OrderPaymentStateType
    type: OrderPaymentTypeType
  }
  organization: string // ref Organization
  source: string
  deliverTo: string // ref User
  vendor?: string // ref Organization
  autoCreateOrder: boolean
  type?: any
  code?: number
  description?: string
  note?: string
  no?: string
  file?: string
  date?: Date
  meta?: any
  isThirdParty?: boolean
}
