interface IOrder {
  _id: string
  updatedAt?: string
  createdAt?: string
  sortOrder?: number
  code?: number
  organization?: any
  deliverTo?: string
  description?: string
  vendor?: any
  meta?: {
    flowProduct: string
    productType: string
  }
  cancelable?: boolean
  isReceived?: boolean
  isConfirmed?: boolean
  requestVat?: boolean
  delivery?: {
    fee: number
    to: {
      name: string
      phone: number
      address: {
        street1: number
      }
    }
  }
  isDelivery?: boolean
  // temp_total?: number
  total?: number
  discount?: number
  customDiscount?: number
  subTotal?: number
  voucherAmount?: number
  stateVoucher?: string
  payment?: {
    source: string
    state: string
    type: string
  }
  isThirdParty?: boolean
  date?: string
  state?: string
  promotions?: any[]
}

export { IOrder }
