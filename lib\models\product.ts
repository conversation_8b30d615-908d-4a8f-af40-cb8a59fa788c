import { IProvider } from './provider'

export interface IProductType {
  _id: string
  name: string
  type: string
}

export interface IProductAttribute {
  key: string
  value: any
}
export interface IProduct {
  _id: string
  name: string
  slug: string
  price: number
  type: IProductType
  provider: IProvider
  state: string
  checkoutFlow: {
    name: string
  }
  includes: Array<any>
  inventoryState: string
  inventories: []
  inventoryStock: number
  title: string
  subtitle: string
  image: any
  description: string
  template?: string
}
