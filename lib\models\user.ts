export interface IAvatar {
  url: string
}
export interface ITimezone {
  local: boolean
}
export interface IAddress {
  country?: null
  postcode?: null
  state?: null
  street1: string
  suburb?: null
}
export interface IReferral {
  refCode: string
}
export interface IValidated {
  email?: boolean
  phone?: boolean
}
export interface IBank {
  account: string
  city: string
  main: string
  branch: string
}
export interface IIdCard {
  date?: null
  number: string
  place: string
}
export interface IInsurance {
  contract: string
}
export interface IUtm {
  campaign: string
  content: string
  medium: string
  source: string
}
export interface IUser {
  id: string
  _id: string
  avatar?: IAvatar
  fullname: string
  gender?: string
  locale?: string
  timezone?: ITimezone
  role?: null[] | null
  countryCode?: string
  name?: string
  search?: string
  updatedAt?: string
  wallet: number
  retired: boolean
  address?: IAddress
  bankAccount?: string
  createAccount?: boolean
  dob: string
  email?: string
  isProvider?: boolean
  oneSignalId?: string
  phone: string
  provider?: string
  referral?: IReferral
  username: string
  validated: IValidated
  veryPatient?: null[] | null
  bank?: IBank
  idCard?: IIdCard
  updatedBy?: string
  fingerPrint?: string
  isAdmin?: boolean
  isTest?: boolean
  insurance?: IInsurance
  utm?: IUtm
  token?: string
  userId?: string
  relationship?: string
}
