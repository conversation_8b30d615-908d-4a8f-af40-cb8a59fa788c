import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { createCaseUrl, createWorkflowJobUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { IFetchOption } from '../fetch-option.interface'
import { IBpm } from './bpm.interface'

export function createBpm(
  data: Ref<IBpm> | ComputedRef<IBpm>,
  fetchOption: IFetchOption
) {
  const { post } = useWellcareApi()
  const { $config } = useContext()
  const { execute: execute1, timeout } = useRepository<IResponse<IBpm>>({
    fetcher: (param) => {
      if (!$config.checkout?.bpm?.disabled) {
        return post({
          url: createCaseUrl(),
          data: param
        })
      } else {
        return new Promise(() => {})
      }
    },
    conditions: data,
    useFetch: false,
    manual: true,
    timeout: fetchOption.timeout
  })

  const { execute: execute2, timeout: timeout2 } = useRepository<
    IResponse<IBpm>
  >({
    fetcher: (param) => {
      if (!$config.checkout?.bpm?.disabled) {
        return post({
          url: createWorkflowJobUrl('support-checkout', param.uid),
          data: param
        })
      } else {
        return new Promise(() => {})
      }
    },
    conditions: data,
    useFetch: false,
    manual: true,
    timeout: fetchOption.timeout
  })

  const execute = () => {
    execute1()
    execute2()
  }

  return { execute, timeout, timeout2 }
}
