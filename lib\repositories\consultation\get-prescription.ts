import { ref, useContext, watch } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useWellcareApi, useRepository } from '@wellcare/nuxt-module-data-layer'
import {
  getConsultationByIdUrl,
  searchPrecriptionUrl
} from '../wellcare-api-urls'

export function getPrescription(id: Ref<string> | ComputedRef<string>) {
  const { $toast } = useContext()
  const { get } = useWellcareApi()
  const data = ref<{ prescriptions: Array<any>; consultation: any }>({
    prescriptions: [],
    consultation: {}
  })
  const loading = ref(false)
  const loaded = ref(false)
  const success = ref(0)
  const {
    response: consultation,
    onSucess: onConsultationSuccess,
    loading: consultationLoading,
    execute: executeGetConsultation,
    onError: onConsultationError
  } = useRepository<any>({
    fetcher: (id) =>
      get({
        url: getConsultationByIdUrl(id),
        params: {
          populate: JSON.stringify([
            { path: 'user' },
            { path: 'patient' },
            { path: 'diagnosis' },
            { path: 'provider' }
          ])
        }
      }),
    conditions: id,
    useFetch: false,
    manual: true
  })

  const {
    response: prescriptions,
    execute: executeSearchPrescription,
    onSucess: onPrescriptionSuccess,
    loading: loadingPrescriptions,
    onError: onPrescriptionError
  } = useRepository<any>({
    fetcher: (id) =>
      get({
        url: searchPrecriptionUrl(),
        params: {
          filter: {
            consultation: id
          },
          populate: JSON.stringify([
            { path: 'drug' },
            { path: 'frequency' },
            { path: 'route' }
          ])
        }
      }),
    conditions: id,
    useFetch: false,
    manual: true
  })
  onPrescriptionSuccess(() => {
    data.value = { ...data.value, prescriptions: prescriptions.value.results }
    success.value++
  })
  onPrescriptionError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  watch([loadingPrescriptions, consultationLoading], () => {
    if (loadingPrescriptions.value && consultationLoading.value) {
      loading.value = true
    } else loading.value = false

    if (!loadingPrescriptions.value && !consultationLoading.value) {
      loaded.value = true
    }
  })
  onConsultationSuccess(() => {
    data.value = { ...data.value, consultation: consultation.value.results }
    success.value++
  })
  onConsultationError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  const execute = () => {
    executeGetConsultation()
    executeSearchPrescription()
  }
  return { data, fetch, execute, loading, loaded }
}
