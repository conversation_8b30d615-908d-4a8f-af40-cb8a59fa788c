import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { ISearchOption } from '../search-option.interface'
import { searchConsultationUrl } from '../wellcare-api-urls'

export function searchConsultation(
  params: ComputedRef<ISearchOption> | Ref<ISearchOption>
) {
  const { get } = useWellcareApi()
  return useRepository<any>({
    fetcher: () =>
      get({
        url: searchConsultationUrl(),
        params: params.value,
        timeout: 10000
      }),
    manual: true,
    useFetch: false,
    toastOnError: true
  })
}
