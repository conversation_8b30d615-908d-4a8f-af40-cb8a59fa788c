import { ref, watch, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { createPdfUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { ICreatePdf, IFile } from './file.interface'

export function createPdf(option: Ref<ICreatePdf> | ComputedRef<ICreatePdf>) {
  const { post } = useWellcareApi()
  const { $toast } = useContext()
  const loaded = ref(false)
  const results: Ref<IFile> = ref({ url: '' })
  const repo = useRepository<IResponse<IFile>>({
    fetcher: ({ userId, template, data }) => {
      return post({ url: createPdfUrl(userId, template), data })
    },
    conditions: option,
    useFetch: false,
    manual: true
  })
  watch(repo.response, () => (results.value = repo.response.value.results))
  repo.onError((e) => {
    loaded.value = true
    $toast.global?.appError({
      message: e.message
    })
  })
  repo.onSucess(() => (loaded.value = true))
  return { ...repo, results, loaded }
}
