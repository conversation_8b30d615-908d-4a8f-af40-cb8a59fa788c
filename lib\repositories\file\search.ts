import { ref, useContext, watch } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { searchFilesUrl } from '../wellcare-api-urls'
import { ISearchOption } from '../search-option.interface'
import { IFile } from './file.interface'

export const searchFiles = (
  options: Ref<ISearchOption> | ComputedRef<ISearchOption>
) => {
  const { get } = useWellcareApi()
  const { $toast } = useContext()
  const files = ref([])
  const total = ref(0)
  const conditions: Ref<object> = ref(options)
  watch(options, () => {
    conditions.value = options
  })
  const { fetch, error, execute, loading, response, onSucess, onError, timer } =
    useRepository<{
      results: IFile[]
      total: number
    }>({
      fetcher: (conds) =>
        get({
          url: searchFilesUrl(),
          params: {
            skip: 0,
            limit: conds?.limit || 10,
            fields: 'url, ext, name, thumbnail, mimetype',
            ...conds,
            count: true
          }
        }),
      conditions,
      useFetch: false,
      manual: false
    })
  onSucess(() => {
    files.value = response.value.results
    total.value = response.value.total
  })
  onError((e) => {
    $toast.error(e.message)
  })
  return {
    fetch,
    error,
    loading,
    execute,
    response,
    onSucess,
    onError,
    total,
    timer,
    files
  }
}
