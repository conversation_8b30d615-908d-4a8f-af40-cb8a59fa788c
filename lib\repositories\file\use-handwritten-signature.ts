import { ref } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

function encodeBase64(str: string): string {
  // return window.btoa(unescape(encodeURIComponent(str)))
  const encoder = new TextEncoder()
  const data = encoder.encode(str)
  const binaryString = String.fromCharCode.apply(null, data)
  return window.btoa(binaryString)
}

export function useHandwrittenSignature(
  doctorName: string,
  query: Ref<string> | ComputedRef<string> = ref('?test=true')
) {
  return (
    'https://cdn.wellcare.vn/file-proxy/node-canvas-service/signature/handwritten/svg/base64-text/' +
    encodeBase64(doctorName) +
    query.value
  )
}
