import { ref, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import dayjs from 'dayjs'

export function UseDob(date: Ref<string> | ComputedRef<string> | string) {
  const { i18n } = useContext()
  const response = ref('')
  const formatDob = () => {
    const dateStr = typeof date === 'string' ? date : date.value
    if (dateStr) {
      let returnText = ''
      if (dayjs().diff(dayjs(dateStr), 'year', true) > 3) {
        // format by years
        returnText +=
          dayjs().diff(dayjs(dateStr), 'year') + ' ' + i18n.t('years old')
      } else if (dayjs().diff(dayjs(dateStr), 'month', true) > 1) {
        // format by months
        returnText +=
          dayjs().diff(dayjs(dateStr), 'month') + ' ' + i18n.t('months old')
      } else {
        // format by days
        returnText +=
          dayjs().diff(dayjs(dateStr), 'day') + ' ' + i18n.t('days old')
      }
      // append dob to return text
      returnText += ` (${dayjs(dateStr).format('DD-MM-YYYY')})`
      response.value = returnText
    }
  }
  return {
    response,
    formatDob
  }
}
