import { computed } from '@nuxtjs/composition-api'
import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { observationUrl } from './wellcare-api-urls'

export interface IObservation {
  _id?: string
  user?: string
  key: string
  name: string
  value: string
  observedAt: string
  updatedAt?: string
  label?: string[]
  unit?: string
}

export default function useObservation() {
  const { post, get } = useWellcareApi()

  const {
    response: resObs,
    execute: executeSearch,
    onSucess: onSearchSuccess,
    loading: loadingSearch
  } = useRepository<IResponse<IObservation[]>>({
    fetcher: (query) => {
      if (query.filter && query.limit && query.sort) {
        return get({
          url: `/phr/observation/search-aggregation`,
          params: query
        })
      }
    },
    useFetch: false
  })

  const {
    execute: executeImportCreate,
    onSucess: onImportCreateSuccess,
    loading: loadingImportCreate
  } = useRepository<IResponse<IObservation[]>>({
    fetcher: (data: IObservation[]) => {
      if (data.length) {
        return post({
          url: observationUrl.importCreate(),
          data
        })
      }
    },
    useFetch: false
  })

  const loading = computed<boolean>(
    () => loadingImportCreate.value || loadingSearch.value
  )

  return {
    executeSearch,
    onSearchSuccess,
    executeImportCreate,
    onImportCreateSuccess,
    loading,
    resObs
  }
}
