import { computed, ref, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef } from '@nuxtjs/composition-api'

import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { updateOrderItemUrl } from '../wellcare-api-urls'
import { usePaymentOrder } from '../order/use-payment-order'
import { updateOrder } from '../order/update-order'

export default function useRemoveOrderItem(
  orderItemId: ComputedRef<string>,
  orderId: ComputedRef<string>,
  shouldRefeshOrder: boolean = true
) {
  const { $config } = useContext()
  const { put } = useWellcareApi()
  const waiting = ref<boolean>(false)
  const paymentConfig = computed(() => $config.checkout?.payment)
  // REPOSITORY
  const { execute: executeUpdateOrder } = updateOrder()
  const { executeGetOrder, onSearchPromotionSuccess, order } = usePaymentOrder(
    computed(() => ({ _id: orderId.value })),
    computed(() => ({ polling: ref(0) })),
    computed(() => 'pending')
  )
  const {
    execute,
    loading: loadingRemove,
    onSuccess,
    onError
  } = useRepository<IResponse<any>>({
    fetcher: (orderItemId) =>
      put({
        url: updateOrderItemUrl(orderItemId),
        data: {
          update: {
            state: 'cancelled'
          }
        }
      }),
    useFetch: false,
    conditions: orderItemId,
    toastOnError: true
  })
  const loading = computed<boolean>(() => loadingRemove.value || waiting.value)
  onSuccess(() => {
    if (shouldRefeshOrder) {
      waiting.value = true
      setTimeout(() => {
        executeGetOrder()
      }, 1500)
    }
  })
  onSearchPromotionSuccess((response: any) => {
    waiting.value = false
    if (order.value.state === 'placed') return
    const updateOrderOption = {
      _id: '',
      promotions: []
    }
    const updatePromotionInOrder = {
      _id: '',
      promotions: []
    }
    if (paymentConfig.value.voucher.autoApply) {
      updateOrderOption._id = order.value._id
      updateOrderOption.promotions = response.results.map(
        (item: any) => item._id
      )
      const currentPromotion = response.results[0]
      if (
        currentPromotion?.redemption?.count >=
        currentPromotion?.policy?.benefit?.repeat?.frequencyMax
      ) {
        return
      }
      if (
        currentPromotion?.redemption?.sum >=
        currentPromotion?.policy?.benefit?.maxAmount
      ) {
        return
      }
      executeUpdateOrder(updateOrderOption)
    }

    updatePromotionInOrder._id = order.value._id
    // updatePromotionInOrder.value.userId = store.state.authen?.user?._id
    updatePromotionInOrder.promotions = response.results.map(
      (item: any) => item._id
    )
    executeUpdateOrder(updatePromotionInOrder)
  })
  return {
    execute,
    loading,
    onSuccess,
    onError
  }
}
