import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { searchOrderItemUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { ISearchOption } from '../search-option.interface'

export function searchOrderItem(
  option: Ref<ISearchOption> | ComputedRef<ISearchOption>
) {
  const { get } = useWellcareApi()
  const { $toast } = useContext()
  const repo = useRepository<IResponse<any>>({
    fetcher: (params) => get({ url: searchOrderItemUrl(), params }),
    conditions: option,
    useFetch: false,
    manual: false
  })
  repo.onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return repo
}
