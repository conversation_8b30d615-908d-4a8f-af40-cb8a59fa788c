import { ref, watch, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { addExistedOrderItem } from '../wellcare-api-urls'

interface UpdateOrderItem {
  orderItemId: string
  data: any
}

const updateOrderItemV2 = function (
  option: Ref<UpdateOrderItem> | ComputedRef<UpdateOrderItem>
) {
  const { put } = useWellcareApi()
  const { $toast } = useContext()
  const loaded = ref(false)
  const results: Ref<any> = ref({ _id: '' })
  const repo = useRepository<IResponse<any>>({
    fetcher: ({ orderItemId, data }) => {
      return put({ url: addExistedOrderItem(orderItemId), data })
    },
    conditions: option,
    useFetch: false,
    manual: true
  })
  watch(repo.response, () => (results.value = repo.response.value.results))
  repo.onError((e) => {
    loaded.value = true
    $toast.global?.appError({
      message: e.message
    })
  })
  repo.onSucess(() => (loaded.value = true))
  return { ...repo, results, loaded }
}
export default updateOrderItemV2
