import { computed, useStore } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IOrderItem } from '@lib/models/order-item/props_order-item'
import { updateOrderItemUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { ISearchOption } from '../search-option.interface'

export function useOrderItem(
  option: Ref<ISearchOption> | ComputedRef<ISearchOption>
) {
  const { put } = useWellcareApi()
  const store = useStore()
  const orderItem = computed(() => store.getters.getField('orderItem'))
  const {
    onSucess: onUpdateSuccess,
    execute,
    onError
  } = useRepository<IResponse<IOrderItem>>({
    fetcher: (params) => {
      if (params._id)
        return put({ url: updateOrderItemUrl(params._id), params })
    },
    conditions: option,
    useFetch: false,
    manual: false,
    toastOnError: true
  })
  onUpdateSuccess((response) => {
    store.commit('checkout/updateField', {
      path: 'orderItem',
      value: response.results
    })
  })
  onError(() => {
    store.commit('checkout/updateField', {
      path: 'orderItem',
      value: { ok: true }
    })
  })
  return { onUpdateSuccess, execute, orderItem }
}
