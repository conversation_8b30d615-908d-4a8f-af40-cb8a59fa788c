import { computed } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { IOrderItem } from '../order/order.interface'

interface IRule {
  name: string
  validate: (item: any, list: any[]) => Boolean
}

export const comboList = [
  {
    extra: ['membership-ask-doctor', 'membership-ask-doctor-combo'],
    main: ['consultation-knowledge-question', 'consultation-question']
  },
  {
    extra: ['membership-ask-doctor', 'membership-personal-doctor'],
    main: [
      'membership-ask-doctor-combo',
      'membership',
      'pregnancy-diary',
      'health-program-baby-development'
    ]
  },
  {
    extra: ['personal-doctor-addon'],
    main: ['health-program-baby-development']
  },
  {
    extra: ['personal-doctor-addon'],
    main: ['membership-personal-doctor', 'membership']
  }
]

const removeOrderItemRules: Array<IRule> = [
  {
    name: 'isMultipleOrder',
    validate(_item, list) {
      return list.length > 1
    }
  },
  // {
  //   name: 'isAddonsProduct',
  //   validate(item, _list) {
  //     const addonsList = ['membership-ask-doctor', 'membership-personal-doctor']
  //     return addonsList.includes(item?.product?.sku)
  //   }
  // }
  {
    name: 'notIsMainInCombo',
    validate(item, list) {
      const sku: string = item?.product?.sku
      for (const combo of comboList) {
        if (combo.main.includes(sku)) {
          // If the this orderItem is the main product within a combo, prevent the user from removing it.
          const hasExtraProduct = list.some((i) =>
            combo.extra.includes(i?.product?.sku)
          )
          if (hasExtraProduct) return false
          // Allow deletion of combos, if the list contains individual products or unrelated combos.
          const mainProduct = list.filter((i) =>
            combo.main.includes(i?.product?.sku)
          )
          if (mainProduct.length >= list.length) return false
        }
      }
      return true
    }
  }
]

export function useRemoveRules(
  orderItem: Ref<IOrderItem<any>> | ComputedRef<IOrderItem<any>>,
  orderItems: Ref<Array<IOrderItem<any>>> | ComputedRef<Array<IOrderItem<any>>>
) {
  const validate = (
    orderItem: IOrderItem<any>,
    orderItems: Array<IOrderItem<any>>
  ) => {
    return removeOrderItemRules.every((rule) =>
      rule.validate(orderItem, orderItems)
    )
  }
  const removeable = computed<boolean>(() =>
    validate(orderItem.value, orderItems.value)
  )
  return {
    removeable,
    validate
  }
}
