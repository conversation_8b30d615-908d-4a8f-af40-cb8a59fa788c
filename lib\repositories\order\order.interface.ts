import { IFile } from '../../models/file'

export interface IOrder {
  _id: string
  updatedAt: string
  createdAt: string
  sortOrder: number
  code: number
  organization: any
  deliverTo: string
  description: string
  vendor: any
  meta: {
    flowProduct: string
    productType: string
  }
  __v: number
  cancelable: boolean
  isReceived: boolean
  isConfirmed: boolean
  requestVat: boolean
  delivery: {
    fee: number
    to: {
      name: string
      phone: number
      address: {
        street1: number
      }
    }
  }
  isDelivery: boolean
  prescription: {
    diagnosis: any[]
  }
  // temp_total: number
  total: number
  discount: number
  customDiscount: number
  subTotal: number
  voucherAmount: number
  stateVoucher: string
  payment: {
    source: string
    state: string
    type: string
    wallet: string
  }
  isThirdParty: boolean
  date: string
  state: string
  promotions: string[]
  voucher: string
  tags: string[]
}

export interface IOrderItem<T> {
  _id?: string
  order?: string
  title?: string
  description?: string
  note?: string
  image?: IFile | { url: string }
  type?: string
  state?: string
  product?: string
  meta?: T
  source?: string
  paymentTerms?: string
  deliverTo?: string
  quantity?: number
  case?: any
  total?: number
  price?: number
  canPay?: boolean
  createdAt?: string
}

export interface IPopulatedOrderItem<T>
  extends Omit<IOrderItem<T>, 'order' | 'product'> {
  order: IOrder
  product?: {
    _id: '629037ca36c554a30948b928'
    name: 'Mua thuốc'
    slug: 'mua-thuoc-pharmacity'
    sku: 'prescription'
    type: {
      _id: '6290310e36c5540e1448b927'
      name: 'prescription-deliver'
    }
    price: 0
  }
}
