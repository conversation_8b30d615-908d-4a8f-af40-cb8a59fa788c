import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useUser } from '@wellcare/nuxt-module-account/repositories'
import { Capacitor } from '@capacitor/core'
import { prepareSingleOrderForProduct } from '../wellcare-api-urls'
import { AXIOS_TIMEOUT } from '../config'

interface OrderOption {
  product: {
    slug: string
  }
  _id?: string
}

function getSource() {
  const platform = Capacitor.getPlatform()
  if (platform === 'web') return document.location.host
  return 'vn_capacitor_' + platform
}

export function prepareSingleOrder(
  option?: Ref<OrderOption> | ComputedRef<OrderOption>
) {
  const { post } = useWellcareApi()
  const { $dayjs } = useContext()
  const { user } = useUser()
  const { onSucess, execute, loading, onError } = useRepository<IResponse<any>>(
    {
      fetcher: (param) => {
        if (param?.product?.slug)
          return post({
            url: prepareSingleOrderForProduct(),
            data: {
              product: param.product,
              // if platform is web, return url source, else return native platform
              source: getSource(),
              description: `${user.value.name} ${
                param.product.slug
              } ${$dayjs().format('YYYY-MM-DD HH:mm')} `
            },
            timeout: AXIOS_TIMEOUT
          })
      },
      conditions: option,
      useFetch: false,
      manual: true,
      toastOnError: false
    }
  )
  return {
    onSucess,
    execute,
    loading,
    onError
  }
}
