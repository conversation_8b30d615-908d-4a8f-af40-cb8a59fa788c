import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { updateOrderUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { IOrder } from './order.interface'

export function removeOrder(id: Ref<string> | ComputedRef<string>) {
  const { del } = useWellcareApi()
  const { $toast } = useContext()
  const repo = useRepository<IResponse<IOrder>>({
    fetcher: (id) => del({ url: updateOrderUrl(id) }),
    conditions: id,
    useFetch: false,
    manual: true
  })
  repo.onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return repo
}
