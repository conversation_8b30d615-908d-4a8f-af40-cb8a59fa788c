import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IOrder } from 'lib/models/order/props-order'
import { searchOrderUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { ISearchOption } from '../search-option.interface'

export function searchOrder(
  option: Ref<ISearchOption> | ComputedRef<ISearchOption>,
  fetchOption?: any
) {
  const { get } = useWellcareApi()
  const { $toast } = useContext()

  const repo = useRepository<IResponse<IOrder[]>>({
    fetcher: (params) => get({ url: searchOrderUrl(), params }),
    conditions: option,
    useFetch: false,
    manual: fetchOption?.manual || true,
    toastOnError: fetchOption?.toastOnError || true
  })

  repo.onSucess((data) => {
    if (!['200', '201'].includes(data.code.toString()))
      $toast.error(data.message)
  })

  return repo
}
