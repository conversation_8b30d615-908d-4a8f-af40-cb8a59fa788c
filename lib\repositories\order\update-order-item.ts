import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { updateOrderItemUrl } from '../wellcare-api-urls'
import { IOrderItem } from './order.interface'

export function updateOrderItem(
  options: Ref<IOrderItem<any>> | ComputedRef<IOrderItem<any>>
) {
  const { put } = useWellcareApi()
  const { $toast } = useContext()
  const repo = useRepository<any>({
    fetcher: (params) =>
      put({
        url: updateOrderItemUrl(params._id),
        data: {
          update: params
        }
      }),
    conditions: options,
    useFetch: false,
    toastOnError: true
  })
  repo.onError((e) => {
    $toast.global?.appError({
      message: e.message + '!'
    })
  })
  return repo
}
