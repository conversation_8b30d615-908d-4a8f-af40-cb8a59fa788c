import { useContext, useStore } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IOrder } from 'lib/models/order/props-order'
import { updateOrderUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'

export function updateOrder(option?: Ref<any> | ComputedRef<IOrder>) {
  const { commit } = useStore()
  const { put } = useWellcareApi()
  const { $toast } = useContext()
  const repo = useRepository<IResponse<IOrder>>({
    fetcher: (params) => {
      if (params._id)
        return put({
          url: updateOrderUrl(params._id),
          data: {
            update: params
          }
        })
    },
    conditions: option,
    useFetch: false
  })
  repo.onSucess((response) => {
    if (response.code === 200) {
      commit('checkout/updateField', {
        path: 'order',
        value: response.results
      })
    } else $toast.error(response.message)
  })
  repo.onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return repo
}
