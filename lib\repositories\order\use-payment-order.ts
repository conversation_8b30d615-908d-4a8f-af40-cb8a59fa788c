/* eslint-disable */
import { computed, ComputedRef, ref, Ref, useContext, useStore, watch } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import usePointConversion from '../payment/use-point-conversion'
import { IFetchOption } from '../fetch-option.interface'
import { IResponse } from '../response.interface'
import { AXIOS_TIMEOUT } from '../config'
import { getOrderByIdUrl, searchOrderItemUrl, searchPromotion as searchPromotionUrl } from '../wellcare-api-urls'
import { IOrder, IOrderItem } from './order.interface'

interface OrderOption {
  _id?: string
}

export function usePaymentOrder(
  option: Ref<OrderOption> | ComputedRef<OrderOption>,
  fetchOption?: Ref<IFetchOption> | ComputedRef<IFetchOption>,
  filterOption?: Ref<String> | ComputedRef<String>
) {
  const MAX_RELOAD_ORDER_COUNT = 20
  const reloadOrderCount = ref(0)
  const { get, post } = useWellcareApi()
  const { commit, state } = useStore()
  const { $toast, $config, app } = useContext()
  const conversionConfig = computed(() => $config.checkout?.payment?.pointConversion)
  // initiate value from store
  const order: ComputedRef<IOrder> = computed(() => state['checkout']['order'])
  const orderItems: ComputedRef<IOrderItem<any>[]> = computed(() => state['checkout']['orderItems'])
  const promotions = computed(() => state['checkout']['promotions'])
  const getMembership = computed(() => state['authen']['user']['membership'])
  const authenUser = computed(() => state['authen']['user'])
  const wallets = computed(() => state['checkout']['wallets'])
  const pointWallet = computed(() => wallets.value.find((wallet: any) => wallet.type === 'point'))
  // clear store if payment for other order
  if (option.value._id != order.value._id) {
    commit('checkout/updateField', {
      path: 'order',
      value: {}
    })
    commit('checkout/updateField', {
      path: 'orderItem',
      value: {}
    })
    commit('checkout/updateField', {
      path: 'orderItems',
      value: []
    })
  }
  // DECLARE SERVICES:
  const {
    execute: searchPromotion,
    onSucess: onSearchPromotionSuccess,
    loading: searchPromotionLoading
  } = useRepository({
    fetcher: (params) => {
      if (params) return post({ url: searchPromotionUrl(), data: params })
    },
    conditions: option,
    useFetch: false
  })

  const { execute: executeGetOrder, onSucess: onGetOrderSuccess } = useRepository<IResponse<IOrder>>({
    fetcher: (param) => {
      return get({
        url: getOrderByIdUrl(param._id),
        params: {
          fields:
            'state,payment,delivery,deliveryTo,voucher,voucherAmount,subTotal,discount,total,vat,requestVat,meta,stateVoucher,promotions,systemTotal,systemAmount,sku'
        },
        timeout: AXIOS_TIMEOUT
      })
    },
    conditions: option,
    useFetch: false,
    toastOnError: true
  })
  const {
    execute: executeGetOrderItem,
    onSucess: onGetOrderItemSuccess,
    loading: getOrderItemLoading
  } = useRepository<IResponse<IOrderItem<any>>>({
    fetcher: (param) => {
      return get({
        url: searchOrderItemUrl(),
        timeout: AXIOS_TIMEOUT,
        params: {
          filter: filterOption.value
            ? {
                order: param._id,
                state: filterOption.value
              }
            : {
                order: param._id
              },
          fields:
            'product,state,price,quantity,meta,_id,total,subTotal,title,description,image,type,sku,occurredAt,note,customPrice',
          populate: JSON.stringify([
            {
              path: 'product',
              select: 'name provider slug paymentMethod state sku'
            },
            { path: 'type', select: 'name type' }
          ])
        }
      })
    },
    conditions: option,
    useFetch: false,
    toastOnError: true
  })
  const policyKey = conversionConfig.value?.key
  const paymentLoading = ref(true)
  const getOrderLoading = ref(true)
  const { convertPointRequest } = usePointConversion(policyKey, order)
  onSearchPromotionSuccess((response: any) => {
    if (response.code === 200 || response.code === 201) {
      commit('checkout/updateField', {
        path: 'promotions',
        value: response.results
      })
    } else $toast.error(response.message)
  })

  onGetOrderSuccess((response) => {
    const specialProgramFlag = [
      '/pregnancy/'
      // '/another_special_program_related_string/'
    ]

    const isSpecialProgramFlag = computed(() =>
      state['checkout']['orderItems'].some((item: any) =>
        specialProgramFlag.some((keyword) =>
          new RegExp(keyword).test(item?.product?.sku || new RegExp(keyword).test(item?.product?.type?.name))
        )
      )
    )

    if (response.code === 200) {
      if (
        response.results.total === 0 &&
        response.results.voucherAmount === 0 &&
        !isSpecialProgramFlag &&
        reloadOrderCount.value <= MAX_RELOAD_ORDER_COUNT
      ) {
        reloadOrderCount.value++
        setTimeout(() => executeGetOrder(), 2000)
      } else {
        getOrderLoading.value = false
      }
      commit('checkout/updateField', {
        path: 'order',
        value: {
          ...(state as any).checkout.order,
          ...response.results
        }
      })
      executeGetOrderItem()
    } else {
      $toast.error(response.message)
      commit('checkout/updateField', {
        path: 'order',
        value: { state: 'other' }
      })
    }
  })

  onGetOrderItemSuccess((response: any) => {
    if (response.code === 200) {
      commit('checkout/updateField', {
        path: 'orderItems',
        value: response.results
      })
      if (response.results?.length === 1 && response.results[0]?.meta?.noCost) {
        reloadOrderCount.value = MAX_RELOAD_ORDER_COUNT + 1
      }
      if (order.value.state === 'placed') return
      searchPromotion({
        user: {
          ...authenUser.value,
          membership: getMembership.value
        },
        order: {
          ...order.value,
          source: process.client ? window.location.host : undefined
        },
        orderItems: orderItems.value,
        issuer: { slug: $config?.checkout?.payment?.promotion }
      })

      if (!conversionConfig.value?.autoConvert || order.value.voucher) return
      const jwt = app.$cookies?.get('org_jwt')
      const usable = pointWallet.value.usable * conversionConfig.value.rate
      const min = usable >= order.value.total ? order.value.total : usable

      const convertOption = {
        beneficiary: authenUser.value._id,
        amount: min,
        note: `jwt=${jwt},exp=${conversionConfig.value.rate}`
      }
      convertPointRequest(convertOption)
    } else $toast.error(response.message)
  })
  watch([searchPromotionLoading, getOrderLoading, getOrderItemLoading], () => {
    paymentLoading.value = searchPromotionLoading.value || getOrderItemLoading.value || getOrderLoading.value
  })

  return {
    order,
    authenUser,
    orderItems,
    promotions,
    paymentLoading,
    getOrderLoading,
    executeGetOrder,
    onGetOrderSuccess,
    executeGetOrderItem,
    onGetOrderItemSuccess,
    onSearchPromotionSuccess
  }
}
