/* eslint-disable */
import { Capacitor } from '@capacitor/core'
import { IProduct } from '@lib/models/product'
import {
  computed,
  ComputedRef,
  ref,
  Ref,
  useContext,
  useStore,
  watch
} from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { AXIOS_TIMEOUT } from '../config'
import { IFetchOption } from '../fetch-option.interface'
import { IResponse } from '../response.interface'
import {
  getProductBySlugUrl,
  prepareMultipleOrderForProduct,
  prepareSingleOrderForProduct,
  updateOrderItemUrl,
  updateOrderUrl
} from '../wellcare-api-urls'
import { IOrder, IOrderItem } from './order.interface'
interface OrderOption {
  product: {
    slug: string
  }
  _id?: string
}

function getSource() {
  const platform = Capacitor.getPlatform()
  if (platform === 'web') return document.location.host
  return 'vn_capacitor_' + platform
}

export function useProductOrder<P = any>(
  option?: Ref<OrderOption> | ComputedRef<OrderOption>,
  fetchOption?: IFetchOption
) {
  const { post, put, get } = useWellcareApi()
  const { commit, state } = useStore()
  const { $toast, $dayjs } = useContext()
  // const route = useRoute()

  // initiate value from store
  const order = computed(() => state['checkout']['order'])
  const orderItems = computed<Array<any>>(() => state['checkout']['orderItems'])
  const authenUser = computed(() => state['authen']['user'])
  const product = ref<any>({
    includes: []
  })
  const provider = computed(() => state['checkout']['provider'])
  const mainProductName = computed<string>(
    () => state['checkout']['mainProductName']
  )
  // const orderItem = computed(() => state['checkout']['orderItem'])
  const orderItem = computed(() => {
    return orderItems.value.find(
      (item) => item?.product?.slug === mainProductName.value
    )
  })

  const loading = ref(false)
  const orderItemData = ref({})
  let countReload = 0
  // DECLARE SERVICES:

  const {
    onSucess: onProductSuccess,
    execute,
    onError: onProductError
  } = useRepository<IResponse<IProduct>>({
    fetcher: (params) =>
      get({
        url: getProductBySlugUrl(params.product.slug),
        params: {
          fields:
            params.fields ||
            'type._id,price,includes.price,includes.specialties,includes.title,template,provider.avatar,provider.spoken,sku'
        },
        timeout: AXIOS_TIMEOUT
      }),
    conditions: option,
    useFetch: fetchOption?.useFetch || false,
    manual: true,
    toastOnError: false
  })

  const {
    onSucess: onProductOrderSuccess,
    execute: reloadOrder,
    loading: getOrderLoading,
    onError: onProductOrderError
  } = useRepository<IResponse<any>>({
    fetcher: (param) => {
      if (param?.product?.slug)
        return post({
          url: prepareSingleOrderForProduct(),
          data: {
            product: param.product,
            // if platform is web, return url source, else return native platform
            source: getSource(),
            description: `${authenUser.value.name} ${
              param.product.slug
            } ${$dayjs().format('YYYY-MM-DD HH:mm')} `
          },
          timeout: AXIOS_TIMEOUT
        })
    },
    conditions: option,
    useFetch: false,
    manual: true,
    toastOnError: false
  })

  const {
    onSucess: onCreateSingleOrderSuccess,
    execute: createSingleOrder,
    loading: getCreateSingleOrderLoading,
    onError: onCreateSingleOrderError
  } = useRepository<IResponse<any>>({
    fetcher: (param) => {
      if (param?.product?.slug)
        return post({
          url: prepareSingleOrderForProduct(),
          data: {
            product: param.product,
            // if platform is web, return url source, else return native platform
            source: getSource(),
            description: `${authenUser.value.name} ${
              param.product.slug
            } ${$dayjs().format('YYYY-MM-DD HH:mm')} `
          },
          timeout: AXIOS_TIMEOUT
        })
    },
    conditions: option,
    useFetch: false,
    manual: true,
    toastOnError: false
  })

  const {
    onSucess: onMultipleProductOrderSuccess,
    execute: executeMultipleOrder,
    loading: getMultipleOrderLoading,
    onError: onMultipleProductOrderError
  } = useRepository<IResponse<any>>({
    fetcher: (param) => {
      if (param?.product?.slug)
        return post({
          url: prepareMultipleOrderForProduct(),
          data: {
            product: param.product,
            // if platform is web, return url source, else return native platform
            source: getSource(),
            description: `${authenUser.value.name} ${
              param.product.slug
            } ${$dayjs().format('YYYY-MM-DD HH:mm')} `
          },
          timeout: AXIOS_TIMEOUT
        })
    },
    conditions: option,
    useFetch: false,
    manual: true,
    toastOnError: false
  })

  const {
    execute: executeUpdateOrderItem,
    onSucess: onUpdateOrderItemSuccess,
    onError: onUpdateOrderItemError,
    loading: loadingUpdateOrderItem
  } = useRepository<IResponse<IOrderItem<any>>>({
    fetcher: (param) =>
      put({
        url: updateOrderItemUrl(param._id),
        data: {
          update: param
        },
        timeout: AXIOS_TIMEOUT
      }),
    useFetch: false,
    manual: true,
    toastOnError: true
  })

  const {
    execute: executeUpdateOrder,
    onError: onUpdateOrderError,
    onSucess: onUpdateOrderSuccess,
    loading: loadingUpdateOrder
  } = useRepository<IResponse<IOrder>>({
    fetcher: (param) =>
      put({
        url: updateOrderUrl(order.value._id),
        data: param,
        timeout: AXIOS_TIMEOUT
      }),
    useFetch: false,
    manual: true,
    toastOnError: true
  })

  // flow
  onProductSuccess((res) => {
    // commit('checkout/updateField', {
    //   path: 'orderItem',
    //   value: { product: res.results }
    // })
    product.value = res.results
    commit('checkout/SET_PRODUCT_ORDER_ITEM', res.results)
    reloadOrder()
  })

  onProductOrderSuccess((response) => {
    if (response.code === 200) {
      const { orderItems, order } = response.results

      // Check if the first order item's product slug matches the mainProductName
      // const mainOrderItem = orderItems[0]
      // if (mainOrderItem?.product?.slug === mainProductName.value) {
      //   commit('checkout/SET_MAIN_ORDER_ITEM', mainOrderItem)
      // }

      // Update order field
      commit('checkout/updateField', { path: 'order', value: order })

      if (orderItems.length > 0) {
        // Update orderItem and orderItems fields
        // const updatedOrderItem = { ...orderItems[0], product: product.value }
        // commit('checkout/SET_ORDER_ITEM', updatedOrderItem)
        orderItems.forEach((element, i) => {
          if (element?.product?._id === product.value._id)
            orderItems[i].product = product.value
        })
        commit('checkout/SET_ORDER_ITEMS', orderItems)
      } else {
        $toast.error('Cannot prepare order item')
      }
    } else {
      $toast.error(response.message)
    }
  })

  const updateOrder = (_data: IOrder) => {
    executeUpdateOrder(_data)
    onUpdateOrderSuccess((res) => {
      if (res.code === 200) {
        commit('checkout/updateField', {
          path: 'order',
          value: res.results
        })
      }
    })
  }

  const updateOrderItem = (_data: IOrderItem<P>) => {
    // if (loadingUpdateOrderItem.value) return
    executeUpdateOrderItem(_data)
    onUpdateOrderItemSuccess((res) => {
      if (res.code === 200) {
        commit('checkout/SET_ORDER_ITEM', {
          ...orderItem.value,
          ..._data
        })

        // const items = orderItems.value?.map((i: any) => {
        //   if (i._id == _data._id)
        //     return {
        //       ...orderItem.value,
        //       ..._data
        //     }
        //   else return i
        // })
        // commit('checkout/SET_ORDER_ITEMS', items)
      } else {
        $toast.error(res.message)
      }
    })
  }

  // avoid situation call a api multiple times at the moment
  const setOrderItem = (newData: any) => {
    if (Object.keys(newData).length > 0)
      orderItemData.value = {
        ...orderItemData.value,
        ...newData
      }
  }

  const setOrderItemMeta = async (newMeta: any, canPay = false) => {
    // if (Object.keys(newMeta).length === 0) return
    const data: IOrderItem<any> = {
      _id: orderItem.value._id,
      ...orderItemData.value,
      meta: { ...orderItem.value.meta, ...newMeta }
    }

    if (canPay) data.canPay = true
    await updateOrderItem(data)
  }

  onUpdateOrderItemError((error) => $toast.error(error))
  onUpdateOrderError(() => {
    if (countReload <= 5) reloadOrder()
    countReload++
  })
  onProductOrderError((e) => {
    console.log('=== ERROR ===')
    console.log(e.message)
    if (countReload <= 5) reloadOrder()
    countReload++
  })

  watch([loadingUpdateOrder, loadingUpdateOrderItem, getOrderLoading], () => {
    loading.value =
      loadingUpdateOrder.value ||
      loadingUpdateOrderItem.value ||
      getOrderLoading.value
  })

  return {
    authenUser,
    provider,
    execute,
    loading,
    onProductOrderSuccess,
    onProductOrderError,
    onProductSuccess,
    onProductError,
    order,
    orderItem,
    orderItems,
    product,
    setOrderItem,
    setOrderItemMeta,
    updateOrder,
    updateOrderItem,
    reloadOrder,
    onMultipleProductOrderSuccess,
    executeMultipleOrder,
    getMultipleOrderLoading,
    onMultipleProductOrderError,
    executeUpdateOrderItem,
    onCreateSingleOrderSuccess,
    createSingleOrder,
    getCreateSingleOrderLoading,
    onCreateSingleOrderError
  }
}
