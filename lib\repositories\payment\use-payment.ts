import { useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { zalopayPayment, momoPayment } from '../wellcare-api-urls'

export interface ITopupRequest {
  // eslint-disable-next-line camelcase
  amount: number
  user: string
  phone: string
  name: string
  // eslint-disable-next-line camelcase
  return_url: any
  metadata: {
    user: string
    order: string
    description: string
  }
  bankCode?: string
  order?: string
  // eslint-disable-next-line camelcase
  app_id?: string
}
export function usePayment(
  option: Ref<ITopupRequest> | ComputedRef<ITopupRequest>
) {
  const { post } = useWellcareApi()
  const { app, $toast } = useContext()
  // eslint-disable-next-line dot-notation
  option.value.app_id =
    app.$config.checkout?.payment?.gatewayId || '60bf536592b7cf4c8de14755'
  option.value.user = option.value.user ? option.value.user : 'empty'
  const {
    execute: createZalopayPayment,
    onSucess: onZalopaySuccess,
    onError: onZalopayError
  } = useRepository({
    fetcher: (params) => {
      if (params) return post({ url: zalopayPayment(), data: params })
    },
    conditions: option,
    useFetch: false
  })
  onZalopaySuccess((response: any) => {
    if (response.code === 200 || response.code === 201) {
      window.location.href = response.results.orderurl
    } else {
      $toast.error(response.message || response.error)
    }
  })
  onZalopayError((error: any) => {
    $toast.error(
      error.response?.data?.message ||
        error.response?.data?.error ||
        error.message
    )
  })
  const {
    execute: createMomoPayment,
    onSucess: onMomoSuccess,
    onError: onMomoError
  } = useRepository({
    fetcher: (params) => {
      if (params) return post({ url: momoPayment(), data: params })
    },
    conditions: option,
    useFetch: false
  })
  onMomoSuccess((response: any) => {
    if (response.code === 200 || response.code === 201) {
      window.location.href = response.results.payUrl
    } else {
      $toast.error(response.message || response.error)
    }
  })
  onMomoError((error: any) => {
    $toast.error(
      error.response?.data?.message ||
        error.response?.data?.error ||
        error.message
    )
  })
  return {
    createZalopayPayment,
    createMomoPayment,
    onZalopaySuccess,
    onMomoSuccess,
    onMomoError,
    onZalopayError
  }
}
