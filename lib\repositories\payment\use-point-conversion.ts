import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { computed, useContext } from '@nuxtjs/composition-api'
import { convertPointByKeyUrl } from '../wellcare-api-urls'
import { updateOrder } from '../order/update-order'
/*
interface IConvertOption {
  beneficiary: string
  amount: number
  note: string
}
*/
export default (key: string, order: any) => {
  const { post } = useWellcareApi()
  const { $config } = useContext()
  const autoApply = computed(
    () => $config.checkout?.payment?.voucher?.autoApply
  )
  const { execute: updateOrderRequest } = updateOrder(order)
  const { execute: convertPointRequest, onSucess: onConvertSuccess } =
    useRepository({
      fetcher: (params) => {
        return post({ url: convertPointByKeyUrl(key), data: params })
      },
      useFetch: false
    })
  onConvertSuccess((response: any) => {
    const { code } = response.results
    if (!autoApply.value) return
    updateOrderRequest({
      _id: order.value._id,
      voucher: code
    })
  })
  return {
    convertPointRequest
  }
}
