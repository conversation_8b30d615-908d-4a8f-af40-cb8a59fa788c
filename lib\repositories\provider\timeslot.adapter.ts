import { computed, ref, useContext, watch } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { IProviderTimeslot } from './use-provider-timeslots'

interface ITarget {
  from: string
  to: string
  fee: any
}

export function timeslotAdapter(
  source: Ref<IProviderTimeslot[]> | ComputedRef<IProviderTimeslot[]>
): any {
  const { $dayjs } = useContext()
  const dayFormatString = 'DD-MM-YYYY'

  function adapt(source: IProviderTimeslot[]): ITarget[] {
    const target = []
    source.forEach((i) => {
      i.slots?.forEach((slot) => {
        target.push({
          from: slot.from,
          to: slot.to,
          fee: i.fee
        })
      })
    })
    return target
  }

  const target = ref<ITarget[]>(adapt(source.value))
  watch(source, (newValue) => {
    target.value = adapt(newValue)
  })

  interface IChooseDate {
    date: string
    surcharge: number
    surchargeReason: 'sunday' | 'holiday'
  }
  const dates: ComputedRef<IChooseDate[]> = computed(() => {
    const output = {}
    target.value?.forEach((i) => {
      output[$dayjs(i.from).format(dayFormatString)] = {
        surcharge: i.fee.isHoliday ? i.fee.holiday || 0 : 0,
        surchargeReason: i.fee.reason
      }
    })
    const res = Object.keys(output).map((dateString) => ({
      ...output[dateString],
      date: dateString
    }))
    return res
  })

  const getTimeslotsInDate = (day: string) => {
    const output = []
    target.value
      .filter((i) => $dayjs(i.from).format(dayFormatString) === day)
      .forEach((i) => {
        output.push({
          from: $dayjs(i.from).format('HH:mm'),
          to: $dayjs(i.to).format('HH:mm')
        })
      })
    return output
  }

  return { target, adapt, dates, getTimeslotsInDate }
}
