import { IProvider } from '@lib/models/provider'
import { IAvatar } from '@lib/models/user'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { reactive, ref, useContext } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import timezone from 'dayjs/plugin/timezone'
import { IResponse } from '../response.interface'
import {
  getProviderTimeslotsLocationUrl,
  getProviderTimeslotsUrl,
  searchProviderUrl
} from '../wellcare-api-urls'

export interface IProviderTimelsotOption {
  slug: string
  consultTime?: number
  date?: string
  medium?: string[]
  flow?: string
}
interface ISlot {
  from: string
  to: string
  ct: number
}
export interface IProviderTimeslot {
  date: string
  slots: ISlot[]
  fee: {
    followUpDiscount: {
      clinic: number
      virtual: number
    }
    chatBlock: number
    minute: number
    chat: number
    reason: 'sunday' | 'holiday'
    video: number
    phone: number
    holiday: number
    normal: number
    base: number
    total: number
  }
  avatar?: IAvatar
  location?: any[]
}

export interface TimeSlotsData {
  consultTime: number
  slots: IProviderTimeslot[]
  fee?: number
}

export function useProviderTimeslots(
  option: Ref<IProviderTimelsotOption> | ComputedRef<IProviderTimelsotOption>,
  countTimeSlot: boolean = false
) {
  const { post, get } = useWellcareApi()
  const { $dayjs, $toast } = useContext()
  $dayjs.extend(timezone)
  // const timeslots: Ref<IProviderTimeslot[]> = ref([])
  const timeslotsData: TimeSlotsData[] = reactive([])
  const consultTime: Ref<number> = ref(1)

  function getConsulTime(timeslots: IProviderTimeslot[]) {
    const slot = timeslots[0].slots[0]
    return $dayjs(slot.to).diff(slot.from, 'm')
  }

  function getLocationId(provider) {
    return provider.location[0]._id
  }

  function checkWorkflow(workflow, provider) {
    return workflow === 'consultation-therapy' && provider.location.length > 0
  }

  const provider = ref({
    consultTime: 1,
    fee: {
      video: 1,
      audio: 1
    },
    _id: '',
    avatar: {
      url: ''
    },
    mediumEnabled: {
      chat: true,
      phone: true,
      video: true
    },
    location: []
  })

  // DECLARE SERVICES
  const { execute: executeSearchProvider, onSucess: onSearchProviderSuccess } =
    useRepository<IResponse<IProvider>>({
      fetcher: (params) => {
        if (params.slug)
          return get({
            url: searchProviderUrl(),
            params: {
              filter: {
                slug: params.slug
              },
              fields:
                'consultTime,fee,user,avatar,mediumEnabled,name,location,spoken',
              populate: JSON.stringify([
                {
                  path: 'location'
                }
              ])
            }
          })
      },
      conditions: option,
      useFetch: false,
      manual: true,
      toastOnError: true
    })

  const {
    execute: executeGetProviderTimeslots,
    onSucess: onGetProviderTimeslotsSuccess,
    onError: onGetProviderTimeslotsError,
    loading
  } = useRepository<IResponse<IProviderTimeslot[]>>({
    fetcher: (params) => {
      if (params)
        return post({
          url: checkWorkflow(option.value.flow, provider.value)
            ? getProviderTimeslotsLocationUrl(
                provider.value._id,
                getLocationId(provider.value)
              )
            : getProviderTimeslotsUrl(provider.value._id),
          data: {
            type: ['booking'],
            algo: 'random',
            numberOfDays: 7,
            show: 10,
            medium: ['phone', 'video', 'inperson'],
            duration: params,
            // delay: 60,
            from: $dayjs().toISOString()
          },
          timeout: 10000
        })
    },
    conditions: consultTime,
    useFetch: false,
    manual: true,
    toastOnError: false
  })
  onSearchProviderSuccess((res) => {
    if (res.code !== 200) {
      $toast.error(res.message)
    } else {
      provider.value = res.results[0]
      if (countTimeSlot) {
        option.value.consultTime = provider.value.consultTime
        consultTime.value = provider.value.consultTime * 1
        executeGetProviderTimeslots()
        consultTime.value = provider.value.consultTime * 2
        executeGetProviderTimeslots()
        consultTime.value = provider.value.consultTime * 3
        executeGetProviderTimeslots()
        consultTime.value = provider.value.consultTime * 4
        executeGetProviderTimeslots()
      }
    }
  })

  onGetProviderTimeslotsSuccess((res) => {
    if (res.code !== 200) {
      $toast.error(res.message)
    }
    // timeslots.value = res.results
    else if (res.results.length > 0) {
      timeslotsData.push({
        consultTime: getConsulTime(res.results),
        slots: res.results,
        fee: 0
      })
      timeslotsData.sort((a, b) => a.consultTime - b.consultTime)
    }
  })

  return {
    executeGetProviderTimeslots,
    executeSearchProvider,
    onGetProviderTimeslotsSuccess,
    onGetProviderTimeslotsError,
    onSearchProviderSuccess,
    timeslotsData,
    provider,
    loading
  }
}
