import { IProvider } from '@lib/models/provider'
import { computed, ref } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IResponse } from '../response.interface'
import { searchProviderUrl } from '../wellcare-api-urls'
export function useProvider(option: Ref<any> | ComputedRef<any>) {
  const { get } = useWellcareApi()
  const providers: any = ref([])
  const provider = computed(() => providers.value[0])
  const {
    execute: executeSearchProvider,
    onSucess: onSearchProviderSuccess,
    loading: searchProviderLoading
  } = useRepository<IResponse<IProvider>>({
    fetcher: (params) => {
      if (params)
        return get({
          url: searchProviderUrl(),
          params
        })
    },
    conditions: option,
    useFetch: false
  })

  onSearchProviderSuccess((res: IResponse<IProvider>) => {
    providers.value = res.results
  })

  const loading = computed(() => searchProviderLoading.value)

  return {
    executeSearchProvider,
    loading,
    providers,
    provider
  }
}
