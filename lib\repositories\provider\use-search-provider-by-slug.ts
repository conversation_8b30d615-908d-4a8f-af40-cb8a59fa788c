import {
  useRepository,
  useWellcareApi,
  IFetchOption
} from '@wellcare/nuxt-module-data-layer'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { computed } from '@nuxtjs/composition-api'
import { searchProviderBySlug } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { IUserWallet } from '../../models/index'

export function useSearchProviderBySlug(
  option: Ref<IUserWallet> | ComputedRef<IUserWallet>,
  _fetchOption?: Ref<IFetchOption> | ComputedRef<IFetchOption>
) {
  const { get } = useWellcareApi()
  const {
    onSucess,
    response,
    loading: searchProviderBySlugLoading,
    execute
  } = useRepository<IResponse<IUserWallet[]>>({
    fetcher: (params) => {
      return get({
        url: searchProviderBySlug(params)
      })
    },
    conditions: option,
    useFetch: true,
    manual: false,
    toastOnError: true
  })
  const results = computed(() => response.value?.results)
  return { onSucess, response, searchProviderBySlugLoading, execute, results }
}
