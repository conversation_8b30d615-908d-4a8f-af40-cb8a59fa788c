import { computed } from '@nuxtjs/composition-api'
import type { ComputedRef } from '@nuxtjs/composition-api'
import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'

export function searchProviderElastic(query: ComputedRef<any>) {
  const { post } = useWellcareApi()
  const { onSucess, response, loading, execute } = useRepository<
    IResponse<any>
  >({
    fetcher: (q) => {
      return post({
        url: '/elastic-read/search/providers/_search',
        data: q
      })
    },
    conditions: query,
    useFetch: true,
    manual: false,
    toastOnError: true
  })
  const results = computed(() => response.value?.body?.hits?.hits ?? [])
  return { onSucess, response, loading, execute, results }
}
