import { ref, computed, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { IFetchOption } from '../fetch-option.interface'

interface NotionQueryOption {
  slug: string
  locale: string
  site: string
  status: string[]
  type: string
}

export function useSearchProvider(
  option: Ref<NotionQueryOption> | ComputedRef<NotionQueryOption>,
  _fetchOption?: Ref<IFetchOption> | ComputedRef<IFetchOption>
) {
  const { $axios, $toast, $config } = useContext()

  const loading = ref(false)
  const data = ref(null)

  const executeQuery = async () => {
    loading.value = true
    try {
      const payload = {
        size: 1,
        query: {
          bool: {
            filter: [
              {
                term: { 'page.properties.Site.keyword': option.value.site }
              },
              {
                terms: { 'page.properties.Status.keyword': option.value.status }
              },
              {
                term: { 'page.properties.Locale.keyword': option.value.locale }
              },
              { term: { 'page.properties.Slug.keyword': option.value.slug } },
              { term: { 'page.properties.Type.keyword': option.value.type } }
            ],
            should: []
          }
        },
        sort: [{ 'page.properties.Order': 'asc' }, '_score'],
        _source: {
          excludes: [
            'page.properties.Sub-item',
            'page.properties.Labels',
            'page.properties.Status',
            'page.properties.Site',
            'page.archived',
            'page.created_time'
          ],
          includes: ['page.properties.Sapo', 'blockstring']
        }
      }

      const res = await $axios.post(
        `${$config.endpoint}/elastic-read/search/notion-website/_search`,
        payload
      )

      if (res.status === 200 || res.status === 201) {
        const hits = res?.data.body?.hits?.hits
        if (hits) data.value = hits[0]?.page?.properties?.Sapo
      } else {
        $toast.error(
          res.data.message || 'An error occurred - user_search_provider()'
        )
      }
    } catch (error) {
      $toast.error('Failed to fetch Notion data')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  return {
    data: computed(() => data.value),
    loading: computed(() => loading.value),
    executeQuery
  }
}
