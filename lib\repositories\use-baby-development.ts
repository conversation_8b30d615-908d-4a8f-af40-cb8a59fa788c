import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { registerBabyDevelopmentUrl } from './wellcare-api-urls'

export function useBabyDevelopment() {
  const { post } = useWellcareApi()
  const {
    loading,
    execute: register,
    onError,
    onSuccess
  } = useRepository<IResponse<any>>({
    fetcher: (data) =>
      post({
        url: registerBabyDevelopmentUrl,
        data
      }),
    useFetch: false
  })
  return {
    loading,
    register,
    onError,
    onSuccess
  }
}
