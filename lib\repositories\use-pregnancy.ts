import { computed, useContext } from '@nuxtjs/composition-api'
import {
  useWellcareApi,
  useRepository,
  IResponse
} from '@wellcare/nuxt-module-data-layer'

export const usePregnancy = () => {
  const { post, put, del } = useWellcareApi()
  const { $toast } = useContext()

  const timeout = 10000

  const {
    execute: register,
    onSucess: onRegisterSuccess,
    loading: loadingRegister,
    onError: onErrorRegister
  } = useRepository<IResponse<any>>({
    fetcher: (data) => {
      if (data && Object.keys(data).length) {
        return post({
          url: '/health-program/pregnancy-diary/register',
          data,
          timeout
        })
      }
    },
    useFetch: false
    // toastOnError: true
  })

  const {
    execute: update,
    onSucess: onUpdateSuccess,
    loading: loadingUpdate,
    onError: onErrorUpdate
  } = useRepository<IResponse<any>>({
    fetcher: (data) => {
      if (data && Object.keys(data).length) {
        return put({
          url: '/health-program/pregnancy-diary/update',
          data,
          timeout
        })
      }
    },
    useFetch: false
    // toastOnError: true
  })

  const {
    execute: unregister,
    onSucess: onUnregisterSuccess,
    loading: loadingUnregister,
    onError: onErrorUnregister
  } = useRepository<IResponse<any>>({
    fetcher: () => {
      return del({
        url: '/health-program/pregnancy-diary/unregister',
        timeout
      })
    },
    useFetch: false
    // toastOnError: true
  })

  const loading = computed<boolean>(
    () =>
      loadingRegister.value || loadingUpdate.value || loadingUnregister.value
  )

  onErrorRegister((e) => {
    $toast.global?.appError({
      message: e?.message
    })
  })

  onErrorUpdate((e) => {
    $toast.global?.appError({
      message: e?.message
    })
  })

  onErrorUnregister((e) => {
    $toast.global?.appError({
      message: e?.message
    })
  })

  return {
    register,
    update,
    unregister,
    onErrorUnregister,
    onUnregisterSuccess,
    onUpdateSuccess,
    onRegisterSuccess,
    onErrorRegister,
    onErrorUpdate,
    loading
  }
}
