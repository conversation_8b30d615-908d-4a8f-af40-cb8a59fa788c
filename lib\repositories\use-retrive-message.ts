import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'
import { retriveMessageUrl } from './wellcare-api-urls'

export function useRetriveMessage() {
  const { get } = useWellcareApi()
  const { loading, execute, onError, onSuccess } = useRepository<
    IResponse<any>
  >({
    fetcher: (data) =>
      get({
        url: retriveMessageUrl,
        params: data
      }),
    useFetch: false
  })
  return {
    loading,
    execute,
    onError,
    onSuccess
  }
}
