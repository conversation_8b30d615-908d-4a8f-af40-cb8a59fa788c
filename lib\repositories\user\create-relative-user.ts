import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IResponse } from '../response.interface'
import { createUserUrl } from '../wellcare-api-urls'
import { IUser } from './user.interface'

export function createRelativeUser(data: Ref<any> | ComputedRef<any>) {
  const { post } = useWellcareApi()
  return useRepository<IResponse<IUser>>({
    fetcher: (param) =>
      post({
        url: createUserUrl(),
        data: param
      }),
    conditions: data,
    useFetch: false,
    manual: true,
    toastOnError: true
  })
}
