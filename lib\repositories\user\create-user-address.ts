import { useContext } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { createAddressURL } from '../wellcare-api-urls'

export function createUserAddress(options: Ref<{}>) {
  const { post } = useWellcareApi()
  const { $toast } = useContext()
  const { execute, loading, timer, response, onSucess, onError } =
    useRepository({
      fetcher: (conditions) =>
        post({
          url: createAddressURL(),
          data: conditions
        }),
      conditions: options,
      useFetch: false
    })
  onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return { execute, loading, timer, response, onSucess }
}
