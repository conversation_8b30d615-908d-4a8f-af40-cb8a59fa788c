import { computed } from '@nuxtjs/composition-api'
import {
  IResponse,
  useRepository,
  useWellcareApi
} from '@wellcare/nuxt-module-data-layer'

export function useUserDetail() {
  const { get } = useWellcareApi()
  const { loading, execute, onError, onSuccess, response } = useRepository<
    IResponse<any>
  >({
    fetcher: (data) =>
      get({
        url: '/api/user/detail/' + data.userId,
        params: {
          fields: 'name,phone,gender,dob,email,validated,avatar'
        }
      }),
    useFetch: false
  })
  const user = computed(() => response.value?.results)
  return {
    loading,
    execute,
    onError,
    onSuccess,
    user
  }
}
