import { watch, ref, useContext } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IAddress } from 'lib/models/user'
import { getUserAddressUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { ISearchOption } from '../search-option.interface'

export function getUserAddress(option?: Ref<ISearchOption>) {
  const { get } = useWellcareApi()
  const { $toast } = useContext()
  const results = ref<any[]>([])
  const repo = useRepository<IResponse<IAddress[]>>({
    fetcher: (params) =>
      get({
        url: getUserAddressUrl(),
        params: {
          skip: params.skip || 0,
          limit: params.limit || 7,
          sort: params.sort || '-updatedAt',
          count: true
        }
      }),
    conditions: option,
    useFetch: false
  })
  watch(repo.response, () => {
    results.value = repo.response.value.results
  })
  repo.onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return { results, ...repo }
}
