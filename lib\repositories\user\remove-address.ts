import { useContext } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { editAddress } from '../wellcare-api-urls'

export function removeAddress(options: Ref<any>) {
  const { del } = useWellcareApi()
  const { $toast } = useContext()
  const { execute, loading, timer, response, onSucess, onError } =
    useRepository({
      fetcher: (params) =>
        del({
          url: editAddress(params.id)
        }),
      conditions: options,
      useFetch: false
    })
  onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return { execute, loading, timer, response, onSucess }
}
