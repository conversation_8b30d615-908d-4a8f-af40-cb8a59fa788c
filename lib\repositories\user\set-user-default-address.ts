import { useContext } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { setDefaultAddressUrl } from '../wellcare-api-urls'

export function setDefaultUserAddress(options: Ref<any>) {
  const { put } = useWellcareApi()
  const { $toast } = useContext()
  const { execute, loading, timer, response, onError } = useRepository({
    fetcher: (params) =>
      put({
        url: setDefaultAddressUrl(params._id),
        params
      }),
    conditions: options
  })
  onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return { execute, loading, timer, response }
}
