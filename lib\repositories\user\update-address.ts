import { useContext } from '@nuxtjs/composition-api'
import type { Ref } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { editAddress } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { IUserAddress } from './user.interface'

export function updateAddress(options: Ref<IUserAddress>) {
  const { put } = useWellcareApi()
  const { $toast } = useContext()
  const { execute, loading, timer, response, onSucess, onError } =
    useRepository<IResponse<IUserAddress>>({
      fetcher: (params) =>
        put({
          url: editAddress(params.value._id),
          data: params.value
        }),
      conditions: options,
      useFetch: false,
      manual: true
    })
  onError((e) => {
    $toast.global?.appError({
      message: e.message
    })
  })
  return { execute, loading, timer, response, onSucess }
}
