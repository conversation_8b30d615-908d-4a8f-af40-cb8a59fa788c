import { computed, ref, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'

import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { useUser } from '@wellcare/nuxt-module-account/repositories'
import { IResponse } from '../response.interface'
import { ISearchOption } from '../search-option.interface'
import {
  createUserUrl,
  deleteRelationshipByIdUrl,
  searchUserRelationshipUrl
} from '../wellcare-api-urls'
import { IUser, IUserRelationship } from './user.interface'

export function usePatients(
  options:
    | Ref<ISearchOption<IUserRelationship>>
    | ComputedRef<ISearchOption<IUserRelationship>>
) {
  const { post, del, get } = useWellcareApi()
  const { $toast, $dayjs } = useContext()
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const { renewStore<PERSON>uthen, user, loading: loadingUser } = useUser()

  const patients = ref([])

  const ME = computed<IUserRelationship>(() => ({
    relationship: 'me',
    related: user.value,
    _id: user.value?._id
  }))

  // DECLARE SERVICES
  const {
    execute,
    loading: loadingGetRelationship,
    timer,
    response,
    onSucess
  } = useRepository<IResponse<IUserRelationship[]>>({
    fetcher: (param) =>
      get({
        url: searchUserRelationshipUrl(),
        params: {
          // filter: { user: param.user, relationship: { $ne: 'doctor' } },
          filter: param.filter,
          populate: JSON.stringify([{ path: 'related' }]),
          skip: param.skip || 0,
          limit: param.limit || 100,
          fields: 'relationship,createdAt'
        }
      }),
    conditions: options,
    useFetch: false,
    manual: false,
    toastOnError: true
  })

  const { execute: executeCreatePatient, onSucess: onCreateSuccess } =
    useRepository<IResponse<IUser>>({
      fetcher: (data) =>
        post({
          url: createUserUrl(),
          data
        }),
      useFetch: false,
      manual: true,
      toastOnError: true
    })

  const { execute: executeDelete, onSucess: onDeleteSuccess } = useRepository<
    IResponse<IUserRelationship>
  >({
    fetcher: (param) =>
      del({
        url: deleteRelationshipByIdUrl(param)
      }),
    useFetch: false,
    manual: true,
    toastOnError: true
  })

  // FLOWS
  onSucess((res) => {
    if (res.code !== 200) $toast.error(res.message)
  })

  const getRelationships = async () => {
    const res = await execute().then((res) => res)
    await renewStoreAuthen()

    patients.value = ([ME.value, ...res.results] as IUserRelationship[]).map(
      (i) => {
        if (
          i.createdAt &&
          $dayjs(i.createdAt).isAfter($dayjs().subtract(2, 'hours'))
        ) {
          i.removable = true
        } else i.removable = false
        return i
      }
    )
  }

  onDeleteSuccess((res) => {
    if (res.code !== 200) $toast.error(res.message)
    else getRelationships()
  })

  onCreateSuccess((res) => {
    if (res.code !== 200) $toast.error(res.message)
    else getRelationships()
  })

  const loading = computed(
    () => loadingGetRelationship.value || loadingUser.value
  )

  return {
    execute,
    loading,
    timer,
    response,
    onSucess,
    patients,
    executeDelete,
    executeCreatePatient,
    getRelationships
  }
}
