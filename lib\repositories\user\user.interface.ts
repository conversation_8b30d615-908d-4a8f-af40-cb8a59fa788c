export interface IUser {
  _id: string
  name: string
  dob: string
  gender: 'F' | 'M'
}

export interface IUserAddress {
  _id?: string
  createdAt?: string
  isDefault?: string
  line1: string
  name?: string
  phone?: string
  province: string
  retired?: boolean
  updatedAt?: string
  user?: string
}

export interface IUpdateUserAddress extends Omit<IUserAddress, '_id'> {
  _id: string
}

export interface IUserRelationship {
  _id: string
  user?: string
  relatedTo?: string
  related?: any
  relationship?: 'doctor' | 'child' | 'parent' | 'sibling' | 'spouse' | 'me'
  removable?: boolean
  createdAt?: string
}

export interface IRelatedUser extends IUser, IUserRelationship {}
