import { ref } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import {
  // checkVoucher as checkVoucherUrl,
  promotionValidate as promotionValidateUrl
  // searchPromotion as searchPromotionUrl
} from '../wellcare-api-urls'

export interface IPromotionOption {
  voucher?: string
  promotions?: Array<string>
  user: object
  order: object
}
export function useVoucher(option: any) {
  const voucherError = ref('')
  const { post } = useWellcareApi()
  const {
    execute: checkVoucher,
    onSucess: onCheckVoucherSuccess,
    onError: onCheckVoucherError,
    loading
  } = useRepository({
    fetcher: (params) => {
      if (params) return post({ url: promotionValidateUrl(), data: params })
    },
    conditions: option,
    useFetch: false
  })
  onCheckVoucherSuccess((response: any) => {
    if (![200, 201].includes(response.code)) {
      voucherError.value = response.message
    } else voucherError.value = ''
  })
  onCheckVoucherError((error: any) => {
    voucherError.value = error.response?.data?.message
  })
  return {
    checkVoucher,
    onCheckVoucherSuccess,
    loading,
    voucherError
  }
}
