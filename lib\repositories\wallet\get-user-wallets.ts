import { useStore, computed } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import {
  useRepository,
  useWellcareApi,
  IFetchOption
} from '@wellcare/nuxt-module-data-layer'
import { getUserWalletsUrl } from '../wellcare-api-urls'
import { IResponse } from '../response.interface'
import { IUserWallet } from '../../models/index'
// import { IFetchOption } from '../fetch-option.interface'

export function getUserWallets(
  option: Ref<IUserWallet> | ComputedRef<IUserWallet>,
  _fetchOption?: Ref<IFetchOption> | ComputedRef<IFetchOption>
) {
  const { get } = useWellcareApi()
  const { commit, state } = useStore()
  // eslint-disable-next-line dot-notation
  const wallets = computed(() => state['checkout']['wallets'] || [])
  const {
    onSucess,
    response,
    loading: getUserWalletsLoading,
    execute
  } = useRepository<IResponse<IUserWallet[]>>({
    fetcher: (params) => {
      return get({
        url: getUserWalletsUrl(params.user),
        params: {
          sort: { type: 'card' }
        }
      })
    },
    conditions: option,
    useFetch: true,
    manual: false,
    toastOnError: true
  })
  onSucess((response) => {
    if (response.code === 200 || response.code === 201) {
      const uniqueResults = new Set([...response.results])
      commit('checkout/updateField', {
        path: 'wallets',
        value: Array.from(uniqueResults)
      })
    }
  })
  return { onSucess, response, getUserWalletsLoading, wallets, execute }
}
