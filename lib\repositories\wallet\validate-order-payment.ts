import type { Ref, ComputedRef } from '@nuxtjs/composition-api'
import { useRepository, useWellcareApi } from '@wellcare/nuxt-module-data-layer'
import { IOrder } from '@lib/models/order/props-order'
import { validateOrderPaymentUrl } from '../wellcare-api-urls'

export function useValidateOrderPayment(
  option: Ref<IOrder> | ComputedRef<IOrder>
) {
  const { post } = useWellcareApi()
  const { execute, onSucess, onError } = useRepository({
    fetcher(params) {
      return post({
        url: validateOrderPaymentUrl(),
        data: params
      })
    },
    conditions: option,
    useFetch: false
    // toastOnError: true
  })
  return {
    execute,
    onSucess,
    onError
  }
}
