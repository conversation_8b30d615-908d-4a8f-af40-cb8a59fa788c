// USER MODULE
export const getUserAddressUrl = () => `/user-management/address`
export const createAddressURL = () => `/user-management/address`
export const setDefaultAddressUrl = (id: string) =>
  `/user-management/address/${id}/set-default`
export const editAddress = (id: string) => `/user-management/address/${id}`
export const createUserUrl = () => `/api/user`
export const createUserRelationshipUrl = () => `/api/relationship`
export const searchUserRelationshipUrl = () => `/api/relationship/search`
export const deleteRelationshipByIdUrl = (id: string) =>
  `/api/relationship/${id}`

// FILE
export const createPdfUrl = (id: string, template: string) =>
  `/file-management/pdf/user/${id}/template/${template}/generate`
export const createDoctorSignUrl = (providerName) =>
  `/file-proxy/node-canvas-service/signature/handwritten/svg/base64-text/${providerName}`
export const searchFilesUrl = () => `/file-management/file/search`

// ORDER MODULE
export const addExistedOrderItem = (id: string) =>
  `/api/ecommerce/order-item/${id}/add-existed`
export const updateOrderItemUrl = (id: string) => `/api/order-item/${id}`
export const getOrderItemByIdUrl = (id: string) => `/api/order-item/${id}`
export const createOrderItemUrl = () => `/api/order-item`
export const prepareOrderForProduct = () => `/ecommerce/order/product`
export const prepareSingleOrderForProduct = () =>
  `/ecommerce/order/single/product`
export const prepareMultipleOrderForProduct = () =>
  `/ecommerce/order/multiple/product`

export const searchOrderItemUrl = () => `/api/order-item/search`
export const updateOrderUrl = (id: string) => `/api/order/${id}`
export const getOrderByIdUrl = (id: string) => `/api/order/${id}`
export const searchOrderUrl = () => `/api/order/search`

// PRODUCT MODULE
export const getProductBySlugUrl = (slug: string) =>
  `api/ecommerce/product/slug/${slug}`

// CONSULTATION MODULE
export const getConsultationByIdUrl = (id: string) =>
  `/consultation-server/consultation/${id}`
export const searchPrecriptionUrl = () =>
  `/consultation-server/prescription/search/`
export const searchConsultationUrl = () =>
  '/consultation-server/consultation/search'

// WALLET MODULE
export const getUserWalletsUrl = (user: string) =>
  `/wallet-journal/account/user/${user}/all`
// `/wallet-journal/account/user`
export const validateWalletUrl = () =>
  `/wallet-journal/account/journal/validate`
export const validateOrderPaymentUrl = () =>
  `/wallet-journal/account/order-payment/validate`

// PAYMENT MODULE
export const momoPayment = () => `/payment-gateway/payment/momo`
export const zalopayPayment = () => `/payment-gateway/payment/zalo`
export const payooPayment = () => `/payment-gateway/payment/payoo`

// PROMOTION MODULE
export const promotionValidate = () => `/promotion/order/validate`
export const searchPromotion = () => `/promotion/order/search`
export const convertPointByKeyUrl = (key: string) =>
  `/promotion/policy/key/${key}/voucher/convert`

// VOUCHER MODULE
export const checkVoucher = () => `/api/voucher/v2/check`
export const applyVoucher = () => `/api/voucher/apply`

// PROVIDER MODULE
// export const getProviderTimeslotsUrl = (providerId: string) =>
//   `api/v4.0/provider/${providerId}/schedule/search`
export const getProviderTimeslotsUrl = (providerId: string) =>
  `/calendar/timeslot/${providerId}`
export const getProviderTimeslotsLocationUrl = (
  providerId: string,
  locationId: string
) => `/calendar/timeslot/${providerId}/location/${locationId}`
export const searchProviderUrl = () => `api/provider/search`
export const searchProviderBySlug = (providerSlug: string) =>
  `api/provider/slug/${providerSlug}`
export const searchProviderNotionWebsite = () =>
  '/elastic-read/search/notion-website/_search'

// BPM
export const createWorkflowJobUrl = (workflowKey: string, jobKey: string) =>
  `/bpm/workflow-key/${workflowKey}/job-key/${jobKey}/event/upsert`
export const createCaseUrl = () => '/case/case'

// Observation
export const observationUrl = {
  importCreate: () => `/phr/observation/import/create`
}

// MEMBERSHIP
export const getMembershipSubscriptionsUrl = () =>
  `/membership/member/subscriptions`
// HEALTH - PROGRAMS
export const registerBabyDevelopmentUrl =
  '/health-program/baby-development/register'
export const registerPregnancyDiaryUrl =
  '/health-program/pregnancy-diary/unregister'
// CHAT
export const retriveMessageUrl =
  '/wellchat/general-chat/retrieve-messages-by-ids'
