import { IFlow } from './flow.interface'

export const CONSULTATION_EXPERT_REVIEW_HEALTHGPT: IFlow = {
  key: 'consultation-expert-review-healthgpt',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'request-verify',
      slug: 'request-verify',
      orderItemMeta: [
        {
          key: 'question',
          type: 'string'
        },
        {
          key: 'description',
          type: 'string'
        }
      ]
    },
    {
      step: 2,
      component: 'choose-expert-verify',
      slug: 'choose-expert-verify',
      orderItemMeta: [
        {
          key: 'providerId',
          type: 'string'
        }
      ]
    },
    {
      step: 2,
      component: 'choose-doctor-verify',
      slug: 'choose-doctor-verify',
      orderItemMeta: [
        {
          key: 'question',
          type: 'string'
        },
        {
          key: 'contentId',
          type: 'string'
        }
      ],
      content: {
        title: 'who will confirm this?'
      }
    }
  ]
}
