import { IFlow } from './flow.interface'

export const CONSULTATION_FIRST_MEET: IFlow = {
  key: 'consultation-first-meet',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'prepare-first-meeting',
      slug: 'prepare-first-meeting',
      name: 'prepareFirst<PERSON>eeting',
      orderItemMeta: [
        {
          key: 'medium',
          type: 'string'
        }
      ],
      content: {
        title: 'your first meeting purpose',
        nextTitle: 'prepare medical record'
      }
    },
    {
      step: 2,
      slug: 'prepare-medical-record',
      name: 'prepareMedicalRecord',
      appTitle: 'indepth consultation',
      component: 'prepare-medical-record',
      content: {
        title: 'prepare medical record',
        nextTitle: 'choose an appointment'
      },
      meta: {
        modifiedAbility: true,
        skip: false
      },
      mobileComponent: 'confirm-question',
      orderItemMeta: [
        {
          key: 'reason',
          type: 'string'
        },
        {
          key: 'chiefComplain',
          type: 'string'
        },
        {
          key: 'questions',
          type: 'array'
        }
      ],
      previous: 'choose-type',
      next: null,
      errorMessage: 'please confirm '
    },
    {
      step: 3,
      slug: 'prepare-medical-history',
      name: 'prepareMedicalHistory',
      appTitle: 'medical history',
      component: 'prepare-medical-history',
      content: {
        title: 'personal health record',
        nextTitle: 'request time slot'
      },
      mobileComponent: 'confirm-question',
      orderItemMeta: [
        {
          key: 'reason',
          type: 'string'
        },
        {
          key: 'chiefComplain',
          type: 'string'
        },
        {
          key: 'questions',
          type: 'array'
        }
      ],
      previous: 'prepare-medical-record',
      next: null
    },
    {
      step: 4,
      slug: 'choose-request-time',
      name: 'chooseRequestTime',
      component: 'choose-request-time',
      content: {
        title: 'request time slot'
      },
      previous: 'prepare-medical-history',
      next: null
    }
  ]
}
