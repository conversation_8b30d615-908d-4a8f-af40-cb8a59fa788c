import { IFlow } from './flow.interface'

export const CONSULTATION_INDEPTH: IFlow = {
  key: 'consultation-indepth',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      component: 'ChoosePatient',
      slug: 'choose-patient',
      orderItemMeta: [
        {
          key: 'patientId',
          type: 'string'
        }
      ]
    }
  ]
}
