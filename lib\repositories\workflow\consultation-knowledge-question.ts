import { IFlow } from './flow.interface'

export const CONSULTATION_KNOWLEDGE_QUESTION: IFlow = {
  key: 'consultation-knowledge-question',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'knowledge-question',
      name: 'knowledge-question',
      slug: 'knowledge-question',
      appTitle: 'knowledge question',
      orderItemMeta: [
        {
          key: 'chiefComplaint',
          type: 'string'
        }
      ],
      content: {
        title: 'your question',
        nextTitle: 'choose your option',
        description: 'knowledge question'
      }
    },
    {
      step: 2,
      component: 'choose-option',
      name: 'choose-option',
      slug: 'choose-option',
      appTitle: 'choose option',
      orderItemMeta: [
        {
          key: 'isExpertReview',
          type: 'string'
        }
      ],
      content: {
        title: 'choose your option',
        nextTitle: 'your physician',
        description: 'choose option'
      }
    },
    {
      step: 3,
      component: 'choose-knowledge-provider',
      name: 'choose-knowledge-provider',
      slug: 'choose-knowledge-provider',
      appTitle: 'choose knowledge provider',
      orderItemMeta: [
        {
          key: 'product',
          type: 'string'
        }
      ],
      content: {
        title: 'your physician',
        nextTitle: 'payment overview',
        description: 'choose knowledge provider'
      },
      previous: 'choose-option',
      next: null,
      isFinal: true,
      errorMessage: 'please confirm'
    }
  ]
}
