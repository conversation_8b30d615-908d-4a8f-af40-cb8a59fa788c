import { IFlow } from './flow.interface'

export const CONSULTATION_QUESTION_PROVIDER: IFlow = {
  key: 'consultation-question-provider',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-patient',
      slug: 'register-patient',
      name: 'registerPatient',
      orderItemMeta: [
        {
          key: 'patient',
          type: 'string'
        }
      ],
      content: {
        title: 'whom is this consultation for?',
        nextTitle: 'question'
      }
    },
    {
      step: 2,
      component: 'question',
      name: 'short question',
      slug: 'question',
      appTitle: 'short question',
      orderItemMeta: [
        {
          key: 'chiefComplaint',
          type: 'string'
        }
      ],
      content: {
        title: 'your question?',
        nextTitle: 'choose provider',
        description: 'question'
      }
    },
    {
      step: 3,
      slug: 'confirm-question',
      name: 'confirmQuestion',
      appTitle: 'short question',
      component: 'confirm-question',
      content: {
        title: 'confirmation',
        nextTitle: 'payment overview',
        description: 'Nên đặt câu hỏi ngắn trong những trường hợp nào?'
      },
      mobileComponent: 'confirm-question',
      orderItemMeta: [
        {
          key: 'confirmType',
          type: 'string'
        }
      ],
      previous: 'choose-provider',
      next: null,
      isFinal: true,
      errorMessage: 'please confirm '
    }
  ]
}
