import { IFlow } from './flow.interface'

export const CONSULTATION_THERAPY: IFlow = {
  key: 'consultation-therapy',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-medium',
      slug: 'choose-medium',
      name: 'chooseMedium',
      orderItemMeta: [
        {
          key: 'medium',
          type: 'string'
        }
      ],
      content: {
        title: 'choose type of communication',
        nextTitle: 'choose an appointment'
      }
    },
    {
      step: 2,
      component: 'choose-language',
      slug: 'choose-language',
      name: 'chooseLanguage',
      content: {
        title: 'choose language'
      }
    },
    {
      step: 3,
      component: 'choose-patient',
      name: 'choosePatient',
      slug: 'choose-patient',
      appTitle: 'indepth consultation',
      orderItemMeta: [
        {
          key: 'patient',
          type: 'string'
        }
      ],
      content: {
        title: 'whom is this consultation for?',
        nextTitle: 'prepare medical record'
      }
    },
    {
      step: 4,
      slug: 'prepare-medical-record',
      name: 'prepareMedicalRecord',
      appTitle: 'indepth consultation',
      component: 'prepare-medical-record',
      content: {
        title: 'prepare medical record',
        nextTitle: 'choose an appointment'
      },
      mobileComponent: 'confirm-question',
      orderItemMeta: [
        {
          key: 'reason',
          type: 'string'
        },
        {
          key: 'chiefComplain',
          type: 'string'
        },
        {
          key: 'questions',
          type: 'array'
        }
      ],
      previous: 'choose-provider',
      next: null,
      errorMessage: 'please confirm '
    },
    {
      step: 5,
      slug: 'choose-provider-timeslot',
      name: 'chooseProviderTimeSlot',
      appTitle: 'indepth consultation',
      component: 'choose-provider-timeslot',
      content: {
        title: 'choose an appointment',
        nextTitle: 'overview payment'
      },
      mobileComponent: 'confirm-question',
      orderItemMeta: [
        {
          key: 'from',
          type: 'string'
        },
        {
          key: 'to',
          type: 'string'
        }
      ],
      previous: 'choose-provider',
      next: null,
      errorMessage: 'please confirm '
    }
  ]
}
