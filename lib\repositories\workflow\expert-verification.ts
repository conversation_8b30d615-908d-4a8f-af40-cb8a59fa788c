import { IFlow } from './flow.interface'

export const EXPERT_VERIFICATION: IFlow = {
  key: 'expert-verification',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    // {
    //   step: 1,
    //   component: 'choose-patient',
    //   name: 'choosePatient',
    //   slug: 'choose-patient',
    //   appTitle: 'indepth consultation',
    //   orderItemMeta: [
    //     {
    //       key: 'patient',
    //       type: 'string'
    //     }
    //   ],
    //   content: {
    //     title: 'whom is this consultation for?',
    //     nextTitle: 'prepare medical record'
    //   }
    // },
    {
      step: 1,
      component: 'choose-provider',
      name: 'chooseProvider',
      slug: 'choose-provider',
      appTitle: 'short question',
      orderItemMeta: [
        {
          key: 'provider',
          type: 'string'
        }
      ],
      content: {
        title: 'choose a doctor',
        nextTitle: 'confirm question',
        description: 'choose-provider'
      }
    },
    {
      step: 2,
      component: 'confirm-verification',
      name: 'confirmVerification',
      slug: 'confirm-verfication',
      appTitle: 'short question',
      orderItemMeta: [
        {
          key: 'provider',
          type: 'string'
        }
      ],
      content: {
        title: 'confirm',
        nextTitle: 'confirm',
        description: 'choose-provider'
      }
    }
  ]
}
