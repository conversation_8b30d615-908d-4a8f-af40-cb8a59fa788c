interface IOrderItemMeta {
  key: string
  type: 'string' | 'number' | 'object' | 'array'
}
interface IContent {
  title: string
  subtitle?: string
  nextTitle?: string
  description?: string
}
interface IStepMeta {
  modifiedAbility: boolean
  skip?: boolean
}
export interface IStep {
  // index of step
  step?: number
  // slpug of step
  slug?: string
  name?: string
  appTitle?: string
  component: string
  meta?: IStepMeta
  // component for mobile verion
  mobileComponent?: string
  content?: IContent
  orderItemMeta?: IOrderItemMeta[]
  previous?: string
  next?: string
  isFinal?: boolean
  errorMessage?: string
  state?: boolean
  showStepper?: boolean
}

type TopupType =
  | 'momo'
  | 'zalopay'
  | 'bank-transfer'
  | 'cash'
  | 'debit-card'
  | 'credit-card'

interface IPayment {
  voucher: {
    enabled: boolean
    policyRegex?: string
  }
  allowUserCredit: boolean
  topupSources: TopupType[]
  wallet?: {
    disabled?: boolean
    match?: string
  }
}

export interface IFlow {
  key: string
  steps: IStep[]
  payment?: IPayment
  bpmKey: string
}
