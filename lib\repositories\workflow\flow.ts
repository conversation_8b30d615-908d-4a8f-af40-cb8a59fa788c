import { APPOINTMENT_GENERAL } from './appointment-general'
import { CONSULTATION_EXPERT_REVIEW_HEALTHGPT } from './consultation-expert-review-healthgpt'
import { CONSULTATION_FIRST_MEET } from './consultation-first-meet'
import { CONSULTATION_INDEPTH } from './consultation-indepth'
import { CONSULTATION_KNOWLEDGE_QUESTION } from './consultation-knowledge-question'
import { CONSULTATION_PSYCHOLOGY } from './consultation-psychology'
import { CONSULTATION_QUESTION } from './consultation-question'
import { CONSULTATION_QUESTION_PROVIDER } from './consultation-question-provider'
import { CONSULTATION_THERAPY } from './consultation-therapy'
import { IFlow } from './flow.interface'
import { HEALTHPROGRAM_BABY_DEVELOPMENT } from './healthprogram-baby-development'
import { MEMBERSHIP } from './membership'
import { MEMBERSHIP_ADDON } from './membership-addon'
import { MEMBERSHIP_CONCIERGE } from './membership-concierge'
import { MEMBERSHIP_PERSONAL_DOCTOR } from './membership-personal-doctor'
import { MEMBERSHIP_PERSONAL_DOCTOR_PROVIDER } from './membership-personal-doctor-provider'
import { PREGNANCY_DIARY } from './pregnancy-diary'
import { PRESCRIPTION_DELIVERY } from './prescripstion-delivery'
import { EXPERT_VERIFICATION } from './expert-verification'

export const flows: IFlow[] = [
  APPOINTMENT_GENERAL,
  CONSULTATION_EXPERT_REVIEW_HEALTHGPT,
  CONSULTATION_FIRST_MEET,
  CONSULTATION_INDEPTH,
  CONSULTATION_KNOWLEDGE_QUESTION,
  CONSULTATION_QUESTION,
  CONSULTATION_QUESTION_PROVIDER,
  CONSULTATION_THERAPY,
  MEMBERSHIP,
  MEMBERSHIP_ADDON,
  MEMBERSHIP_CONCIERGE,
  PREGNANCY_DIARY,
  PRESCRIPTION_DELIVERY,
  CONSULTATION_PSYCHOLOGY,
  HEALTHPROGRAM_BABY_DEVELOPMENT,
  MEMBERSHIP_PERSONAL_DOCTOR,
  MEMBERSHIP_PERSONAL_DOCTOR_PROVIDER,
  EXPERT_VERIFICATION
]
