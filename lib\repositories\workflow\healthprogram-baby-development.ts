import { IFlow } from './flow.interface'

export const HEALTHPROGRAM_BABY_DEVELOPMENT: IFlow = {
  key: 'healthprogram-baby-development',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-baby',
      name: 'chooseBaby',
      slug: 'choose-baby',
      appTitle: 'indepth consultation',
      orderItemMeta: [
        {
          key: 'baby',
          type: 'string'
        }
      ],
      content: {
        title: 'choose baby'
      }
    },
    {
      step: 2,
      component: 'body-index',
      slug: 'body-index',
      name: 'bodyIndex',
      content: {
        title: 'body index'
      }
    },
    {
      step: 3,
      component: 'choose-packages',
      slug: 'choose-packages',
      name: 'choosePackages',
      content: {
        title: 'choose packages',
        nextTitle: 'choose personal doctor',
        description: 'choose packages'
      }
    },
    // {
    //   step: 4,
    //   component: 'choose-personal-doctor',
    //   name: 'chooseAddonsPersonalDoctor',
    //   slug: 'addons-personal-doctor',
    //   content: {
    //     title: 'choose personal doctor'
    //   }
    // }
    {
      step: 4,
      component: 'addons-personal-doctor',
      name: 'addonsPersonalDoctor',
      slug: 'addons-personal-doctor',
      content: {
        title: 'choose personal doctor'
      }
    }
  ]
}
