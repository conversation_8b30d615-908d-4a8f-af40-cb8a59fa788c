import { IFlow } from './flow.interface'

export const MEMBERSHIP_ADDON: IFlow = {
  key: 'membership-addon',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'membership-addon',
      slug: 'membership-addon',
      name: 'membership-addon',
      content: {
        title: 'Membership Addon'
      },
      orderItemMeta: [
        {
          key: 'addon-membership',
          type: 'string'
        }
      ]
    }
  ]
}
