import { IFlow } from './flow.interface'

export const MEMBERSHIP_CONCIERGE: IFlow = {
  key: 'membership-concierge',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'register-member',
      slug: 'register-member',
      name: 'registerM<PERSON>ber',
      content: {
        title: 'Choose member',
        subtitle: 'Choose your own doctor for your loved one'
      }
    },
    {
      step: 2,
      component: 'register-private-provider',
      slug: 'register-private-provider',
      name: 'registerPrivateProvider',
      content: {
        title: 'Choose private provider'
      }
    }
  ]
}
