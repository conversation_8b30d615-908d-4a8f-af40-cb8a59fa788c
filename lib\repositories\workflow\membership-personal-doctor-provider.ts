import { IFlow } from './flow.interface'
//  test: /checkout/personal-doctor?patientId=6662ad97ebfba8081f4fb256

export const MEMBERSHIP_PERSONAL_DOCTOR_PROVIDER: IFlow = {
  key: 'membership-personal-doctor-provider',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-patient-personal-doctor',
      slug: 'register-patient',
      name: 'registerPatient',
      orderItemMeta: [
        {
          key: 'patient',
          type: 'string'
        }
      ],
      content: {
        title: 'Select Member',
        subtitle: 'program applies to children under 16 years old',
        nextTitle: 'Your Personal Doctor'
      }
    },
    {
      step: 2,
      component: 'personal-doctor-package-confirm',
      name: 'PersonalDoctorPackageConfirm',
      slug: 'personal-doctor-package-confirm',
      content: {
        title: 'Your Personal Doctor'
      }
    }
  ]
}
