import { IFlow } from './flow.interface'
//  test: /checkout/personal-doctor?patientId=6662ad97ebfba8081f4fb256

export const MEMBERSHIP_PERSONAL_DOCTOR: IFlow = {
  key: 'membership-personal-doctor',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-patient-personal-doctor',
      slug: 'register-patient',
      name: 'registerPatient',
      orderItemMeta: [
        {
          key: 'patient',
          type: 'string'
        }
      ],
      content: {
        title: 'Select Member',
        nextTitle: 'Your Personal Doctor'
      }
    },
    // {
    //   step: 2,
    //   component: 'choose-personal-doctor',
    //   name: 'choosePersonalDoctor',
    //   slug: 'choose-personal-doctor',
    //   content: {
    //     title: 'Choose Personal Doctor'
    //   }
    // },
    {
      step: 2,
      component: 'personal-doctor-package-confirm',
      name: 'personalDoctorPackageConfirm',
      slug: 'personal-doctor-package-confirm',
      content: {
        title: 'Your Personal Doctor'
      }
    }
  ]
}
