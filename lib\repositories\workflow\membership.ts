import { IFlow } from './flow.interface'

export const MEMBERSHIP: IFlow = {
  key: 'membership',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'choose-membership',
      slug: 'choose-membership',
      name: 'chooseMembership',
      content: {
        title: 'choose membership package'
      },
      orderItemMeta: [
        {
          key: 'membership',
          type: 'string'
        }
      ]
    },
    {
      step: 2,
      component: 'membership-choose-patient-personal-doctor',
      slug: 'register-patient',
      name: 'registerPatient',
      orderItemMeta: [
        {
          key: 'patient',
          type: 'string'
        }
      ],
      content: {
        title: 'Select Member',
        subtitle: 'program applies to children under 16 years old',
        nextTitle: 'Your Personal Doctor'
      }
    },
    {
      step: 3,
      component: 'choose-personal-doctor',
      name: 'choosePersonalDoctor',
      slug: 'choose-personal-doctor',
      content: {
        title: 'choose personal doctor'
      }
    }
  ]
}
