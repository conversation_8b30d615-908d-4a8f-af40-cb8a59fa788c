import { IFlow } from './flow.interface'

export const PREGNANCY_DIARY: IFlow = {
  key: 'pregnancy-diary',
  bpmKey: 'checkout',
  payment: {
    allowUserCredit: true,
    topupSources: [
      'bank-transfer',
      'cash',
      'credit-card',
      'debit-card',
      'momo',
      'zalopay'
    ],
    voucher: {
      enabled: true
    }
  },
  steps: [
    {
      step: 1,
      component: 'pregnancy-diary',
      slug: 'pregnancy-diary',
      name: 'pregnancy-diary',
      content: {
        title: 'Pregnancy Diary'
      },
      orderItemMeta: [
        {
          key: 'pregnancy-diary',
          type: 'string'
        }
      ]
    }
  ]
}
