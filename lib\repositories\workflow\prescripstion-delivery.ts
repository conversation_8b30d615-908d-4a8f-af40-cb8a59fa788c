import { IFlow } from './flow.interface'

export const PRESCRIPTION_DELIVERY: IFlow = {
  key: 'prescription-delivery',
  bpmKey: '',
  steps: [
    // {
    //   step: 1,
    //   slug: 'medicine-fullfillment-type',
    //   name: 'medicineFullfillmentType',
    //   appTitle: '',
    //   component: 'medicine-fullfillment-type',
    //   mobileComponent: 'medicine-fullfillment-type',
    //   state: false,
    //   content: {
    //     title: 'where do you get the medicine?',
    //     nextTitle: 'address',
    //     description: 'medicine pick up location'
    //   },
    //   orderItemMeta: [
    //     {
    //       key: 'consultation',
    //       type: 'string'
    //     },
    //     {
    //       key: 'deliveryType',
    //       type: 'string'
    //     },
    //     {
    //       key: 'orgName',
    //       type: 'string'
    //     }
    //   ],
    //   previous: null,
    //   next: 'address',
    //   isFinal: false,
    //   errorMessage: 'Please choose a delivery type and pharmacy'
    // },
    {
      step: 1,
      slug: 'address',
      name: 'Address',
      appTitle: '',
      component: 'choose-address',
      mobileComponent: 'address',
      content: {
        title: 'fill in information',
        nextTitle: 'medicine-pending-delivery',
        description: 'delivery address information'
      },
      orderItemMeta: [
        {
          key: 'phone',
          type: 'string'
        },
        {
          key: 'name',
          type: 'string'
        },
        {
          key: 'address',
          type: 'object'
        }
      ],
      previous: 'medicine-fullfillment-type',
      next: 'medicine-pending-delivery',
      isFinal: false,
      errorMessage: 'Please enter your receiver info and shipping address'
    },
    {
      step: 2,
      slug: 'medicine-pending-delivery',
      name: 'medicinePendingDelivery',
      appTitle: '',
      component: 'medicine-pending-delivery',
      mobileComponent: 'medicine-pending-delivery',
      content: {
        title: 'medicine pending',
        description: "patient's prescription"
      },
      orderItemMeta: [
        {
          key: 'attachment',
          type: 'object'
        }
      ],
      previous: 'address',
      showStepper: false,
      isFinal: true,
      errorMessage: 'Please enter your posted for delivery'
    }
  ]
}
