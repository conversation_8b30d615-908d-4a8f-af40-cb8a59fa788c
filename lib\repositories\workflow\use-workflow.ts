import { computed, useContext } from '@nuxtjs/composition-api'
import type { ComputedRef, Ref } from '@nuxtjs/composition-api'
import { flows } from './flow'

interface option {
  key: string
}

export function useWorkflow(option: Ref<option> | ComputedRef<option>) {
  const { $config } = useContext()

  const workflow = computed(() => {
    const flow = flows.find((f) => f.key === option.value.key)

    // ======= [ALERT] THIS CODE ONLY MODIFY FOR AIA REQUIREMENT ! [ALERT] ===========
    // should not make effect to main product (checkout-wellcare)
    // should be improved when we have time
    if (
      $config.checkout.forWellcare === false &&
      $config.checkout.agent === 'aia'
    ) {
      // find index of step choose patient in flow
      const index = flow.steps.findIndex(
        (i) => i.component === 'choose-patient'
      )
      if (index !== -1) {
        // change order of next step
        if (index + 1 < flow.steps.length)
          flow.steps[index + 1].step = flow.steps[index].step
        // remove step choose patient in flow
        flow.steps.splice(index, 1)
      }
    }
    // ===============================================================================
    return flow
  })

  return { workflow }
}
