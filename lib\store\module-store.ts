import { actionTree, getterTree, mutationTree } from 'typed-vuex'
import { getField, updateField } from 'vuex-composition-map-fields'

export const strict = false

export const namespaced = true

export const state = () => ({
  mainProductName: '',
  order: {
    delivery: {
      to: {}
    },
    meta: {}
  },
  // mainOrderItem: {
  //   product: {},
  //   meta: {}
  // },
  // orderItem: {
  //   product: {},
  //   meta: {}
  // },
  orderItems: [],
  buttonNextState: {
    isDisabled: false
  },
  stepperValidDated: {
    medicalRecord: {
      summarize: '',
      data: {}
    },
    vaccination: {
      summarize: '',
      data: null
    },
    bodyIndex: {
      summarize: '',
      data: {}
    },
    newborn: {
      summarize: '',
      data: {}
    },
    present: {
      summarize: '',
      data: {}
    }
  },
  pregnancy: {
    dueDate: '',
    isPregnancy: false,
    packages: []
  }
})

export const getters = getterTree(state, {
  getField
})

export const mutations = mutationTree(state, {
  updateField,
  SET_ORDER_ITEMS(state, val: any) {
    state.orderItems = val
  },
  SET_ORDER_ITEM(state, orderItem) {
    const orderItems = state.orderItems
    // // reset array to computed can watch
    state.orderItems = []
    // if orderItem is not existed, add to list; else update request
    const index = orderItems.findIndex((i) => i._id === orderItem._id)
    if (index >= 0) orderItems[index] = orderItem
    else orderItems.push(orderItem)
    state.orderItems = orderItems
  },
  REMOVE_ORDER_ITEM(state, orderItem) {
    state.orderItems = state.orderItems.filter(
      (item) => item._id !== orderItem._id
    )
  },
  // SET_ORDER_ITEM(state, val: any) {
  //   state.orderItem = val
  // },
  SET_PRODUCT_ORDER_ITEM(state, product) {
    const data = state.orderItems
    state.orderItems = []
    for (let i = 0; i < data.length; i++) {
      if (data[i]?.product?._id === product._id) {
        data[i].product = {
          ...data[i].product,
          ...product
        }
      }
    }
    state.orderItems = data
    // state.orderItems.forEach((e, i) => {
    //   if (e?.product?._id === product._id) {
    //     state.orderItems[i].product = product
    //   }
    // })
  },
  // SET_MAIN_ORDER_ITEM(state, val: any) {
  //   state.mainOrderItem = val
  // },
  SET_MAIN_PRODUCT_NAME(state, val: string) {
    state.mainProductName = val
  },
  SET_PREGNANCY(state, val: boolean) {
    state.pregnancy.isPregnancy = val
  },
  SET_PREGNANCY_DUE_DATE(state, val: string) {
    state.pregnancy.dueDate = val
  },
  SET_PREGNANCY_PACKAGES(state, val: any[]) {
    state.pregnancy.packages = val
  },
  SET_SUMMARIZE_MEDICAL_RECORD(state, val: string) {
    state.stepperValidDated.medicalRecord.summarize = val
  },
  SET_SUMMARIZE_BODY_INDEX(state, val: string) {
    state.stepperValidDated.bodyIndex.summarize = val
  },
  SET_SUMMARIZE_VACCINATION(state, val: string) {
    state.stepperValidDated.vaccination.summarize = val
  },
  SET_DATA_VACCINATION(state, val: any) {
    state.stepperValidDated.vaccination.data = val
  },
  SET_BUTTON_NEXT_STATE_DISABLE(state, val: boolean) {
    state.buttonNextState.isDisabled = val
  },
  SET_SUMMARIZE_NEWBORN(state, val: string) {
    state.stepperValidDated.newborn.summarize = val
  },
  SET_SUMMARIZE_PRESENT(state, val: string) {
    state.stepperValidDated.present.summarize = val
  }
})

export const actions = actionTree({ state, getters, mutations }, {})
