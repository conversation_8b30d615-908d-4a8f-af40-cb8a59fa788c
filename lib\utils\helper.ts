export const CONVERT = {
  height: {
    name: 'Person Height',
    unit: 'cm'
  },
  weight: {
    name: 'Person Weight',
    unit: 'kg'
  },
  headCircumference: {
    name: 'Person Head Circumference',
    unit: 'cm'
  }
}

export const calcConvertUnitValue = ({
  unit,
  value
}: {
  unit: 'm' | 'cm' | 'km'
  value: number | string
}): number => {
  const units = {
    m: 100,
    M: 100
  }

  return units[unit] ? Number(value) * units[unit] : Number(value)
}
