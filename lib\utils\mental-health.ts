export const myself = {
  reason: {
    vi: 'Điều gì khiến bạn tìm tới trị liệu?',
    en: 'What make you look for a therapist?',
    type: 'radio',
    data: [
      {
        vi: 'Tôi cảm thấy buồn chán',
        en: 'I’ve been feeling depressed'
      },
      {
        vi: 'Tôi cảm thấy lo lắng hoặc quá sức chịu đựng',
        en: 'I feel axious or overwhelmed'
      },
      {
        vi: 'Tâm trạng của tôi không ổn, gây ảnh hưởng đến kết quả học tập/công việc hàng ngày',
        en: 'My mood is interfering with my job/school performance'
      },
      {
        vi: 'Tôi gặp khó khăn trong việc xây dựng hoặc vun đắp các mối quan hệ',
        en: 'I struggle with building or maintaining relationships'
      },
      {
        vi: 'Tôi không tìm thấy mục đích sống và ý nghĩa cuộc đời sống mình',
        en: "I can't find purpose and meaning in my life"
      },
      {
        vi: 'Tôi đang đau khổ',
        en: 'I am grieving'
      },
      {
        vi: 'Tôi bị sang chấn tâm lý',
        en: 'I have experienced trauma'
      },
      {
        vi: 'Tôi muốn tham vấn về một khó khăn cụ thể của mình',
        en: 'I need to talk through a specific challenge'
      },
      {
        vi: 'Tôi muốn tự tin vào giá trị của bản thân',
        en: 'I want to gain self-confidence'
      },
      {
        vi: 'Tôi muốn cải thiện bản thân nhưng không biết bắt đầu từ đâu',
        en: 'I want to improve myself but don’t know where to start'
      },
      {
        vi: 'Tôi được bác sĩ hoặc bạn bè khuyên nên trị liệu',
        en: 'Recommended by doctors or friends'
      },
      {
        vi: 'Tôi chỉ đang khám phá bản thân',
        en: 'Just exploring'
      },
      {
        vi: 'Những vấn đề khác',
        en: 'Others'
      }
    ]
  },
  expectations: [
    {
      vi: 'Bạn mong muốn nhà trị liệu sẽ:',
      en: 'What are your expectations from your therapist? A therapist who…',
      type: 'checkbox',
      data: [
        {
          vi: 'Lắng nghe',
          en: 'Listen'
        },
        {
          vi: 'Tâm sự về quá khứ',
          en: 'Explore my past'
        },
        {
          vi: 'Luyện cho tôi các kỹ năng mới',
          en: 'Teach me new skills'
        },
        {
          vi: 'Xác định lại các niềm tin của tôi',
          en: 'Challenge my beliefs'
        },
        {
          vi: 'Giao bài tập cho tôi',
          en: 'Assign me homeworks'
        },
        {
          vi: 'Hướng dẫn tôi đặt ra các mục tiêu',
          en: 'Guide me to set goals'
        },
        {
          vi: 'Chủ động hỏi và khai thác thông tin',
          en: 'Proactively checks in with me'
        },
        {
          vi: 'Điều gì đó khác',
          en: 'Other'
        },
        {
          vi: 'Tôi chưa có mong muốn cụ thể',
          en: 'I don’t know'
        }
      ]
    }
  ]
}

export const couples = {
  reason: {
    vi: 'Điều gì khiến bạn tìm tới trị liệu?',
    en: 'What led you to consider therapy today?',
    type: 'radio',
    data: [
      {
        vi: 'Cân nhắc về việc chia tay',
        en: 'Decide whether we should separate'
      },
      {
        vi: 'Giải quyết mâu thuẫn và bất đồng quan điểm',
        en: 'Resolve conflicts and disagreements'
      },
      {
        vi: 'Vấn đề quan hệ ngoài luồng',
        en: 'Overcome adultery'
      },
      {
        vi: 'Thấu hiểu bản thân',
        en: 'Understand myself better'
      },
      {
        vi: 'Thấu hiểu đối phương',
        en: 'Understand my partner better'
      },
      {
        vi: 'Phân công trách nhiệm',
        en: 'Get to a more fair workload'
      },
      {
        vi: 'Giảm áp lực',
        en: 'Reduce tension'
      },
      {
        vi: 'Cứu vãn tình cảm hoặc hôn nhân',
        en: 'Prevent separation or divorce'
      },
      {
        vi: 'Học cách tranh luận tích cực',
        en: 'Learn "good" ways to fight'
      },
      {
        vi: 'Ngưng làm tổn thương nhau',
        en: 'Stop hurting each other'
      },
      {
        vi: 'Lấy lại tình yêu',
        en: "Win back my partner's love"
      },
      {
        vi: 'Yêu lại người ấy',
        en: 'Love my partner again'
      },
      {
        vi: 'Thảo luận các vấn đề về nuôi dạy con cái',
        en: 'Discuss issues around raising kids'
      },
      {
        vi: 'Cải thiện hoạt động tình dục và thân mật',
        en: 'Improve our sex and intimacy'
      },
      {
        vi: 'Thỏa thuận hoặc hòa giải Ly dị hoặc ly thân',
        en: 'Divorce or separation mediation'
      },
      {
        vi: 'Điều gì đó khác',
        en: 'Others'
      }
    ]
  },
  expectations: [
    {
      vi: 'Bạn mong muốn nhà trị liệu sẽ',
      en: 'What are your expectations from your therapist? A therapist who…',
      type: 'checkbox',
      data: [
        {
          vi: 'Lắng nghe',
          en: 'Listen'
        },
        {
          vi: 'Tìm hiểu bản chất và các góc độ khác nhau của mối quan hệ',
          en: 'Explores relationship dynamics'
        },
        {
          vi: 'Hướng dẫn các kỹ năng giao tiếp',
          en: 'Teaches new communication skills'
        },
        {
          vi: 'Hòa giải các vấn đề mẫu thuẫn',
          en: 'Mediate in difficult situations'
        },
        {
          vi: 'Tìm hiểu các tác động từ phía gia đình hai bên',
          en: 'Explores the impacts of families of origin'
        },
        {
          vi: 'Giao bài tập cho tôi',
          en: 'Assigns homework'
        },
        {
          vi: 'Hướng dẫn tôi đặt ra các mục tiêu',
          en: 'Guides to set goals'
        },
        {
          vi: 'Chủ động khai thác thông tin',
          en: 'Proactively checks in'
        },
        {
          vi: 'Điều gì đó khác',
          en: 'Others'
        },
        {
          vi: 'Mục tiêu chưa rõ',
          en: 'I don’t know'
        }
      ]
    }
  ]
}

export const teenager = {
  reason: {
    vi: 'Vấn đề nào của trẻ làm bạn lo lắng?',
    en: 'Do you have concerns about your child in any of these issues?',
    type: 'radio',
    data: [
      {
        vi: 'Mối quan hệ với gia đình',
        en: 'Relationship with family'
      },
      {
        vi: 'Mối quan hệ với bạn đồng trang lứa',
        en: 'Relationship with peers'
      },
      {
        vi: 'Khả năng thích nghi với thay đổi trong đời sống',
        en: 'Coping with life changes'
      },
      {
        vi: 'Tăng động giảm chú ý (ADD, ADHD)',
        en: 'ADHD /ADD'
      },
      {
        vi: 'Khó khăn học đường',
        en: 'School challenges'
      },
      {
        vi: 'Hành vi phạm tội',
        en: 'Criminal behavior'
      },
      {
        vi: 'Sử dụng chất kích thích',
        en: 'Substance abuse'
      },
      {
        vi: 'Các vấn đề liên quan đến giới tính và tình dục',
        en: 'Sexuality-related issues'
      }
    ]
  },
  expectations: [
    {
      vi: 'Bạn có muốn trẻ cùng tham gia tư vấn?',
      en: 'Would you like to invite your child?',
      type: 'radio',
      data: [
        {
          vi: 'Có (Tôi muốn trẻ được trị liệu)',
          en: 'Yes (I want therapy for my child)'
        },
        {
          vi: 'Không (Tôi chỉ muốn xin lời khuyên cho cha mẹ)',
          en: 'No (I only need parental advice)'
        },
        {
          vi: 'Tôi chưa biết (sẽ quyết định sau)',
          en: 'Not sure yet (decide later)'
        }
      ]
    },
    {
      vi: 'Bạn là gì của trẻ?',
      en: 'How are you related to your child?',
      type: 'radio',
      data: [
        {
          vi: 'Tôi là mẹ',
          en: 'I am the mother'
        },
        {
          vi: 'Tôi là ba',
          en: 'I am the father'
        },
        {
          vi: 'Tôi là người giám hộ',
          en: 'I am the legal guardian'
        },
        {
          vi: 'Khác',
          en: 'Other'
        }
      ]
    }
  ]
}

export const mentalHealth = {
  myself,
  couples,
  teenager
}
