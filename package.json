{"name": "@wellcare/nuxt-module-checkout", "version": "0.1.1", "description": "<PERSON><PERSON>t Module Checkout", "homepage": "https://github.com/wellcare/nuxt-module-checkout#readme", "repository": {"type": "module", "url": "git+https://github.com/wellcare/nuxt-module-checkout.git", "directory": "packages/nuxt-module-checkout"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "license": "UNLICENSED", "author": "Wellcare", "scripts": {"build:example": "nuxt build example --modern", "build:package": "rimraf ./dist && tsc && node build.js", "release:local": "rimraf ./dist && tsc && npm version patch -f && node build.js && cd dist/ && npm publish", "build:release": "rimraf ./dist && tsc && node build.js", "publish:package": "cd dist/ && npm publish", "dev": "cross-env NODE_ENV=development npm run lint && nuxt example", "generate": "nuxt generate --modern", "lint": "eslint --ext .ts,.vue --fix . ./example ./lib", "start": "nuxt start example", "test": "npm run test:unit", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:unit": "jest ./tests/unit", "coverage": "codecov"}, "dependencies": {"@capacitor/app": "^5.0.6", "@capacitor/browser": "^5.1.0", "@capacitor/clipboard": "^5.0.6", "@capacitor/core": "^5.3.0", "@capacitor/preferences": "^5.0.6", "@capawesome-team/capacitor-datetime-picker": "^5.0.0", "@nuxtjs/gtm": "^2.4.0", "@vuelidate/core": "2.0.0", "@vuelidate/validators": "2.0.0", "@wellcare/capacitor-permissions": "^0.0.4", "@wellcare/nuxt-module-notion": "^0.0.34-beta2", "capacitor-native-settings": "^5.0.0", "dompurify": "^3.0.2", "markdown-it": "^14.1.0", "numeral": "^2.0.6", "swiper": "^8.0.6", "vue-class-component": "^7.2.6", "vue-dompurify-html": "2.5.2", "vue-property-decorator": "^9.1.2", "vue-the-mask": "^0.11.1"}, "devDependencies": {"@babel/core": "^7.13.10", "@babel/preset-env": "^7.13.10", "@capacitor/preferences": "^5.0.6", "@mdi/js": "^6.6.96", "@nuxt/types": "^2.15.3", "@nuxt/typescript-build": "^2.1.0", "@nuxt/typescript-runtime": "^2.1.0", "@nuxt/utils": "^2.15.8", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/composition-api": "^0.32.0", "@nuxtjs/dayjs": "^1.4.1", "@nuxtjs/eslint-config-typescript": "6.0.1", "@nuxtjs/eslint-module": "3.0.2", "@nuxtjs/google-fonts": "^2.0.0", "@nuxtjs/i18n": "^7.2.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/stylelint-module": "^4.0.0", "@nuxtjs/toast": "^3.3.1", "@nuxtjs/vuetify": "^1.12.3", "@playwright/test": "^1.42.1", "@tanstack/vue-query": "4.27.0", "@types/dompurify": "^3.0.5", "@types/jest": "^26.0.20", "@types/node": "^18.11.9", "@vue/runtime-dom": "^3.2.47", "@vue/test-utils": "^1.1.3", "@vueuse/nuxt": "9.1.0", "@wellcare/nuxt-module-account": "1.2.5", "@wellcare/nuxt-module-chart": "1.1.26", "@wellcare/nuxt-module-content": "0.0.15", "@wellcare/nuxt-module-data-layer": "0.10.12", "@wellcare/nuxt-module-elastic": "0.4.1", "@wellcare/nuxt-module-media": "0.5.0", "@wellcare/nuxt-module-notion": "^0.0.34-beta2", "@wellcare/nuxt-module-phr": "0.1.51", "@wellcare/vue-component": "1.0.8", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^26.6.3", "base64url": "^3.0.1", "canvas-confetti": "^1.9.3", "codecov": "^3.8.1", "cookie-universal-nuxt": "^2.1.5", "cross-env": "^7.0.3", "cypress": "^10.4.0", "cypress-autorecord": "^3.1.2", "cypress-fail-fast": "^5.0.0", "cypress-fail-on-console-error": "^3.2.1", "dotenv": "^8.2.0", "eslint": "7.21.0", "eslint-config-prettier": "8.1.0", "eslint-loader": "4.0.2", "eslint-plugin-cypress": "2.12.1", "eslint-plugin-jest": "24.2.1", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.7.0", "eslint-plugin-vuetify": "1.1.0", "eslint-webpack-plugin": "3.1.1", "glob": "^8.0.3", "husky": "^5.1.3", "jest": "^26.6.3", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "lint-staged": "^10.5.4", "nuxt": "^2.15.8", "nuxt-socket-io": "2", "nuxt-typed-vuex": "^0.1.22", "nuxt-webpack-optimisations": "^2.2.0", "prettier": "^2.2.1", "rimraf": "^3.0.2", "stylelint": "13.12.0", "stylelint-config-recommended": "4.0.0", "ts-jest": "^26.5.3", "ts-loader": "8.1.0", "typescript": "^4.6.4", "uuid": "^8.3.2", "vue-debounce-decorator": "^1.0.1", "vue-jest": "^3.0.7", "vuex-composition-map-fields": "^1.0.5", "webpack-dev-server": "^4.9.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,vue}": ["eslint --ext .ts,.vue --fix"]}, "resolutions": {"vue": "2.6.14", "vue-server-renderer": "2.6.14", "vue-template-compiler": "2.6.14", "vuetify-loader": "1.7.3", "eslint-plugin-import": "2.26.0", "strip-ansi": "6.0.1", "string-width": "4.2.2", "wrap-ansi": "7.0.0", "@vueuse/core": "10.1.2"}}