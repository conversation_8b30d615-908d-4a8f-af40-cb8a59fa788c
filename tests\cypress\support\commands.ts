/// <reference types="cypress" />
/// <reference path="./index.d.ts" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }
Cypress.Commands.add('loginWellcare', () => {
  cy.session(Cypress.env('wellcareUsername'), () => {
    cy.request({
      method: 'POST',
      url: 'https://api.mhealthvn.com/api/login',
      body: {
        username: Cypress.env('wellcareUsername'),
        password: Cypress.env('wellcarePassword')
      }
    }).then(({ body }) => {
      const { results } = body
      // expect(results.token.length).to.be.greaterThan(1)
      cy.setCookie('token', results.token)
    })
  })
})
