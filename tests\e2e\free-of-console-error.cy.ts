/// <reference types="cypress" />
/// <reference types="../cypress/support" />

import failOnConsoleError, { consoleType } from 'cypress-fail-on-console-error'
import autoRecord from 'cypress-autorecord' // Require the autorecord function

const config = {
  // excludeMessages: [], // accept string or regex
  includeConsoleTypes: [consoleType.ERROR, consoleType.WARN],
  cypressLog: false
}

failOnConsoleError(config)

describe('Free from console errors', function () {
  autoRecord() // Call the autoRecord function at the beginning of your describe block
  // NOTE: Do not use ES6 arrow functions for your describe or it callback. This will cause the recording function to break.
  it(`pass all pages`, function () {
    cy.task('glob', {
      path: './example/pages/**/*',
      option: { mark: true }
    }).then((pageFiles: any) => {
      cy.fixture('routes').then((routes: any) => {
        pageFiles
          .filter(
            (filePath: string) =>
              filePath.substring(filePath.length - 1, filePath.length) !== '/'
          )
          .map((filePath: string) =>
            filePath
              .replace('./example/pages', '')
              .replace('/index.vue', '')
              .replace('_.vue', '')
              .replace('.vue', '')
          )
          .map((route: string) => {
            let replacedRoute = route
            if (routes[route]) replacedRoute = routes[route]
            return replacedRoute
          })
          .forEach((page: string) => {
            cy.loginWellcare()
            cy.visit(page)
          })
      })
    })
  })
})
