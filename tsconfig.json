{"compilerOptions": {"target": "es2018", "module": "esnext", "moduleResolution": "node", "lib": ["esnext", "esnext.asynciterable", "dom"], "outDir": "dist", "rootDir": ".", "baseUrl": ".", "experimentalDecorators": true, "noImplicitAny": false, "declaration": false, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "preserveSymlinks": true, "paths": {"@lib/*": ["./lib/*"], "@type": ["./vuex-composition-map-fields"], "typeRoots": ["@types"]}, "types": ["@nuxt/types", "@nuxtjs/axios", "@nuxtjs/dayjs", "@nuxtjs/i18n", "@nuxtjs/toast", "@nuxtjs/vuetify", "@types/node", "@nuxtjs/composition-api"]}, "include": ["lib/**/*.ts", "lib/*.jsons", "lib/**/*.json", "middleware.ts", "lib/components/internal/order/order-item/use-combo-product.js"], "exclude": ["node_modules", "dist"], "extensions": [".vue"]}